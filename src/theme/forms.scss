.form-container {
    @apply mx-auto min-h-full max-w-md py-6;
}

.form-header {
    @apply mb-6;
}

.form-title {
    @apply mb-8 text-center text-lg font-medium text-gray-900;
}

.checkbox-container {
    @apply mt-2 flex items-start gap-2;
}

.checkbox-label {
    @apply mt-[2px] text-base text-gray-900;
}

.form-fields {
    @apply space-y-4;
}

.form-group {
    @apply mb-4;
}

.form-label {
    @apply mb-1 block text-sm font-normal text-gray-500;
}

.form-control {
    @apply w-full;
}

.required {
    @apply text-red-500;
}

.error-message {
    @apply mt-1 block text-xs text-red-500;
}

.form-actions {
    @apply mt-8 flex justify-between gap-4;
}

.cancel-button {
    @apply flex-1;
}