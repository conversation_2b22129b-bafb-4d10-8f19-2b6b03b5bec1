.app-layout {
	@apply flex h-dvh w-full flex-col overflow-hidden bg-white;
}

.app-layout-dialog {
	@apply flex h-full w-full flex-col overflow-hidden;
}

.app-header {
	@apply flex items-center justify-between border-b border-gray-200 bg-white px-6 py-4;
	@apply h-[74px] shrink-0;
	@apply sticky top-0 z-10;
}

.app-content {
	@apply relative flex-1 overflow-y-auto;
	@apply w-full;
}

.content-wrapper {
	@apply mx-auto w-full flex flex-col max-w-lg px-6 py-6;
}

.app-content::-webkit-scrollbar {
	@apply w-2;
}

.app-content::-webkit-scrollbar-track {
	@apply bg-gray-100;
}

.app-content::-webkit-scrollbar-thumb {
	@apply rounded-full bg-gray-300;
}

.app-content::-webkit-scrollbar-thumb:hover {
	@apply bg-gray-400;
}

.app-footer {
	@apply min-h-[60px] shrink-0;
	@apply sticky bottom-0 z-10;
	@apply border-t border-gray-200 bg-white;
	@apply rounded-bl-lg rounded-br-lg;
	@apply px-6;
	@apply flex items-center justify-between;
}

.footer-wrapper {
	@apply flex flex-1 flex-row items-center justify-end gap-4;
	@apply w-full;

	p-button,
	button {
		@apply min-w-[200px];
	}
}