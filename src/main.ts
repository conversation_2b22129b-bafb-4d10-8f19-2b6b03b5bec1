import { provideHttpClient, withInterceptors } from "@angular/common/http";
import { bootstrapApplication } from "@angular/platform-browser";
import { provideAnimationsAsync } from "@angular/platform-browser/animations/async";
import {
	PreloadAllModules,
	RouteReuseStrategy,
	provideRouter,
	withPreloading,
} from "@angular/router";
import {
	IonicRouteStrategy,
	provideIonicAngular,
} from "@ionic/angular/standalone";
import { definePreset } from "@primeng/themes";
import Aura from "@primeng/themes/aura";
import { ConfirmationService, MessageService } from "primeng/api";
import { providePrimeNG } from "primeng/config";
import { DialogService } from "primeng/dynamicdialog";
import { AppComponent } from "./app/app.component";
import { routes } from "./app/app.routes";
import { authInterceptor } from "./app/core/interceptors/auth.interceptor";
import { loanProviders } from "./app/core/providers/loan.providers";

const MyPreset = definePreset(Aura, {
	semantic: {
		primary: {
			50: "#E3F4FF",
			100: "#B8E1FF",
			200: "#8DCEFF",
			300: "#62BAFF",
			400: "#38A7FF",
			500: "#0192FB",
			600: "#0178D3",
			700: "#015FAA",
			800: "#004681",
			900: "#002F59",
			950: "#001A33",
		},
	},
});

bootstrapApplication(AppComponent, {
	providers: [
		{ provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
		provideIonicAngular(),
		provideRouter(routes, withPreloading(PreloadAllModules)),
		provideHttpClient(withInterceptors([authInterceptor])),
		DialogService,
		MessageService,
		ConfirmationService,
		...loanProviders,
		provideAnimationsAsync(),
		providePrimeNG({
			theme: {
				preset: MyPreset,
				options: {
					darkModeSelector: false,
				},
			},
			ripple: true,
			zIndex: {
				modal: 9000, // dialog, sidebar
				overlay: 9500, // dropdown, overlaypanel
				menu: 9500, // overlay menus
				tooltip: 11000, // tooltip
			},
		}),
	],
});
