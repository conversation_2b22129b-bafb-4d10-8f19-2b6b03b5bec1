/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */
@tailwind base;
@tailwind components;
@tailwind utilities;

@import "./theme/variables.scss";
@import "./theme/layout.scss";
@import "./theme/typography.scss";
@import "./theme/primeng.scss";
@import "./theme/forms.scss";

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
// @import '@ionic/angular/css/palettes/dark.system.css';

/* Base styles */
:root {

	html,
	body {
		width: 100%;
		height: 100%;
		margin: 0;
		padding: 0;
		overflow: hidden;
	}

	html,
	body {
		font-family: "Kanit", sans-serif;
		font-style: normal;
		font-weight: 400;
		-webkit-text-size-adjust: 100%;
	}

	* {
		font-family: "Kanit", sans-serif;
	}

	*,
	*::before,
	*::after {
		box-sizing: border-box;
	}

	/* ทำให้ router-outlet แสดงผลเต็มหน้าจอ */
	router-outlet {
		display: contents;
	}

	/* ทำให้คอมโพเนนต์ที่โหลดผ่าน router-outlet แสดงผลเต็มหน้าจอ */
	router-outlet+* {
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
	}
}

/**
 * Workflow Modal Styles
 * -----------------------------------------------------
 */

/* Main workflow modal */
ion-modal {
	--width: 90%;
	--height: 80%;
	--border-radius: 16px;
	--box-shadow:
		0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

	&.modal-blur-backdrop {
		--backdrop-opacity: 0.65 !important;
	}

	&::part(backdrop) {
		z-index: 10;
		background: var(--backdrop-background);
		transition: all 0.3s ease-in-out;
	}

	.modal-wrapper {
		z-index: 11;
		box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
	}
}

.modal-blur-backdrop {
	--backdrop-opacity: 0.65;
	--backdrop-background: rgba(17, 24, 39, var(--backdrop-opacity));

	&::part(backdrop) {
		backdrop-filter: blur(4px);
		-webkit-backdrop-filter: blur(4px);
		background: rgba(17, 24, 39, var(--backdrop-opacity));
	}
}

/* Main workflow modal - fullscreen */
.workflow-modal,
.fullscreen-modal {
	--width: 100%;
	--height: 100%;
	--border-radius: 0;
	--box-shadow: none;

	.modal-wrapper {
		display: flex;
		flex-direction: column;
		height: 100%;
	}

	ion-content {
		--background: #ffffff;
	}
}

/* Subflow modal */
.subflow-modal {
	/* Default for mobile - fullscreen without border radius */
	--width: 100%;
	--height: 100%;

	/* Tablet and desktop */
	@media (min-width: 768px) {
		--width: 70%;
		--height: 90%;
		--border-radius: 16px;
	}
}

/* Dialog global styles */
.global-dialog-modal {
	--width: 520px;
	--height: 90%;
	--border-radius: 16px;
}

.installment-plan-modal {
	--width: 860px;
	--min-height: 90%;
	--border-radius: 16px;
}

/* Status notification modal */
.status-notification-modal {
	--width: 520px;
	--height: 90%;
	--border-radius: 16px;

	/* Override any fullscreen styles */
	&.workflow-modal {
		--width: 520px !important;
		--height: 90% !important;
		--border-radius: 16px !important;
	}
}

/* Error modal */
.error-modal {
	--width: 70%;
	--height: auto;
	--border-radius: 16px;
}

/* Circular Progress Loading modal */
.circular-progress-loading-modal {
	--width: 520px;
	--height: 90%;
	--border-radius: 16px;

	/* Override any fullscreen styles */
	&.workflow-modal {
		--width: 520px !important;
		--height: 90% !important;
		--border-radius: 16px !important;
	}
}

/**
 * Overlay Modals For Circle Loading & Status Notification
 * -----------------------------------------------------
 */
.overlay-modal {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	/* สูงกว่ากล้อง */
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: rgba(255, 255, 255, 0.85);
	/* หรือ transparent ถ้า modal มีพื้นหลังเอง */
}