import dayjs from "dayjs";

const months = [
	"มกราคม",
	"กุมภาพันธ์",
	"มีนาคม",
	"เมษายน",
	"พฤษภาคม",
	"มิถุนายน",
	"กรกฎาคม",
	"สิงหาคม",
	"กันยายน",
	"ตุลาคม",
	"พฤศจิกายน",
	"ธันวาคม",
];

const shortMonths = [
	"ม.ค.",
	"ก.พ.",
	"มี.ค.",
	"เม.ย.",
	"พ.ค.",
	"มิ.ย.",
	"ก.ค.",
	"ส.ค.",
	"ก.ย.",
	"ต.ค.",
	"พ.ย.",
	"ธ.ค.",
];

export class DateUtils {
	static getMonthOptions(incould_0: boolean = false) {
		const monthOptions: { label: string; value: string | number }[] = [];
		if (incould_0) {
			monthOptions.push({
				value: 0,
				label: "-",
			});
		}
		for (let month = 1; month <= 12; month++) {
			monthOptions.push({
				value: month,
				label: months[month - 1].toString(),
			});
		}
		return monthOptions;
	}

	static getShortMonth(month: string | number) {
		if (typeof month === "string") {
			month = parseInt(month);
		}
		return shortMonths[month - 1];
	}

	static getDateOptions(
		include_0: boolean = false,
		month?: number,
		year?: number,
	) {
		const dateOptions: { label: string; value: string | number }[] = [];
		if (include_0) {
			dateOptions.push({
				value: 0,
				label: "-",
			});
		}

		const daysInMonth = month && year ? new Date(year, month, 0).getDate() : 31;

		for (let date = 1; date <= daysInMonth; date++) {
			dateOptions.push({
				value: date,
				label: date.toString(),
			});
		}

		return dateOptions;
	}

	static getYearOptions(max: number = 0): number[] {
		const currentYear = new Date()
			.toLocaleString("th", { year: "numeric" })
			.split(" ")[1];
		const startYear = Number(currentYear) + max;
		const endYear = startYear - 100;
		const yearOptions: number[] = [];

		for (let year = startYear; year >= endYear; year--) {
			yearOptions.push(year);
		}
		return yearOptions;
	}

	static getFullDateBE(date: string) {
		const parsedDate = dayjs(date, "YYYY-MM-DD");
		if (!parsedDate.isValid()) {
			throw new Error("Invalid date format. Expected format: YYYY-MM-DD.");
		}
		// Check if date contains '00-00' for day or month
		if (date.includes("00-00")) {
			const year = dayjs(date).year() + 543; // Convert to Buddhist Era year
			return `${year}`;
		}
		const day = parsedDate.date();
		const month = months[parsedDate.month()]; // Get month index (0-11)

		const year = parsedDate.year() + 543;
		return `${day} ${month} ${year}`;
	}

	static convertIsoDateToFullDateBE(date: string) {
		const parsedDate = dayjs(date).locale("th");
		if (!parsedDate.isValid()) {
			throw new Error(
				"Invalid date format. Expected format: YYYY-MM-DD or ISO 8601.",
			);
		}

		const day = parsedDate.date();
		const month = months[parsedDate.month()];
		const year = parsedDate.year() + 543;

		return `${day} ${month} ${year}`;
	}

	static generateDate(day: number, month: number, year: number) {
		return [
			year - 543,
			month.toString().padStart(2, "0"),
			day.toString().padStart(2, "0"),
		]
			.filter(Boolean)
			.join("-");
	}

	static getDateParts(date_input: string) {
		const dateValue = new Date(date_input);
		if (isNaN(dateValue.getTime())) {
			return null;
		}
		return {
			day: dateValue.getDate(), // Extract day
			month: dateValue.getMonth() + 1, // Extract month (0-indexed, so add 1)
			year: dateValue.getFullYear() + 543, // Extract year
		};
	}
}
