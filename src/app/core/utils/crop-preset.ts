export interface CropConfigurationPreset {
	name: "extra-low" | "low" | "medium" | "high" | "center" | "final-id-card";
	config: CropConfiguration;
}

export interface CropConfiguration {
	xOffset: number; // สัดส่วนระยะทาง x จากตรงกลาง (ค่าลบคือซ้าย, ค่าบวกคือขวา)
	yOffset: number; // สัดส่วนระยะทาง y จากตรงกลาง (ค่าลบคือบน, ค่าบวกคือล่าง)
	width: number; // สัดส่วนความกว้างที่ต้องการ crop
	height: number; // สัดส่วนความสูงที่ต้องการ crop
}

export const CropConfigurationPresets: CropConfigurationPreset[] = [
	{
		name: "extra-low",
		config: {
			// extra low
			xOffset: 0.1, // ไปทางขวา 10% จากจุดศูนย์กลาง
			yOffset: -0.19, // ไปทางบน 19% จากจุดศูนย์กลาง
			width: 0.45, // ความกว้างของ crop = 45% ของภาพ
			height: 0.12, // ความสูงของ crop = 12% ของภาพ
		},
	},
	{
		name: "low",
		config: {
			// low
			xOffset: 0.05, // ไปทางขวา 10% จากจุดศูนย์กลาง
			yOffset: -0.19, // ไปทางบน 19% จากจุดศูนย์กลาง
			width: 0.65, // ความกว้างของ crop = 65% ของภาพ
			height: 0.12, // ความสูงของ crop = 12% ของภาพ
		},
	},
	{
		name: "medium",
		config: {
			// medium
			xOffset: 0.05, // ตัดตรงกลาง 0% จากจุดศูนย์กลาง
			yOffset: -0.08, // ไปทางบน 15% จากจุดศูนย์กลาง
			width: 0.65, // ความกว้างของ crop = 65% ของภาพ
			height: 0.35, // ความสูงของ crop = 35% ของภาพ
		},
	},
	{
		name: "high",
		config: {
			// high
			xOffset: 0, //  0% จากจุดศูนย์กลาง
			yOffset: 0, //  0% จากจุดศูนย์กลาง
			width: 0.77, // ความกว้างของ crop = 45% ของภาพ
			height: 0.5, // ความสูงของ crop = 12% ของภาพ
		},
	},
	{
		name: "center",
		config: {
			// center
			xOffset: 0, // ตัดตรงกลาง 0% จากจุดศูนย์กลาง
			yOffset: 0.05, // ไปทางบน 10% จากจุดศูนย์กลาง
			width: 0.45, // ความกว้างของ crop = 45% ของภาพ
			height: 0.2, // ความสูงของ crop = 35% ของภาพ
		},
	},
	{
		name: "final-id-card",
		config: {
			// center
			xOffset: 0, // ตัดตรงกลาง 0% จากจุดศูนย์กลาง
			yOffset: 0, // ไปทางบน 10% จากจุดศูนย์กลาง
			width: 0.9, // ความกว้างของ crop = 45% ของภาพ
			height: 0.65, // ความสูงของ crop = 12% ของภาพ
		},
	},
];
