export const CHARSET_NAMES: Record<string, string> = {
	"Thai characters": "ตัวอักษรไทย",
	"English letters": "ตัวอักษรภาษาอังกฤษ",
	"numbers": "ตัวเลข",
	"spaces": "เว้นวรรค",
	"dashes": "ขีด (-)",
	"dots": "จุด (.)",
	"commas": "จุลภาค (,)",
	"slashes": "เครื่องหมายทับ (/)",
	"colons": "เครื่องหมายโคลอน (:)",
	"at signs": "เครื่องหมายแอด (@)",
	"underscores": "ขีดล่าง (_)",
	"parentheses": "วงเล็บ (())",
};

export class MessageErrorUtils {
	static argumentValidationErrorMessage(errorMessage: string): string {
		const match = errorMessage.match(/\(([^)]+)\)/); // Extract text inside parentheses
		if (!match) return errorMessage; // Return original if no match

		const cleanedText = match[1].replace(/\band\b/g, "").trim(); // Remove "and" and extra spaces
		const allowedChars = cleanedText.split(",").map((char) => char.trim()); // Split by ","
		const translatedChars = allowedChars.map(
			(char) => CHARSET_NAMES[char] || char,
		); // Translate

		return `มีอักขระที่ไม่ถูกต้อง (อนุญาตให้ใช้เฉพาะ ${translatedChars.join(", ")})`;
	}
}
