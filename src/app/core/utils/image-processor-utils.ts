import { CropConfiguration } from "./crop-preset";

export interface ImageProcessingConfig {
	mimeType: string;
	quality: number;
	padding: number;
}

export interface ImageCropArea {
	top: number;
	right: number;
	bottom: number;
	left: number;
}

export interface ImageDimensions {
	width: number;
	height: number;
}

export class ImageProcessorUtils {
	private static readonly DEFAULT_PROCESSING_CONFIG: ImageProcessingConfig = {
		mimeType: "image/jpeg",
		quality: 0.95,
		padding: 20,
	};

	//   public static async extractDocumentArea(
	//     base64Image: string,
	//     detection: YoloDetectionResult,
	//     originalDimensions: ImageDimensions,
	//     config: Partial<ImageProcessingConfig> = {},
	//   ): Promise<string> {
	//     return new Promise((resolve, reject) => {
	//       const img = new Image();
	//       img.onload = () => {
	//         const canvas = document.createElement('canvas');
	//         const ctx = canvas.getContext('2d');

	//         // คำนวณ scaling factors
	//         const scaleX = img.width / originalDimensions.width;
	//         const scaleY = img.height / originalDimensions.height;

	//         // ปรับค่าพิกัดตามสัดส่วนและเพิ่ม padding
	//         const padding = config.padding ?? 0;
	//         const x = Math.max(0, detection.box.x1 * scaleX - padding * scaleX);
	//         const y = Math.max(0, detection.box.y1 * scaleY - padding * scaleY);
	//         const width = Math.min(
	//           img.width - x,
	//           (detection.box.x2 - detection.box.x1) * scaleX + 2 * padding * scaleX,
	//         );
	//         const height = Math.min(
	//           img.height - y,
	//           (detection.box.y2 - detection.box.y1) * scaleY + 2 * padding * scaleY,
	//         );

	//         canvas.width = width;
	//         canvas.height = height;

	//         if (ctx) {
	//           ctx.drawImage(img, x, y, width, height, 0, 0, width, height);

	//           const croppedBase64 = canvas.toDataURL('image/jpeg', 0.95).split(',')[1];
	//           resolve(croppedBase64);
	//         } else {
	//           reject(new Error('Failed to get canvas context'));
	//         }
	//       };

	//       img.onerror = () => reject(new Error('Failed to load image'));
	//       img.src = base64Image;
	//     });
	//   }

	static async extractImageArea(
		base64Image: string,
		cropArea: ImageCropArea,
		config: Partial<ImageProcessingConfig> = {},
	): Promise<string> {
		const processingConfig = {
			...ImageProcessorUtils.DEFAULT_PROCESSING_CONFIG,
			...config,
		};

		try {
			// Load image
			const image = new Image();
			image.src = base64Image;
			await image.decode();

			const { width, height } = image;

			// Calculate extraction area
			const extractArea = {
				left: Math.floor(cropArea.left * width),
				top: Math.floor(cropArea.top * height),
				width: Math.min(
					width - Math.floor((cropArea.left + cropArea.right) * width),
					width,
				),
				height: Math.min(
					height - Math.floor((cropArea.top + cropArea.bottom) * height),
					height,
				),
			};

			// Setup canvas
			const canvas = document.createElement("canvas");
			canvas.width = extractArea.width;
			canvas.height = extractArea.height;

			const context = canvas.getContext("2d", { alpha: false });
			if (!context) {
				throw new Error("Canvas context creation failed");
			}

			// Extract and process image area
			context.drawImage(
				image,
				extractArea.left,
				extractArea.top,
				extractArea.width,
				extractArea.height,
				0,
				0,
				extractArea.width,
				extractArea.height,
			);

			return canvas.toDataURL(
				processingConfig.mimeType,
				processingConfig.quality,
			);
		} catch (error: any) {
			throw new Error(`Image area extraction failed: ${error.message}`);
		}
	}

	public static async base64ToImageBitmap(
		base64Image: string,
	): Promise<ImageBitmap> {
		const base64Data = base64Image.split(",")[1] || base64Image;
		const blob = new Blob(
			[Uint8Array.from(atob(base64Data), (c) => c.charCodeAt(0))],
			{
				type: "image/jpeg",
			},
		);
		return createImageBitmap(blob);
	}

	public static base64ToImageFile(base64Image: string): File | null {
		// Check if the base64Image is valid
		if (!base64Image || !base64Image.startsWith("data:image/")) {
			console.error("Invalid base64 image string");
			return null;
		}

		const matches = base64Image.match(
			/^data:image\/(png|jpg|jpeg);base64,(.+)$/,
		);
		if (!matches) {
			console.error("Base64 image format is not recognized");
			return null;
		}

		const imageType = matches[1];
		const base64Data = matches[2];
		const binaryData = atob(base64Data);
		const byteArray = new Uint8Array(binaryData.length);

		for (let i = 0; i < binaryData.length; i++) {
			byteArray[i] = binaryData.charCodeAt(i);
		}

		const blob = new Blob([byteArray], { type: `image/${imageType}` });
		const imageFile = new File([blob], `${Date.now()}.${imageType}`, {
			type: `image/${imageType}`,
		});
		return imageFile;
	}

	public static async resizeImage(
		imageDataUrl: string,
		scaleFactor: number,
	): Promise<string> {
		const imageBitmap = await this.base64ToImageBitmap(imageDataUrl);
		const canvas = this.createCanvas(
			Math.floor(imageBitmap.width * scaleFactor),
			Math.floor(imageBitmap.height * scaleFactor),
		);
		const context = this.getCanvasContext(canvas);
		context.imageSmoothingEnabled = false; // Disable image smoothing for faster performance
		context.drawImage(imageBitmap, 0, 0, canvas.width, canvas.height);
		return canvas.toDataURL();
	}

	private static createCanvas(
		width: number,
		height: number,
	): HTMLCanvasElement {
		const canvas = document.createElement("canvas");
		canvas.width = width;
		canvas.height = height;
		return canvas;
	}

	private static getCanvasContext(
		canvas: HTMLCanvasElement,
	): CanvasRenderingContext2D {
		const context = canvas.getContext("2d", { alpha: false });
		if (!context) {
			throw new Error("Failed to get canvas context");
		}
		return context;
	}

	public static async loadImage(src: string): Promise<HTMLImageElement> {
		const image = new Image();
		image.src = src;
		await image.decode();
		return image;
	}

	// public static async cropImageWithDimensions(
	//     imageDataUrl: string,
	//     cropDimensions: CropPresetDimensions
	// ): Promise<string> {
	//     const image = await this.loadImage(imageDataUrl);
	//     const { width, height } = image;

	//     const cropBox = {
	//         left: Math.floor(cropDimensions.left * width),
	//         top: Math.floor(cropDimensions.top * height),
	//         width: Math.min(width - Math.floor((cropDimensions.left + cropDimensions.right) * width), width),
	//         height: Math.min(height - Math.floor((cropDimensions.top + cropDimensions.bottom) * height), height),
	//     };

	//     const canvas = this.createCanvas(cropBox.width, cropBox.height);
	//     const context = this.getCanvasContext(canvas);

	//     context.drawImage(
	//         image,
	//         cropBox.left,
	//         cropBox.top,
	//         cropBox.width,
	//         cropBox.height,
	//         0,
	//         0,
	//         cropBox.width,
	//         cropBox.height
	//     );

	//     return canvas.toDataURL();
	// }

	public static setImageDataUrlToCanvas(
		imageDataUrl: string,
		canvas: HTMLCanvasElement,
	) {
		return new Promise((resolve, reject) => {
			// ดึง <canvas> โดยใช้ canvasId
			if (!canvas) {
				reject(new Error(`Canvas with id "${canvas}" not found.`));
				return;
			}

			const ctx = canvas.getContext("2d");
			if (!ctx) {
				reject(new Error("Failed to get 2D context."));
				return;
			}

			// สร้าง Image และโหลด ImageDataUrl
			const img = new Image();
			img.onload = function () {
				// ปรับขนาด canvas ให้เท่ากับขนาดภาพ (ถ้าจำเป็น)
				canvas.width = img.width;
				canvas.height = img.height;

				// วาดภาพลงบน canvas
				ctx.drawImage(img, 0, 0);
				resolve(canvas); // ส่งคืน canvas เมื่อวาดสำเร็จ
			};

			img.onerror = function () {
				reject(new Error("Failed to load image data URL."));
			};

			img.src = imageDataUrl;
		});
	}

	public static async cropImageByRatio(
		imageSrc: string,
		cropRatio: { x: number; y: number; width: number; height: number },
	): Promise<string> {
		return new Promise((resolve, reject) => {
			const image = new Image();
			image.src = imageSrc;

			image.onload = () => {
				const originalWidth = image.width;
				const originalHeight = image.height;

				// คำนวณตำแหน่งและขนาดที่ต้องการ crop จากอัตราส่วน
				const x = cropRatio.x * originalWidth;
				const y = cropRatio.y * originalHeight;
				const width = cropRatio.width * originalWidth;
				const height = cropRatio.height * originalHeight;

				const canvas = document.createElement("canvas");
				const context = canvas.getContext("2d");

				if (!context) {
					reject("Failed to get canvas context");
					return;
				}

				// ตั้งค่าขนาด canvas
				canvas.width = width;
				canvas.height = height;

				// วาดส่วนที่ต้องการ crop ลงใน canvas
				context.drawImage(
					image,
					x, // ตำแหน่ง x ของพื้นที่ crop (คำนวณจากอัตราส่วน)
					y, // ตำแหน่ง y ของพื้นที่ crop (คำนวณจากอัตราส่วน)
					width, // ความกว้างของพื้นที่ crop
					height, // ความสูงของพื้นที่ crop
					0, // ตำแหน่ง x บน canvas
					0, // ตำแหน่ง y บน canvas
					width, // ความกว้างบน canvas
					height, // ความสูงบน canvas
				);

				// แปลง canvas เป็น Data URL
				resolve(canvas.toDataURL("image/jpeg"));
			};

			image.onerror = (error) => {
				reject(`Failed to load image: ${error}`);
			};
		});
	}

	public static async cropFromCenterWithConfig(
		imageSrc: string,
		config: CropConfiguration,
	): Promise<string> {
		return new Promise((resolve, reject) => {
			const image = new Image();
			image.src = imageSrc;

			image.onload = () => {
				const imageWidth = image.width;
				const imageHeight = image.height;

				const { xOffset, yOffset, width, height } = config;

				// คำนวณตำแหน่งและขนาดสำหรับ crop
				const centerX = imageWidth / 2;
				const centerY = imageHeight / 2;

				const cropWidth = width * imageWidth;
				const cropHeight = height * imageHeight;

				const startX = centerX - cropWidth / 2 + xOffset * imageWidth;
				const startY = centerY - cropHeight / 2 + yOffset * imageHeight;

				// สร้าง canvas และ crop
				const canvas = document.createElement("canvas");
				const ctx = canvas.getContext("2d");

				if (!ctx) {
					reject(new Error("Failed to get 2D context from canvas"));
					return;
				}

				// ตั้งขนาด canvas ตามขนาดที่ต้องการ crop
				canvas.width = Math.round(cropWidth);
				canvas.height = Math.round(cropHeight);

				// วาดเฉพาะส่วนที่ crop ลงใน canvas
				ctx.drawImage(
					image,
					Math.max(0, Math.round(startX)), // พิกัดเริ่มต้น x ในภาพต้นฉบับ
					Math.max(0, Math.round(startY)), // พิกัดเริ่มต้น y ในภาพต้นฉบับ
					Math.round(cropWidth), // ความกว้างในภาพต้นฉบับ
					Math.round(cropHeight), // ความสูงในภาพต้นฉบับ
					0,
					0, // พิกัดเริ่มต้นใน canvas
					canvas.width, // ความกว้างใน canvas
					canvas.height, // ความสูงใน canvas
				);

				// แปลง canvas เป็น Data URL
				resolve(canvas.toDataURL("image/jpeg"));
			};

			image.onerror = () => reject(new Error("Failed to load image"));
		});
	}

	public static resizeImageToCanvas(
		base64Image: string,
		width: number,
		height: number,
	): Promise<HTMLCanvasElement> {
		return new Promise((resolve, reject) => {
			const img = new Image();
			img.onload = () => {
				// สร้าง canvas
				const canvas = document.createElement("canvas");
				canvas.width = width;
				canvas.height = height;

				// วาดรูปที่ resize ลงใน canvas
				const ctx = canvas.getContext("2d");
				if (ctx) {
					ctx.drawImage(img, 0, 0, width, height);
					resolve(canvas);
				} else {
					reject(new Error("Failed to get 2D context from canvas."));
				}
			};
			img.onerror = (error) => {
				reject(new Error("Failed to load image."));
			};

			// ตั้งค่ารูปจาก base64
			img.src = base64Image;
		});
	}

	/**
	 * Optimizes an image by resizing it based on a given scale factor.
	 *
	 * @param imageDataUrl - The data URL of the image to be optimized.
	 * @param scaleFactor - The factor by which to scale the image dimensions.
	 * @returns A promise that resolves to the resized HTMLImageElement, or null if an error occurs.
	 *
	 */
	public static async optimizeImage(
		imageDataUrl: string,
		scaleFactor: number,
	): Promise<HTMLImageElement | null> {
		try {
			if (!imageDataUrl) {
				console.warn("Image data URL is null or undefined");
				return null;
			}

			// Create a canvas for resizing
			const imageBitmap =
				await ImageProcessorUtils.base64ToImageBitmap(imageDataUrl);
			const width = Math.floor(imageBitmap.width * scaleFactor);
			const height = Math.floor(imageBitmap.height * scaleFactor);

			const canvas = document.createElement("canvas");
			canvas.width = width;
			canvas.height = height;

			const context = canvas.getContext("2d");
			if (!context) {
				console.error("Failed to get 2D context");
				return null;
			}

			// Optimal rendering settings
			context.imageSmoothingEnabled = true;
			context.imageSmoothingQuality = "medium";
			context.drawImage(imageBitmap, 0, 0, width, height);

			// Load the resized image directly from the canvas
			const resizedDataUrl = canvas.toDataURL();
			const image = new Image();
			image.src = resizedDataUrl;
			await image.decode();

			return image;
		} catch (error) {
			console.error("Error during image processing:", error);
			return null;
		}
	}
}
