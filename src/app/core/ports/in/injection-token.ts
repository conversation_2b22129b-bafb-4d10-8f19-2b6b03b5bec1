import { InjectionToken } from "@angular/core";
import { AddressUseCasePort } from "./address-usecase.port";
import { DocumentUseCasePort } from "./document.port";
import { LoanUseCasePort } from "./loan-usecase.port";
import { ProductUseCasePort } from "./product.port";
import { TermUseCasePort } from "./term.port";
import { UserUseCasePort } from "./user.port";
import { WorkingUseCasePort } from "./working.port";
import { ContactUseCasePort } from "./contact.port";

export const USER_USE_CASE = new InjectionToken<UserUseCasePort>("UserUseCase");
export const LOAN_USE_CASE = new InjectionToken<LoanUseCasePort>("LoanUseCase");
export const ADDRESS_USE_CASE = new InjectionToken<AddressUseCasePort>(
	"AddressUseCase",
);
export const WORKING_USE_CASE = new InjectionToken<WorkingUseCasePort>(
	"WorkingUseCase",
);
export const PRODUCT_USE_CASE = new InjectionToken<ProductUseCasePort>(
	"ProductUseCase",
);
export const DOCUMENT_USE_CASE = new InjectionToken<DocumentUseCasePort>(
	"DocumentUseCase",
);
export const TERM_USE_CASE = new InjectionToken<TermUseCasePort>("TermUseCase");
export const CONTACT_USE_CASE = new InjectionToken<ContactUseCasePort>(
	"ContactUseCase",
);
