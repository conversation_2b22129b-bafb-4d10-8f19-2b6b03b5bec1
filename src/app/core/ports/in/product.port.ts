import { ConfirmProductRequestDto } from "@domain/dto/products/confirm-product.dto";
import {
	GetProductListRequestDto,
	GetProductListResponseDto,
} from "@domain/dto/products/get-product-list.dto";
import {
	GetProductRequestDto,
	GetProductResponseDto,
} from "@domain/dto/products/get-product.dto";
import { SetProductRequestDto } from "@domain/dto/products/set-product.dto";
import { ApiResponse } from "@domain/models/api-response.model";

export interface ProductUseCasePort {
	/**
	 * Get product list
	 * @param request The request containing transaction ID and product type
	 * @returns Promise of ApiResponse containing an array of products
	 */
	getProductList(
		request: GetProductListRequestDto,
	): Promise<ApiResponse<GetProductListResponseDto[]>>;

	/**
	 * Set product
	 * @param request The request containing transaction ID and product information
	 * @returns Promise of ApiResponse containing boolean data
	 */
	setProduct(request: SetProductRequestDto): Promise<ApiResponse<boolean>>;

	/**
	 * Get product
	 * @param request The request containing transaction ID and promotion code
	 * @returns Promise of ApiResponse containing product information
	 */
	getProduct(
		request: GetProductRequestDto,
	): Promise<ApiResponse<GetProductResponseDto>>;

	/**
	 * Confirm product
	 * @param request The request containing transaction ID and down payment and period
	 * @returns Promise of ApiResponse containing boolean data
	 */
	confirmProduct(request: ConfirmProductRequestDto): Promise<ApiResponse<any>>;
}
