import {
	GetOccupationGroupRequestDto,
	GetOccupationGroupResponseDto,
} from "@domain/dto/working/get-occupation-group.dto";
import {
	GetOccupationRequestDto,
	GetOccupationResponseDto,
} from "@domain/dto/working/get-occupation.dto";
import {
	GetWorkingInfoRequestDto,
	GetWorkingInfoResponseDto,
} from "@domain/dto/working/get-working-info.dto";
import {
	UpdateWorkingInfoRequestDto,
	UpdateWorkingInfoResponseDto,
} from "@domain/dto/working/update-working-info.dto";
import { ApiResponse } from "@domain/models/api-response.model";

/**
 * Input port for getting user information
 * This interface defines how the UI can request user information
 */
export interface WorkingUseCasePort {
	/**
	 * Get working information
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing working information
	 */
	getWorkingInfo(
		request: GetWorkingInfoRequestDto,
	): Promise<ApiResponse<GetWorkingInfoResponseDto>>;

	/**
	 * Update working information
	 * @param request The request containing transaction ID and working information
	 * @returns Promise of ApiResponse containing updated working information
	 */
	updateWorkingInfo(
		request: UpdateWorkingInfoRequestDto,
	): Promise<ApiResponse<UpdateWorkingInfoResponseDto>>;

	/**
	 * Get occupation groups
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing an array of occupation groups
	 */
	getOccupationGroups(
		request: GetOccupationGroupRequestDto,
	): Promise<ApiResponse<GetOccupationGroupResponseDto[]>>;

	/**
	 * Get occupations
	 * @param request The request containing transaction ID and occupation group code
	 * @returns Promise of ApiResponse containing an array of occupations
	 */
	getOccupations(
		request: GetOccupationRequestDto,
	): Promise<ApiResponse<GetOccupationResponseDto[]>>;
}
