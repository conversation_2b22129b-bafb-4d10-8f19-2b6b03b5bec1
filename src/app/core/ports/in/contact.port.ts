import {
	GetContactRequestDto,
	GetContactResponseDto,
} from "@domain/dto/contact/get-contact.dto";
import {
	SendOtpRequestDto,
	SendOtpResponseDto,
} from "@domain/dto/contact/send-otp.dto";
import { UpdateContactRequestDto } from "@domain/dto/contact/update-contact.dto";
import {
	VerifyOtpRequestDto
} from "@domain/dto/contact/verify-otp.dto";
import { ApiResponse } from "@domain/models/api-response.model";

/**
 * Input port for getting user information
 * This interface defines how the UI can request user information
 */
export interface ContactUseCasePort {
	/**
	 * Update contact
	 * @param request The request containing transaction ID and contact information
	 * @returns Promise of ApiResponse containing boolean data
	 */
	updateContact(
		request: UpdateContactRequestDto,
	): Promise<ApiResponse<boolean>>;

	/**
	 * Get contact
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing contact information
	 */
	getContact(
		request: GetContactRequestDto,
	): Promise<ApiResponse<GetContactResponseDto>>;

	/**
	 * Send OTP
	 * @param request The request containing transaction ID and recipient type and OTP type
	 * @returns Promise of ApiResponse containing OTP response
	 */
	sendOtp(request: SendOtpRequestDto): Promise<ApiResponse<SendOtpResponseDto>>;

	/**
	 * Verify OTP
	 * @param request The request containing transaction ID and recipient type and OTP type and OTP
	 * @returns Promise of ApiResponse containing boolean data
	 */
	verifyOtp(
		request: VerifyOtpRequestDto,
	): Promise<ApiResponse<boolean>>;
}
