import {
	GetUploadDocsRequestDto,
	GetUploadDocsResponseDto,
} from "@domain/dto/document/get-upload-docs.dto";
import {
	UploadDocRequestDto,
	UploadDocResponseDto,
} from "@domain/dto/document/upload-doc.dto";
import { ApiResponse } from "@domain/models/api-response.model";

export interface DocumentUseCasePort {
	/**
	 * Get upload documents
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing upload documents
	 */
	getUploadDocuments(
		request: GetUploadDocsRequestDto,
	): Promise<ApiResponse<GetUploadDocsResponseDto>>;

	/**
	 * Upload document
	 * @param request The request containing transaction ID and file
	 * @returns Promise of ApiResponse containing upload document response
	 */
	uploadDocument(
		request: UploadDocRequestDto,
	): Promise<ApiResponse<UploadDocResponseDto>>;
}
