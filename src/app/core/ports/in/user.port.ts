import { TransactionIdDto } from "@domain/dto/transaction.dto";
import { ApiResponse } from "@domain/models/api-response.model";

/**
 * Input port for getting user information
 * This interface defines how the UI can request user information
 */
export interface UserUseCasePort {
	/**
	 * Sign in anonymously
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing authentication data
	 */
	signInAnonymous(
		request: TransactionIdDto,
	): Promise<ApiResponse<Record<string, unknown>>>;

	/**
	 * Refresh token
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing refreshed token data
	 */
	refreshToken(
		request: TransactionIdDto,
	): Promise<ApiResponse<Record<string, unknown>>>;
}
