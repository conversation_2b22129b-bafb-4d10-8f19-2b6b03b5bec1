import { InjectionToken } from "@angular/core";
import { AddressRepositoryPort } from "./address-repository.port";
import { LoanRepositoryPort } from "./loan-repository.port";
import { UserRepositoryPort } from "./user-repository.port";
import { WorkingRepositoryPort } from "./working-repository.port";
import { ProductRepositoryPort } from "./product-repository.port";
import { DocumentRepositoryPort } from "./document-repository.port";
import { TermRepositoryPort } from "./term-repository.port";
import { ContactRepositoryPort } from "./contact-repository.port";

/**
 * Injection tokens for repositories
 * These tokens are used to inject the repository implementations
 */
export const LOAN_REPOSITORY = new InjectionToken<LoanRepositoryPort>(
	"LoanRepository",
);
export const USER_REPOSITORY = new InjectionToken<UserRepositoryPort>(
	"UserRepository",
);
export const ADDRESS_REPOSITORY = new InjectionToken<AddressRepositoryPort>(
	"AddressRepository",
);
export const WORKING_REPOSITORY = new InjectionToken<WorkingRepositoryPort>(
	"WorkingRepository",
);
export const PRODUCT_REPOSITORY = new InjectionToken<ProductRepositoryPort>(
	"ProductRepository",
);
export const DOCUMENT_REPOSITORY = new InjectionToken<DocumentRepositoryPort>(
	"DocumentRepository",
);
export const TERM_REPOSITORY = new InjectionToken<TermRepositoryPort>(
	"TermRepository",
);
export const CONTACT_REPOSITORY = new InjectionToken<ContactRepositoryPort>(
	"ContactRepository",
);
