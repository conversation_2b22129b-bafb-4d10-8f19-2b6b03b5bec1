import { GetLoanCategoriesResponseDto } from "@domain/dto/loan/get-loan-categories.dto";
import {
	GetLoanTermRequestDto,
	GetLoanTermResponseDto,
} from "@domain/dto/loan/get-loan-term.dto";
import {
	GetLoanTypeRequestDto,
	GetLoanTypeResponseDto,
} from "@domain/dto/loan/get-loan-type.dto";
import { SetLoanTypeRequestDto } from "@domain/dto/loan/set-loan-type.dto";
import {
	StartKycTransactionRequestDto,
	StartKycTransactionResponseDto,
} from "@domain/dto/loan/start-transaction.dto";
import { ApiResponse } from "@domain/models/api-response.model";

export interface LoanRepositoryPort {
	/**
	 * Start a KYC transaction
	 * @param request The transaction ID
	 * @returns Promise of an API response containing the transaction data
	 */
	startKycTransaction(
		request: StartKycTransactionRequestDto,
	): Promise<ApiResponse<StartKycTransactionResponseDto>>;

	/**
	 * Get all available loan categories
	 * @returns Promise of an array of loan categories
	 */
	getLoanCategories(): Promise<ApiResponse<GetLoanCategoriesResponseDto[]>>;

	/**
	 * Set the loan type for a transaction
	 * @param transactionId The transaction ID
	 * @param loanTypeId The loan type ID to set
	 * @returns Promise of an API response with boolean data
	 */
	setLoanType(request: SetLoanTypeRequestDto): Promise<ApiResponse<boolean>>;

	/**
	 * Get the loan type for a transaction
	 * @param transactionId The transaction ID
	 * @returns Promise of an API response containing the loan type
	 */
	getLoanType(
		request: GetLoanTypeRequestDto,
	): Promise<ApiResponse<GetLoanTypeResponseDto>>;

	/**
	 * Get all available loan terms for a transaction
	 * @param transactionId The transaction ID
	 * @returns Promise of an API response containing an array of loan terms
	 */
	getLoanTerms(
		request: GetLoanTermRequestDto,
	): Promise<ApiResponse<GetLoanTermResponseDto>>;
}
