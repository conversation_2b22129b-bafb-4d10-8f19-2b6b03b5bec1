import { TransactionIdDto } from "@domain/dto/transaction.dto";
import { ApiResponse } from "@domain/models/api-response.model";

/**
 * Output port for user repository
 * This interface defines how the application core interacts with external data sources
 */
export interface UserRepositoryPort {
	/**
	 * Sign in anonymously
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing authentication data
	 */
	signInAnonymous(
		request: TransactionIdDto,
	): Promise<ApiResponse<Record<string, unknown>>>;

	/**
	 * Refresh token
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing refreshed token data
	 */
	refreshToken(
		request: TransactionIdDto,
	): Promise<ApiResponse<Record<string, unknown>>>;
}
