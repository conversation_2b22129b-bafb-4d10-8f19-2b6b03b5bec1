import {
	GetAddressInfoRequestDto,
	GetAddressInfoResponseDto,
} from "@domain/dto/address/get-address-info.dto";
import {
	GetDistrictRequestDto,
	GetDistrictResponseDto,
} from "@domain/dto/address/get-district.dto";
import {
	GetHousingTypeRequestDto,
	GetHousingTypeResponseDto,
} from "@domain/dto/address/get-housing-type.dto";
import {
	GetPostcodeRequestDto,
	GetPostcodeResponseDto,
} from "@domain/dto/address/get-postcode.dto";
import {
	GetProvinceRequestDto,
	GetProvinceResponseDto,
} from "@domain/dto/address/get-province.dto";
import {
	GetSubDistrictRequestDto,
	GetSubDistrictResponseDto,
} from "@domain/dto/address/get-sub-district.dto";
import { UpdateAddressInfoRequestDto } from "@domain/dto/address/update-address-info.dto";
import { UpdateHousingTypeRequest } from "@domain/dto/address/update-housing-type.dto";
import { ApiResponse } from "@domain/models/api-response.model";

export interface AddressRepositoryPort {
	getAddressInfo(
		request: GetAddressInfoRequestDto,
	): Promise<ApiResponse<GetAddressInfoResponseDto[]>>;

	getProvinces(
		request: GetProvinceRequestDto,
	): Promise<ApiResponse<GetProvinceResponseDto[]>>;

	getDistricts(
		request: GetDistrictRequestDto,
	): Promise<ApiResponse<GetDistrictResponseDto[]>>;

	getSubDistricts(
		request: GetSubDistrictRequestDto,
	): Promise<ApiResponse<GetSubDistrictResponseDto[]>>;

	getPostalCodes(
		request: GetPostcodeRequestDto,
	): Promise<ApiResponse<GetPostcodeResponseDto[]>>;

	getHousingTypes(
		request: GetHousingTypeRequestDto,
	): Promise<ApiResponse<GetHousingTypeResponseDto[]>>;

	updateAddressInfo(
		request: UpdateAddressInfoRequestDto,
	): Promise<ApiResponse<GetAddressInfoResponseDto>>;

	updateHousingType(
		request: UpdateHousingTypeRequest,
	): Promise<ApiResponse<GetHousingTypeResponseDto>>;
}
