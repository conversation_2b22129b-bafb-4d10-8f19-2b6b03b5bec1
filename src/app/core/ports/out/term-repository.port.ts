import { AcceptTermsRequestDto } from "@domain/dto/terms/accept-term.dto";
import {
	GetTermRequestDto,
	GetTermResponseDto,
} from "@domain/dto/terms/get-term.dto";
import { ApiResponse } from "@domain/models/api-response.model";

/**
 * Output port for user repository
 * This interface defines how the application core interacts with external data sources
 */
export interface TermRepositoryPort {
	/**
	 * Get term
	 * @param request The request containing transaction ID and term code
	 * @returns Promise of ApiResponse containing term information
	 */
	getTerm(request: GetTermRequestDto): Promise<ApiResponse<GetTermResponseDto>>;

	/**
	 * Accept term
	 * @param request The request containing transaction ID and term code
	 * @returns Promise of ApiResponse containing boolean data
	 */
	acceptTerm(request: AcceptTermsRequestDto): Promise<ApiResponse<boolean>>;
}
