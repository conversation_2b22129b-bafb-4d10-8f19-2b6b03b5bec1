import { GetOccupationGroupResponseDto } from "@domain/dto/working/get-occupation-group.dto";
import { GetOccupationResponseDto } from "@domain/dto/working/get-occupation.dto";
import { GetWorkingInfoResponseDto } from "@domain/dto/working/get-working-info.dto";
import { UpdateWorkingInfoResponseDto } from "@domain/dto/working/update-working-info.dto";

export class WorkingInfo {
	constructor(
		private readonly _occupationGroupCode: string,
		private readonly _occupationGroupName: string,
		private readonly _occupationCode: string,
		private readonly _occupationName: string,
		private readonly _companyName: string,
		private readonly _position: string,
		private readonly _startDate: string,
		private readonly _monthlyIncome: number,
		private readonly _additionalIncome: number,
		private readonly _monthlyExpense: number,
		private readonly _incomeSlip: string,
		private readonly _hasRequestedLoan: boolean,
	) {}

	get occupationGroupCode(): string {
		return this._occupationGroupCode;
	}

	get occupationGroupName(): string {
		return this._occupationGroupName;
	}

	get occupationCode(): string {
		return this._occupationCode;
	}

	get occupationName(): string {
		return this._occupationName;
	}

	get companyName(): string {
		return this._companyName;
	}

	get position(): string {
		return this._position;
	}

	get startDate(): string {
		return this._startDate;
	}

	get monthlyIncome(): number {
		return this._monthlyIncome;
	}

	get additionalIncome(): number {
		return this._additionalIncome;
	}

	get monthlyExpense(): number {
		return this._monthlyExpense;
	}

	get incomeSlip(): string {
		return this._incomeSlip;
	}

	get hasRequestedLoan(): boolean {
		return this._hasRequestedLoan;
	}

	static fromJson(
		json: GetWorkingInfoResponseDto | UpdateWorkingInfoResponseDto,
	): WorkingInfo {
		return new WorkingInfo(
			json.occupation_group_code,
			json.occupation_group_name,
			json.occupation_code,
			json.occupation_name,
			json.company_name,
			json.position,
			json.start_date,
			json.monthly_income,
			json.additional_income,
			json.monthly_expense,
			json.income_slip,
			json.has_requested_loan,
		);
	}
}

export class OccupationGroup {
	constructor(
		private readonly _code: string,
		private readonly _name: string,
	) {}

	get code(): string {
		return this._code;
	}

	get name(): string {
		return this._name;
	}

	static fromJson(json: GetOccupationGroupResponseDto): OccupationGroup {
		return new OccupationGroup(
			json.occupationGroupCode,
			json.occupationGroupName,
		);
	}
}

export class Occupation {
	constructor(
		private readonly _code: string,
		private readonly _name: string,
	) {}

	get code(): string {
		return this._code;
	}

	get name(): string {
		return this._name;
	}

	static fromJson(json: GetOccupationResponseDto): Occupation {
		return new Occupation(json.occupationCode, json.occupationName);
	}
}
