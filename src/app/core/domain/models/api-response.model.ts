/**
 * Standard API response structure
 * This is used to wrap all API responses in a consistent format
 */
export class ApiResponse<T> {
	constructor(
		private readonly _statusCode: number,
		private readonly _data: T,
		private readonly _message: string | null,
		private readonly _error: string | null,
		private readonly _timestamp: string,
	) {}

	get statusCode(): number {
		return this._statusCode;
	}

	get data(): T {
		return this._data;
	}

	get message(): string | null {
		return this._message;
	}

	get error(): string | null {
		return this._error;
	}

	get timestamp(): string {
		return this._timestamp;
	}

	/**
	 * Check if the response is successful
	 * @returns True if the status code is in the 2xx range
	 */
	isSuccess(): boolean {
		return this._statusCode >= 200 && this._statusCode < 300;
	}

	hasData(): boolean {
		return this._data !== null && this._data !== undefined;
	}

	isTrue(): boolean {
		return this._data === true;
	}

	/**
	 * Factory method to create an API response from a raw response object
	 * @param response The raw response object
	 * @returns A new ApiResponse instance
	 */
	static fromResponse<T>(response: {
		statusCode: number;
		data: T;
		message: string | null;
		error: string | null;
		timestamp: string;
	}): ApiResponse<T> {
		return new ApiResponse<T>(
			response.statusCode,
			response.data,
			response.message,
			response.error,
			response.timestamp,
		);
	}
}
