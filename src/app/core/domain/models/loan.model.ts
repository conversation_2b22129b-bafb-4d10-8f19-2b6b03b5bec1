/**
 * Represents a loan category in the system
 * Categories are used to group different types of loans
 */
export class LoanCategory {
	constructor(
		private readonly _id: number,
		private readonly _name: string,
		private readonly _description: string,
	) {}

	get id(): number {
		return this._id;
	}

	get name(): string {
		return this._name;
	}

	get description(): string {
		return this._description;
	}

	/**
	 * Factory method to create a category from primitive values
	 * @param id The category ID
	 * @param name The category name
	 * @param description The category description
	 * @returns A new LoanCategory instance
	 */
	static create(id: number, name: string, description: string): LoanCategory {
		return new LoanCategory(id, name, description);
	}

	/**
	 * Factory method to create a category from DTO
	 * @param dto The category DTO
	 * @returns A new LoanCategory instance
	 */
	static fromJson(dto: any): LoanCategory {
		return new LoanCategory(dto.id, dto.name, dto.description);
	}
}

/**
 * Represents a specific type of loan product
 * Each loan type belongs to a category
 */
export class LoanType {
	constructor(
		private readonly _id: number,
		private readonly _name: string,
		private readonly _description: string,
	) {}

	get id(): number {
		return this._id;
	}

	get name(): string {
		return this._name;
	}

	get description(): string {
		return this._description;
	}

	isHirePurchase(): boolean {
		return this.id === 2;
	}

	isPersonalLoan(): boolean {
		return this.id === 6;
	}

	/**
	 * Factory method to create a loan type from DTO
	 * @param dto The loan type DTO
	 * @returns A new LoanType instance
	 */
	static fromJson(dto: any): LoanType {
		return new LoanType(dto.id, dto.name, dto.description);
	}
}

/**
 * Represents terms and conditions for a loan
 * These are the legal agreements that customers must accept
 */
export class LoanTerm {
	constructor(
		private readonly _id: number,
		private readonly _code: string,
		private readonly _mandatory: boolean,
		private readonly _type: string,
		private readonly _isActive: boolean,
		private readonly _title: string,
		private readonly _version: string,
		private readonly _languageCode: string,
		private readonly _content: string,
	) {}

	get id(): number {
		return this._id;
	}

	get code(): string {
		return this._code;
	}

	get mandatory(): boolean {
		return this._mandatory;
	}

	get type(): string {
		return this._type;
	}

	get isActive(): boolean {
		return this._isActive;
	}

	get title(): string {
		return this._title;
	}

	get version(): string {
		return this._version;
	}

	get languageCode(): string {
		return this._languageCode;
	}

	get content(): string {
		return this._content;
	}

	/**
	 * Factory method to create a loan term from DTO
	 * @param dto The loan term DTO
	 * @returns A new LoanTerm instance
	 */
	static fromJson(dto: any): LoanTerm {
		return new LoanTerm(
			dto.id,
			dto.code,
			dto.mandatory,
			dto.type,
			dto.is_active,
			dto.title,
			dto.version,
			dto.language_code,
			dto.content,
		);
	}
}
