import { GetTermResponseDto } from "@domain/dto/terms/get-term.dto";

export class Term {
	constructor(
		private readonly _id: number,
		private readonly _code: string,
		private readonly _mandatory: boolean,
		private readonly _type: string,
		private readonly _isActive: boolean,
		private readonly _title: string,
		private readonly _version: string,
		private readonly _languageCode: string,
		private readonly _content: string,
		private readonly _consentItem: ConsentItem[],
	) {}

	get id(): number {
		return this._id;
	}

	get code(): string {
		return this._code;
	}

	get mandatory(): boolean {
		return this._mandatory;
	}

	get type(): string {
		return this._type;
	}

	get isActive(): boolean {
		return this._isActive;
	}

	get title(): string {
		return this._title;
	}

	get version(): string {
		return this._version;
	}

	get languageCode(): string {
		return this._languageCode;
	}

	get content(): string {
		return this._content;
	}

	get consentItem(): ConsentItem[] {
		return this._consentItem;
	}

	static fromJson(json: GetTermResponseDto): Term {
		return new Term(
			json.id,
			json.code,
			json.mandatory,
			json.type,
			json.is_active,
			json.title,
			json.version,
			json.language_code,
			json.content,
			Array.isArray(json.consent_item)
				? json.consent_item.map((item: any) => ConsentItem.fromJson(item))
				: [],
		);
	}
}

export class ConsentItem {
	constructor(
		private readonly _id: number,
		private readonly _context: ConsentContext,
		private readonly _order: number,
		private readonly _item: ConsentSubItem[],
	) {}

	get id(): number {
		return this._id;
	}

	get context(): ConsentContext {
		return this._context;
	}

	get order(): number {
		return this._order;
	}

	get item(): ConsentSubItem[] {
		return this._item;
	}

	static fromJson(json: any): ConsentItem {
		return new ConsentItem(
			json.id,
			ConsentContext.fromJson(json.context),
			json.order,
			json.item.map((item: any) => ConsentSubItem.fromJson(item)),
		);
	}
}

export class ConsentContext {
	constructor(private readonly _label: string) {}

	get label(): string {
		return this._label;
	}

	static fromJson(json: any): ConsentContext {
		return new ConsentContext(json.label);
	}
}

export class ConsentSubItem {
	constructor(
		private readonly _id: number,
		private readonly _context: ConsentContext,
		private readonly _order: number,
	) {}

	get id(): number {
		return this._id;
	}

	get context(): ConsentContext {
		return this._context;
	}

	get order(): number {
		return this._order;
	}

	static fromJson(json: any): ConsentSubItem {
		return new ConsentSubItem(
			json.id,
			ConsentContext.fromJson(json.context),
			json.order,
		);
	}
}
