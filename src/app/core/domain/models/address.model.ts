import { GetAddressInfoResponseDto } from "@domain/dto/address/get-address-info.dto";
import { GetDistrictResponseDto } from "@domain/dto/address/get-district.dto";
import { GetHousingTypeResponseDto } from "@domain/dto/address/get-housing-type.dto";
import { GetPostcodeResponseDto } from "@domain/dto/address/get-postcode.dto";
import { GetProvinceResponseDto } from "@domain/dto/address/get-province.dto";
import { GetSubDistrictResponseDto } from "@domain/dto/address/get-sub-district.dto";

export class AddressInfo {
	constructor(
		private readonly _addressType: string,
		private readonly _houseNo?: string,
		private readonly _villageNo?: string,
		private readonly _address1?: string,
		private readonly _address2?: string,
		private readonly _alley?: string,
		private readonly _street?: string | null,
		private readonly _subDistrict?: string,
		private readonly _district?: string,
		private readonly _province?: string,
		private readonly _postalCode?: string,
		private readonly _housingType?: string,
		private readonly _manualPostalCode?: boolean,
		private readonly _isSame?: boolean,
		private readonly _housingCode?: string,
		private readonly _reason?: string,
	) {}

	get addressType(): string {
		return this._addressType;
	}

	get houseNo(): string | undefined {
		return this._houseNo;
	}

	get villageNo(): string | undefined {
		return this._villageNo;
	}

	get address1(): string | undefined {
		return this._address1;
	}

	get address2(): string | undefined {
		return this._address2;
	}

	get alley(): string | undefined {
		return this._alley;
	}

	get street(): string | null | undefined {
		return this._street;
	}

	get subDistrict(): string | undefined {
		return this._subDistrict;
	}

	get district(): string | undefined {
		return this._district;
	}

	get province(): string | undefined {
		return this._province;
	}

	get postalCode(): string | undefined {
		return this._postalCode;
	}

	get housingType(): string | undefined {
		return this._housingType;
	}

	get manualPostalCode(): boolean | undefined {
		return this._manualPostalCode;
	}

	get isSame(): boolean | undefined {
		return this._isSame;
	}

	get housingCode(): string | undefined {
		return this._housingCode;
	}

	get reason(): string | undefined {
		return this._reason;
	}

	static fromJson(response: GetAddressInfoResponseDto): AddressInfo {
		return new AddressInfo(
			response.addressType,
			response.house_no,
			response.village_no,
			response.address_1,
			response.address_2,
			response.alley,
			response.street,
			response.sub_district,
			response.district,
			response.province,
			response.postal_code,
			response.housing_type,
			response.manual_postal_code,
			response.is_same,
			response.housing_code,
			response.reason,
		);
	}
}

export class Province {
	constructor(private readonly _name: string) {}

	get name(): string {
		return this._name;
	}

	static fromJson(json: GetProvinceResponseDto): Province {
		return new Province(json.provinceName);
	}
}

export class District {
	constructor(
		private readonly _name: string,
		private readonly _province: string,
	) {}

	get name(): string {
		return this._name;
	}

	get province(): string {
		return this._province;
	}

	static fromJson(response: GetDistrictResponseDto): District {
		return new District(response.districtName, response.provinceName);
	}
}

export class SubDistrict {
	constructor(
		private readonly _name: string,
		private readonly _district: string,
		private readonly _province: string,
	) {}

	get name(): string {
		return this._name;
	}

	get district(): string {
		return this._district;
	}

	get province(): string {
		return this._province;
	}

	static fromJson(response: GetSubDistrictResponseDto): SubDistrict {
		return new SubDistrict(
			response.subdistrictName,
			response.districtName,
			response.provinceName,
		);
	}
}

export class PostalCode {
	constructor(
		private readonly _code: string,
		private readonly _subDistrict: string,
		private readonly _district: string,
		private readonly _province: string,
	) {}

	get code(): string {
		return this._code;
	}

	get subDistrict(): string {
		return this._subDistrict;
	}

	get district(): string {
		return this._district;
	}

	get province(): string {
		return this._province;
	}

	static fromJson(response: GetPostcodeResponseDto): PostalCode {
		return new PostalCode(
			response.postcode,
			response.subdistrictName,
			response.districtName,
			response.provinceName,
		);
	}
}

export class HousingType {
	constructor(
		private readonly _code: string,
		private readonly _name: string,
	) {}

	get code(): string {
		return this._code;
	}

	get name(): string {
		return this._name;
	}

	static fromJson(response: GetHousingTypeResponseDto): HousingType {
		return new HousingType(response.housingTypeCode, response.housingTypeName);
	}
}
