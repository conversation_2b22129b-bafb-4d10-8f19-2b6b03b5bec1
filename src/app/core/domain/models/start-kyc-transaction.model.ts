/**
 * Model class for StartKycTransaction
 */
export class StartKycTransaction {
	constructor(
		public txnId: string,
		public currentState: string,
		public expiryAt: number,
		public workflow: string,
	) {}

	/**
	 * Static method to create an instance from raw JSON data
	 * @param json The raw JSON data
	 * @returns An instance of StartKycTransaction
	 */
	static fromJson(json: any): StartKycTransaction {
		return new StartKycTransaction(
			json.txn_id ?? "",
			json.current_state ?? "",
			Number(json.expiry_at ?? 0),
			json.workflow ?? "",
		);
	}
}
