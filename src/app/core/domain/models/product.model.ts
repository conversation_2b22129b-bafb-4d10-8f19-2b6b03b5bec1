import { GetProductListResponseDto } from "@domain/dto/products/get-product-list.dto";
import { GetProductResponseDto } from "@domain/dto/products/get-product.dto";

export class ProductList {
	constructor(
		private readonly _productType: string,
		private readonly _productTypeName: string,
		private readonly _productSubType: string,
		private readonly _productBrandCode: string,
		private readonly _productBrandName: string,
		private readonly _productModelCode: string,
		private readonly _productModelName: string,
		private readonly _productColorCode: string,
		private readonly _productColorName: string,
		private readonly _productPrice: number,
		private readonly _normalPrice: number,
		private readonly _startDate: string,
		private readonly _endDate: string,
		private readonly _productImage: string,
		private readonly _productDescription: string,
	) {}

	get productType(): string {
		return this._productType;
	}

	get productTypeName(): string {
		return this._productTypeName;
	}

	get productSubType(): string {
		return this._productSubType;
	}

	get productBrandCode(): string {
		return this._productBrandCode;
	}

	get productBrandName(): string {
		return this._productBrandName;
	}

	get productModelCode(): string {
		return this._productModelCode;
	}

	get productModelName(): string {
		return this._productModelName;
	}

	get productColorCode(): string {
		return this._productColorCode;
	}

	get productColorName(): string {
		return this._productColorName;
	}

	get productPrice(): number {
		return this._productPrice;
	}

	get normalPrice(): number {
		return this._normalPrice;
	}

	get startDate(): string {
		return this._startDate;
	}

	get endDate(): string {
		return this._endDate;
	}

	get productImage(): string {
		return this._productImage;
	}

	get productDescription(): string {
		return this._productDescription;
	}

	static fromJson(json: GetProductListResponseDto): ProductList {
		return new ProductList(
			json.productType,
			json.productTypeName,
			json.productSubType,
			json.productBrandCode,
			json.productBrandName,
			json.productModelCode,
			json.productModelName,
			json.productColorCode,
			json.productColorName,
			json.productPrice,
			json.normalPrice,
			json.startDate,
			json.endDate,
			json.productImage,
			json.productDescription,
		);
	}
}

export class Product {
	constructor(
		private readonly _id: string,
		private readonly _productType: string,
		private readonly _brandCode: string,
		private readonly _modelCode: string,
		private readonly _modelName: string,
		private readonly _colorCode: string,
		private readonly _colorName: string,
		private readonly _price: number,
		private readonly _normalPrice: number,
		private readonly _startDate: string,
		private readonly _endDate: string,
		private readonly _onboardingId: string,
		private readonly _brandName: string,
		private readonly _interestRate: number,
		private readonly _policyCode: string,
		private readonly _policySubCode: string,
		private readonly _discount: any,
		private readonly _discountPercent: any,
		private readonly _promotionCode: any,
		private readonly _promotionName: any,
		private readonly _down: any,
		private readonly _period: any,
		private readonly _image: string,
		private readonly _promotionDescription: any,
		private readonly _productSubtype: string,
		private readonly _iirValue: number,
		private readonly _promotionStatus: any,
		private readonly _promotionMessage: any,
		private readonly _netPrice: number,
		private readonly _description: string,
		private readonly _downRange: number[],
		private readonly _periodRange: number[],
		private readonly _minMaxDownRange: number[],
		private readonly _minMaxPeriodRange: number[],
		private readonly _downRangePrice: DownRangePrice[],
		private readonly _periodRangePrice: PeriodRangePrice[],
	) {}

	get id(): string {
		return this._id;
	}

	get productType(): string {
		return this._productType;
	}

	get brandCode(): string {
		return this._brandCode;
	}

	get modelCode(): string {
		return this._modelCode;
	}

	get modelName(): string {
		return this._modelName;
	}

	get colorCode(): string {
		return this._colorCode;
	}

	get colorName(): string {
		return this._colorName;
	}

	get price(): number {
		return this._price;
	}

	get normalPrice(): number {
		return this._normalPrice;
	}

	get startDate(): string {
		return this._startDate;
	}

	get endDate(): string {
		return this._endDate;
	}

	get onboardingId(): string {
		return this._onboardingId;
	}

	get brandName(): string {
		return this._brandName;
	}

	get interestRate(): number {
		return this._interestRate;
	}

	get policyCode(): string {
		return this._policyCode;
	}

	get policySubCode(): string {
		return this._policySubCode;
	}

	get discount(): any {
		return this._discount;
	}

	get discountPercent(): any {
		return this._discountPercent;
	}

	get promotionCode(): any {
		return this._promotionCode;
	}

	get promotionName(): any {
		return this._promotionName;
	}

	get down(): any {
		return this._down;
	}

	get period(): any {
		return this._period;
	}

	get image(): string {
		return this._image;
	}

	get promotionDescription(): any {
		return this._promotionDescription;
	}

	get productSubtype(): string {
		return this._productSubtype;
	}

	get iirValue(): number {
		return this._iirValue;
	}

	get promotionStatus(): any {
		return this._promotionStatus;
	}

	get promotionMessage(): any {
		return this._promotionMessage;
	}

	get netPrice(): number {
		return this._netPrice;
	}

	get description(): string {
		return this._description;
	}

	get downRange(): number[] {
		return this._downRange;
	}

	get periodRange(): number[] {
		return this._periodRange;
	}

	get minMaxDownRange(): number[] {
		return this._minMaxDownRange;
	}

	get minMaxPeriodRange(): number[] {
		return this._minMaxPeriodRange;
	}

	get downRangePrice(): DownRangePrice[] {
		return this._downRangePrice;
	}

	get periodRangePrice(): PeriodRangePrice[] {
		return this._periodRangePrice;
	}

	static fromJson(json: GetProductResponseDto): Product {
		return new Product(
			json.id,
			json.product_type,
			json.brand_code,
			json.model_code,
			json.model_name,
			json.color_code,
			json.color_name,
			json.price,
			json.normal_price,
			json.start_date,
			json.end_date,
			json.onboarding_id,
			json.brand_name,
			json.interest_rate,
			json.policy_code,
			json.policy_sub_code,
			json.discount,
			json.discount_percent,
			json.promotion_code,
			json.promotion_name,
			json.down,
			json.period,
			json.image,
			json.promotion_description,
			json.product_subtype,
			json.iir_value,
			json.promotion_status,
			json.promotion_message,
			json.net_price,
			json.description,
			json.down_range,
			json.period_range,
			json.min_max_down_range,
			json.min_max_period_range,
			json.down_range_price.map((item: any) => DownRangePrice.fromJson(item)),
			json.period_range_price.map((item: any) =>
				PeriodRangePrice.fromJson(item),
			),
		);
	}
}

export class DownRangePrice {
	constructor(
		private readonly _percent: number,
		private readonly _price: number,
	) {}

	get percent(): number {
		return this._percent;
	}

	get price(): number {
		return this._price;
	}

	static fromJson(json: DownRangePrice): DownRangePrice {
		return new DownRangePrice(json.percent, json.price);
	}
}

export class PeriodRangePrice {
	constructor(
		private readonly _percent: number,
		private readonly _period: Period[],
	) {}

	get percent(): number {
		return this._percent;
	}

	get period(): Period[] {
		return this._period;
	}

	static fromJson(json: PeriodRangePrice): PeriodRangePrice {
		return new PeriodRangePrice(json.percent, json.period);
	}
}

export class Period {
	constructor(
		private readonly _period: number,
		private readonly _price: number,
		private readonly _disabled: boolean,
	) {}

	get period(): number {
		return this._period;
	}

	get price(): number {
		return this._price;
	}

	get disabled(): boolean {
		return this._disabled;
	}

	static fromJson(json: Period): Period {
		return new Period(json.period, json.price, json.disabled);
	}
}
