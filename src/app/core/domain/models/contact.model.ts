import { GetContactResponseDto } from "@domain/dto/contact/get-contact.dto";
import { SendOtpResponseDto } from "@domain/dto/contact/send-otp.dto";

export class Contact {
	constructor(
		private readonly _phone: string,
		private readonly _email: string,
		private readonly _isPhoneConfirmed: boolean,
		private readonly _isEmailConfirmed: boolean,
		private readonly _isDocumentVerified: boolean,
		private readonly _phoneConfirmedAt: number,
		private readonly _emailConfirmedAt: number,
		private readonly _documentVerifiedAt: string,
	) {}

	get phone(): string {
		return this._phone;
	}

	get email(): string {
		return this._email;
	}

	get isPhoneConfirmed(): boolean {
		return this._isPhoneConfirmed;
	}

	get isEmailConfirmed(): boolean {
		return this._isEmailConfirmed;
	}

	get isDocumentVerified(): boolean {
		return this._isDocumentVerified;
	}

	get phoneConfirmedAt(): number {
		return this._phoneConfirmedAt;
	}

	get emailConfirmedAt(): number {
		return this._emailConfirmedAt;
	}

	get documentVerifiedAt(): string {
		return this._documentVerifiedAt;
	}

	static from<PERSON>son(json: GetContactResponseDto): Contact {
		return new Contact(
			json.phone,
			json.email,
			json.is_phone_confirmed,
			json.is_email_confirmed,
			json.is_document_verified,
			json.phone_confirmed_at,
			json.email_confirmed_at,
			json.document_verified_at,
		);
	}
}

export class SendOtp {
	constructor(
		private readonly _phone: string,
		private readonly _refCode: string,
		private readonly _otpExpiresAt: number,
	) {}

	get phone(): string {
		return this._phone;
	}

	get refCode(): string {
		return this._refCode;
	}

	get otpExpiresAt(): number {
		return this._otpExpiresAt;
	}

	static fromJson(json: SendOtpResponseDto): SendOtp {
		return new SendOtp(json.phone, json.ref_code, json.otp_expires_at);
	}
}
