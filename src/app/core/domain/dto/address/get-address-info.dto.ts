import { TransactionIdDto } from "../transaction.dto";

export interface GetAddressInfoRequestDto extends TransactionIdDto {}

export interface GetAddressInfoResponseDto {
	addressType: string;
	house_no?: string;
	village_no?: string;
	address_1?: string;
	address_2?: string;
	alley?: string;
	street?: string | null;
	sub_district?: string;
	district?: string;
	province?: string;
	postal_code?: string;
	housing_type?: string;
	manual_postal_code?: boolean;
	is_same?: boolean;
	housing_code?: string;
	reason?: string;
}
