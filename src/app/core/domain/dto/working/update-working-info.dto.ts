import { TransactionIdDto } from "../transaction.dto";

export interface UpdateWorkingInfoRequestDto extends TransactionIdDto {
	company_name?: string;
	occupation_group_code?: string;
	occupation_group_name?: string;
	occupation_code?: string;
	occupation_name?: string;
	position?: string;
	start_date?: string;
	monthly_income?: number;
	additional_income?: number;
	monthly_expense?: number;
	income_slip?: boolean;
}

export interface UpdateWorkingInfoResponseDto {
	occupation_group_code: string;
	occupation_group_name: string;
	occupation_code: string;
	occupation_name: string;
	company_name: string;
	position: string;
	start_date: string;
	monthly_income: number;
	additional_income: number;
	monthly_expense: number;
	income_slip: string;
	has_requested_loan: boolean;
}
