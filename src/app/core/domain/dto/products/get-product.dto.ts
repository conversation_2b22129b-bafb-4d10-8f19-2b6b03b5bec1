import { TransactionIdDto } from "../transaction.dto";

export interface GetProductRequestDto extends TransactionIdDto {
	promotion_code?: string;
}

export interface GetProductResponseDto {
	id: string;
	product_type: string;
	brand_code: string;
	model_code: string;
	model_name: string;
	color_code: string;
	color_name: string;
	price: number;
	normal_price: number;
	start_date: string;
	end_date: string;
	onboarding_id: string;
	brand_name: string;
	interest_rate: number;
	policy_code: string;
	policy_sub_code: string;
	discount: any;
	discount_percent: any;
	promotion_code: any;
	promotion_name: any;
	down: any;
	period: any;
	image: string;
	promotion_description: any;
	product_subtype: string;
	iir_value: number;
	promotion_status: any;
	promotion_message: any;
	net_price: number;
	description: string;
	down_range: number[];
	period_range: number[];
	min_max_down_range: number[];
	min_max_period_range: number[];
	down_range_price: DownRangePrice[];
	period_range_price: PeriodRangePrice[];
}

export interface DownRangePrice {
	percent: number;
	price: number;
}

export interface PeriodRangePrice {
	percent: number;
	period: Period[];
}

export interface Period {
	period: number;
	price: number;
	disabled: boolean;
}
