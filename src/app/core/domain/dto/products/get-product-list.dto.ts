import { TransactionIdDto } from "../transaction.dto";

export interface GetProductListRequestDto extends TransactionIdDto {
	product_type: string;
}

export interface GetProductListResponseDto {
	productType: string;
	productTypeName: string;
	productSubType: string;
	productBrandCode: string;
	productBrandName: string;
	productModelCode: string;
	productModelName: string;
	productColorCode: string;
	productColorName: string;
	productPrice: number;
	normalPrice: number;
	startDate: string;
	endDate: string;
	productImage: string;
	productDescription: string;
}
