import { TransactionIdDto } from "../transaction.dto";

export interface GetTermRequestDto extends TransactionIdDto {
	lang: string;
	code: string;
}

export interface GetTermResponseDto {
	id: number;
	code: string;
	mandatory: boolean;
	type: string;
	is_active: boolean;
	title: string;
	version: string;
	language_code: string;
	content: string;
	consent_item: ConsentItem[];
}

export interface ConsentItem {
	id: number;
	context: ConsentContext;
	order: number;
	item: ConsentSubItem[];
}

export interface ConsentContext {
	label: string;
}

export interface ConsentSubItem {
	id: number;
	context: ConsentContext;
	order: number;
}
