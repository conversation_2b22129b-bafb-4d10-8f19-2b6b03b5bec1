import { PageId, Workflow } from "./workflow.model";

/**
 * Loan Application Workflow Configuration
 *
 * This workflow defines the steps and navigation for the loan application process.
 * It includes steps for selecting loan type, customer type, ID verification,
 * personal information, document uploads, and final submission.
 */

export const loanApplicationWorkflow: Workflow = {
	id: "loan-application-workflow",
	name: "Loan Application Workflow",
	version: "1.0.0",
	start: "1.1",
	createdAt: "2025-05-01",
	steps: [
		{
			stepId: "1",
			stepName: "เลือกผลิตภัณฑ",
			stepDescription: "เริ่มต้นสมัครสินเชื่อ",
			children: [
				{
					stepId: "1.1",
					stepName: "เลือกประเภทสินเชื่อ",
					stepDescription: "เลือกประเภทสินเชื่อที่ต้องการ",
					meta: { pageId: PageId.LOAN_TYPE },
					back: null,
					next: ["1.2"],
				},
				{
					stepId: "1.2",
					stepName: "เลือกประเภทลูกค้า",
					stepDescription: "เลือกประเภทลูกค้าที่ทำการสมัคร",
					meta: { pageId: PageId.CUSTOMER_TYPE },
					back: "1.1",
					next: [
						{
							condition: {
								field: "customerType",
								operator: "==",
								value: "thai",
							},
							goTo: "1.3",
						},
						{
							condition: {
								field: "customerType",
								operator: "==",
								value: "foreign",
							},
							goTo: "2.1",
							forceCompleteParent: true,
						},
					],
				},
				{
					stepId: "1.3",
					stepName: "อ่านบัตรประชาชน",
					stepDescription: "อ่านข้อมูลจากบัตรประชาชนของลูกค้า",
					back: "1.2",
					next: ["1.4"],
					children: [
						{
							stepId: "1.3.1",
							stepName: "อ่านข้อมูลจากบัตรประชาชน (เฉพาะบุคคลธรรมดาสัญชาติไทย)",
							meta: { pageId: PageId.READ_ID_CARD_GUIDELINE },
							back: null,
							next: ["1.3.2"],
						},
						{
							stepId: "1.3.2",
							stepName: "อ่านข้อมูลจากบัตรประชาชน (เฉพาะบุคคลธรรมดาสัญชาติไทย)",
							meta: { pageId: PageId.READ_ID_CARD_QR },
							back: "1.3.1",
							next: ["1.4"], // อาจให้ component คุม next → ไป 1.4
						},
					],
				},
				{
					stepId: "1.4",
					stepName: "กรอกและตรวจสอบข้อมูลส่วนบุคคล และถ่ายรูปเอกสาร",
					stepDescription:
						"กรอกข้อมูลส่วนบุคคล * สำหรับบุคคลสัญชาติไทย ระบบจะทำการตรวจสอบ ข้อมูลของท่านกับกรมการปกครองก่อนดำเนินการต่อ",
					meta: { pageId: PageId.PERSONAL_INFO },
					back: "1.3",
					next: ["1.5"],
				},
				{
					stepId: "1.5",
					stepName: "ถ่ายรูปเอกสารยืนยันตัวตน",
					stepDescription:
						"บุคคลสัญชาติไทย: บัตรประชาชน บุคคลต่างสัญชาติ: หนังสือเดินทาง หรือบัตรต่างด้าว",
					meta: { pageId: PageId.ID_PHOTO },
					next: ["2.1.1"],
					back: "1.4",
					subflow: {
						id: "id-photo-subflow",
						start: "id-photo.1",
						returnTo: "1.5",
						steps: [
							{
								stepId: "id-photo.1",
								stepName: "คำแนะนำการถ่ายรูป",
								meta: { pageId: PageId.TAKE_ID_PHOTO_GUIDE },
								back: null,
								next: ["id-photo.2"],
							},
							{
								stepId: "id-photo.2",
								stepName: "ตรวจสอบกล้อง",
								meta: { pageId: PageId.WEBCAM_CHECK },
								back: "id-photo.1",
								next: ["id-photo.3"],
							},
							{
								stepId: "id-photo.3",
								stepName: "ถ่ายรูปใบหน้า",
								meta: { pageId: PageId.TAKE_ID_PHOTO },
								back: "id-photo.2",
								next: ["id-photo.4"],
							},
							{
								stepId: "id-photo.4",
								stepName: "ยืนยันรูปถ่าย",
								meta: { pageId: PageId.CONFIRM_ID_PHOTO },
								back: "id-photo.3",
								next: [],
							},
						],
					},
				},
			],
		},
		{
			stepId: "2",
			stepName: "กรอกข้อมูลเพิ่มเติม",
			stepDescription: "กรอกข้อมูลและตรวจสอบความถูกต้อง",
			children: [
				{
					stepId: "2.1",
					stepName: "กรอกข้อมูลการสมัครสินเชื่อ",
					stepDescription:
						"กรอกข้อมูลการสมัครสินเชื่อให้ครบถ้วน และทำการถ่ายภาพ หรือแนบเอกสารที่เกี่ยวข้อง เพื่อใช้ประกอบในการพิจารณาสินเชื่อ",
					children: [
						{
							stepId: "2.1.1",
							stepName: "ข้อมูลที่อยู่และการติดต่อ",
							meta: { pageId: PageId.ADDRESS_CONTACT },
							next: ["2.1.2"],
							back: "1.5",
						},
						{
							stepId: "2.1.2",
							stepName: "ข้อมูลการทำงาน",
							meta: { pageId: PageId.WORK_INFO },
							next: ["2.1.3"],
							back: "2.1.1",
						},
						{
							stepId: "2.1.3",
							stepName: "กรอกข้อมูลการสมัครสินเชื่อ",
							meta: { pageId: PageId.SELECT_INSTALLMENT },
							next: ["2.1.4"],
							back: "2.1.2",
						},
						{
							stepId: "2.1.4",
							stepName: "กรอกข้อมูลการสมัครสินเชื่อ",
							stepDescription: "แนบเอกสารหลักฐาน",
							meta: { pageId: PageId.ATTACH_DOCUMENTS },
							back: "2.1.3",
							next: ["2.1.5"],
						},
						{
							stepId: "2.1.5",
							stepName: "กรอกข้อมูลการสมัครสินเชื่อ",
							stepDescription: "ตรวจสอบข้อมูล",
							meta: { pageId: PageId.REVIEW_PRE_SUBMIT },
							back: "2.1.4",
							next: ["2.1.6"],
						},
						{
							stepId: "2.1.6",
							stepName: "กรอกข้อมูลการสมัครสินเชื่อ",
							stepDescription: "บันทึกข้อมูลใบสมัครสินเชื่อแล้ว",
							meta: { pageId: PageId.LOAN_SUBMISSION_CONFIRMATION },
							next: ["3.1"],
							back: "2.1.5",
						},
					],
				},
			],
		},
		{
			stepId: "3",
			stepName: "ยืนยันและส่งข้อมูล",
			children: [
				{
					stepId: "3.1",
					stepName: "ข้อกำหนดและเงื่อนไขการใช้บริการ สตาร์ มันนี่ สมาร์ทเซลล์",
					stepDescription:
						"ข้อกำหนดและเงื่อนไขการใช้ศึกษา ทำความเข้าใจ และให้การยอมรับ ข้อกำหนด และเงื่อนไขการใช้บริการ เพื่อดำเนินการต่อ",
					next: ["3.2"],
					back: null,
					children: [
						{
							stepId: "3.1.1",
							stepName: "ข้อกำหนดและเงื่อนไขการใช้บริการ สตาร์ มันนี่ สมาร์ทเซลล์",
							meta: { pageId: PageId.CUSTOMER_CONFIRM_GUIDELINE },
							next: ["3.1.2"],
							back: null,
						},
						{
							stepId: "3.1.2",
							stepName: "ข้อกำหนดและเงื่อนไขการใช้บริการ สตาร์ มันนี่ สมาร์ทเซลล์",
							meta: {
								pageId: PageId.TERMS_CONDITIONS,
								code: "terms_and_conditions",
							},
							back: "1.3.1",
							next: ["3.2"],
						},
					],
				},
				{
					stepId: "3.2",
					stepName: "ยืนยันเบอร์โทรศัพท์มือถือ",
					stepDescription: "ทำยืนยันรหัส OTP ที่ได้จากระบบ",
					meta: { pageId: PageId.OTP_PHONE },
					back: null,
					next: ["3.3"],
				},
				{
					stepId: "3.3",
					stepName: "ยืนยันอีเมลแอดเดรส (ถ้ามี)",
					stepDescription: "ทำยืนยันรหัส OTP ที่ได้จากระบบ",
					meta: { pageId: PageId.OTP_EMAIL },
					back: null,
					next: ["3.4"],
				},
				{
					stepId: "3.4",
					stepName: "ให้ความยินยอม การเก็บและใช้ข้อมูลส่วนบุคคล",
					stepDescription:
						"เลือกช่องทางที่ท่านยินยอมให้บริษัทฯ ทำการติดต่อท่านได้ และ เลือกผลิตภัณฑ์ที่ท่านสนใจรับข่าวสารหรือโปรโมชัน",
					meta: { pageId: PageId.CONSENT, code: "consent_personal_data" },
					back: null,
					next: ["3.5"],
				},
				{
					stepId: "3.5",
					stepName: "ให้ความยินยอม การเก็บและใช้ข้อมูลส่วนชีวมิติ",
					stepDescription: "ศึกษา ทำความเข้าใจรายละเอียด และให้ความยินยอมเพื่อดำเนินการต่อ",
					meta: {
						pageId: PageId.TERMS_CONDITIONS,
						code: "bio_terms_and_conditions",
					},
					back: null,
					next: ["3.6"],
				},
				{
					stepId: "3.6",
					stepName: "ถ่ายรูปใบหน้า",
					stepDescription: "ถ่ายรูปใบหน้าของลูกค้า เพื่อยืนยันความเป็นเจ้าของข้อมูล",
					meta: { pageId: PageId.FACE_PHOTO },
					back: null,
					next: ["4.1"],
					subflow: {
						id: "face-detection-subflow",
						start: "face-detection.1",
						returnTo: "3.6",
						steps: [
							{
								stepId: "face-detection.1",
								stepName: "คำแนะนำการถ่ายรูป",
								meta: { pageId: PageId.TAKE_FACE_PHOTO_GUIDE },
								next: ["face-detection.2"],
								back: null,
							},
							{
								stepId: "face-detection.2",
								stepName: "ตรวจสอบกล้อง",
								meta: { pageId: PageId.WEBCAM_CHECK },
								next: ["face-detection.3"],
								back: "face-detection.1",
							},
							{
								stepId: "face-detection.3",
								stepName: "ถ่ายรูปใบหน้า",
								meta: { pageId: PageId.TAKE_FACE_PHOTO },
								next: ["face-detection.4"],
								back: "face-detection.2",
							},
							{
								stepId: "face-detection.4",
								stepName: "ยืนยันรูปถ่าย",
								meta: { pageId: PageId.CONFIRM_FACE_PHOTO },
								next: [],
								back: "face-detection.3",
							},
						],
					},
				},
			],
		},
		{
			stepId: "4",
			stepName: "ตรวจสอบข้อมูลการสมัครสินเชื่อ",
			children: [
				{
					stepId: "4.1",
					stepName: "ตรวจสอบข้อมูลการสมัครสินเชื่อ",
					stepDescription:
						"ทำการตรวจทานข้อมูลการสมัครอีกครั้ง หลังจากทำการยืนยันข้อมูลแล้ว ท่านจะไม่สามารถแก้ไขรายละเอียดได้อีกครั้ง",
					meta: { pageId: PageId.REVIEW_FINAL_SUBMIT },
					back: null,
					next: ["4.2"],
				},
				{
					stepId: "4.2",
					stepName: "ให้ความยินยอมเปิดเผยข้อมูลโดย วิธีการผ่านอินเทอร์เน็ต",
					stepDescription: "ศึกษา ทำความเข้าใจรายละเอียด และให้ความยินยอมเพื่อดำเนินการต่อ",
					meta: {
						pageId: PageId.TERMS_CONDITIONS,
						code: "consent_disclosure_info_via_internet",
					},
					back: null,
					next: ["4.3"],
				},
				{
					stepId: "4.3",
					stepName: "ยืนยันรหัส OTP",
					stepDescription: "ยืนยันรหัส OTP ที่ส่งไปยังเบอร์โทรศัพท์ที่ลงทะเบียนไว้ เพื่อยืนยันการยิยอมให้เปิดเผยข้อมูล",
					meta: {
						isDialog: true, // เพิ่ม flag เพื่อบอกว่าเป็น dialog
						handledBy: "4.2" // บอกว่าถูกจัดการโดย step 4.2
					},
					back: null,
					next: [],
				},
			],
		},
	],
};
