/**
 * Workflow event types and interfaces
 */

/**
 * Workflow command types
 */
export type WorkflowCommand =
	| "initialize"
	| "start"
	| "next"
	| "back"
	| "close"
	| "start_subflow"
	| "next_subflow"
	| "back_subflow"
	| "close_subflow"
	| "resume"
	| "jump_to"
	| "error";

/**
 * Workflow event interface
 */
export interface WorkflowEvent {
	command: WorkflowCommand;
	targetStepId?: string;
	payload?: any; // อาจเป็น state/form data/validation info
	fromStepId?: string;
}

/**
 * Modal state interface
 */
export interface ModalState {
	isOpen: boolean;
	component: any;
	componentProps?: Record<string, any>;
	isSubflow: boolean;
	parentModalId?: string;
}

/**
 * Error event payload
 */
export interface ErrorEventPayload {
	message: string;
	code?: string;
	details?: any;
}
