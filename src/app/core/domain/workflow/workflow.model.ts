/**
 * Workflow model interfaces
 * Defines the structure of workflow configurations
 */

/**
 * Page IDs enum for all pages in the application
 */
export enum PageId {
	// Loan application pages
	LOAN_TYPE = "loan-type",
	CUSTOMER_TYPE = "customer-type",
	READ_ID_CARD_GUIDELINE = "read-id-card-guideline",
	READ_ID_CARD_QR = "read-id-card-qr",
	PERSONAL_INFO = "personal-info",
	WEBCAM_CHECK = "webcam-check",
	ADDRESS_CONTACT = "address-contact",
	WORK_INFO = "work-info",
	SELECT_INSTALLMENT = "select-installment",
	ATTACH_DOCUMENTS = "attach-documents",
	REVIEW_PRE_SUBMIT = "review-pre-submit",
	LOAN_SUBMISSION_CONFIRMATION = "loan-submission-confirmation",
	CUSTOMER_CONFIRM_GUIDELINE = "customer-confirm-guideline",
	TERMS_CONDITIONS = "terms-conditions",
	OTP_PHONE = "otp-phone",
	OTP_EMAIL = "otp-email",
	CONSENT = "consent",
	REVIEW_FINAL_SUBMIT = "review-final",

	// ID photo pages
	ID_PHOTO = "id-photo",
	TAKE_ID_PHOTO_GUIDE = "take-id-photo-guide",
	TAKE_ID_PHOTO = "take-id-photo",
	CONFIRM_ID_PHOTO = "confirm-id-photo",

	// Face detection pages
	FACE_PHOTO = "face-photo",
	TAKE_FACE_PHOTO_GUIDE = "take-face-photo-guide",
	TAKE_FACE_PHOTO = "take-face-photo",
	CONFIRM_FACE_PHOTO = "confirm-face-photo",
}

/**
 * Navigation condition for conditional routing
 */
export interface NavigationCondition {
	condition: string;
	goTo: string;
	forceCompleteParent?: boolean;
}

/**
 * Simple navigation to next step
 */
export type SimpleNavigation = string;

/**
 * Combined navigation type - can be either a simple string or a condition
 */
export type Navigation = SimpleNavigation | NavigationCondition;

/**
 * Step metadata
 */
export interface StepMeta {
	pageId: PageId;
	[key: string]: any; // Additional metadata properties
}

/**
 * Subflow definition
 */
export interface Subflow {
	id: string;
	start: string;
	returnTo: string;
	steps: WorkflowStep[];
}

/**
 * Main workflow definition
 */
export interface Workflow {
	id: string;
	name: string;
	version: string;
	start: string;
	createdAt: string;
	steps: WorkflowStepGroup[];
}

/**
 * Represents a group of workflow steps
 */
export interface WorkflowStepGroup {
	stepId: string;
	stepName: string;
	stepDescription?: string;
	children: WorkflowStep[];
}

/**
 * Represents a single step in the workflow
 */
export interface WorkflowStep {
	stepId: string;
	stepName: string;
	stepDescription?: string;
	meta?: WorkflowStepMeta;
	next?: (string | WorkflowCondition)[];
	back?: string | null;
	children?: WorkflowStep[]; // Multi-page within a single step
	subflow?: WorkflowSubflow; // Separate workflow that opens in a nested modal
}

/**
 * Represents metadata for a workflow step
 */
export interface WorkflowStepMeta {
	pageId: string;
	[key: string]: any; // Allow for additional metadata properties
}

/**
 * Represents a subflow in the workflow
 */
export interface WorkflowSubflow {
	id: string;
	start: string;
	returnTo: string;
	steps: WorkflowStep[]; // Steps within the subflow
}

/**
 * Represents a field condition for workflow navigation
 */
export interface FieldCondition {
	field: string;
	operator: string;
	value: string | number | boolean;
}

/**
 * Represents a condition for workflow navigation
 */
export interface WorkflowCondition {
	condition: string | FieldCondition;
	goTo: string;
	forceCompleteParent?: boolean; // Optional flag to force mark parent step as completed when jumping to a different step group
}
