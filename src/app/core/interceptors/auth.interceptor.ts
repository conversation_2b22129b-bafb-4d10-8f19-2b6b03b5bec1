import { HttpInterceptorFn } from "@angular/common/http";
import { inject } from "@angular/core";

/**
 * Auth interceptor function
 * This interceptor adds the Authorization header to requests if a token is available
 */
export const authInterceptor: HttpInterceptorFn = (req, next) => {
	// Try to get the auth session from localStorage
	const authSessionStr = localStorage.getItem("auth_session");

	if (authSessionStr) {
		try {
			// Parse the auth session
			const authSession = JSON.parse(authSessionStr);

			// Check if we have an access token and it's not expired
			if (
				authSession.access_token &&
				authSession.expires_at > Math.floor(Date.now() / 1000)
			) {
				// Clone the request and add the Authorization header
				const authReq = req.clone({
					setHeaders: {
						Authorization: `${authSession.token_type} ${authSession.access_token}`,
					},
				});

				// Log for debugging (remove in production)
				console.log("Adding auth header to request:", req.url);

				// Continue with the modified request
				return next(authReq);
			}
		} catch (error) {
			console.error("Error parsing auth session:", error);
		}
	}

	// If no token or error, continue with the original request
	return next(req);
};
