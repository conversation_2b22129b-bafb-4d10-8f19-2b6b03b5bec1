import { Provider } from "@angular/core";
import {
	ADDRESS_USE_CASE,
	CONTACT_USE_CASE,
	DOCUMENT_USE_CASE,
	LOAN_USE_CASE,
	PRODUCT_USE_CASE,
	TERM_USE_CASE,
	USER_USE_CASE,
	WORKING_USE_CASE,
} from "@ports/in/injection-token";
import {
	ADDRESS_REPOSITORY,
	CONTACT_REPOSITORY,
	DOCUMENT_REPOSITORY,
	LOAN_REPOSITORY,
	PRODUCT_REPOSITORY,
	TERM_REPOSITORY,
	USER_REPOSITORY,
	WORKING_REPOSITORY,
} from "@ports/out/injection-token";
import { AddressHttpRepository } from "src/app/adapters/secondary/address-http.repository";
import { DocumentHttpRepository } from "src/app/adapters/secondary/document-http.repository";
import { LoanHttpRepository } from "src/app/adapters/secondary/loan-http.repository";
import { ProductHttpRepository } from "src/app/adapters/secondary/product-http.repository";
import { TermHttpRepository } from "src/app/adapters/secondary/term-http.repository";
import { UserHttpRepository } from "src/app/adapters/secondary/user-http.repository";
import { WorkingHttpRepository } from "src/app/adapters/secondary/working-http.repository";
import { AddressUseCase } from "../application/use-cases/address-usecase";
import { DocumentUseCase } from "../application/use-cases/document-usecase";
import { LoanUseCase } from "../application/use-cases/loan-usecase";
import { ProductUseCase } from "../application/use-cases/product-usecase";
import { TermUseCase } from "../application/use-cases/term-usecase";
import { UserUseCase } from "../application/use-cases/user-usecase";
import { WorkingUseCase } from "../application/use-cases/working-usecase";
import { ContactHttpRepository } from "src/app/adapters/secondary/contact-http.repository";
import { ContactUseCase } from "../application/use-cases/contact-usecase";

/**
 * Providers for loan-related dependencies
 * These providers are used to configure the dependency injection for loan-related functionality
 */
export const loanProviders: Provider[] = [
	// Repository
	{
		provide: LOAN_REPOSITORY,
		useClass: LoanHttpRepository,
	},

	// Consolidated use case
	{
		provide: LOAN_USE_CASE,
		useFactory: (repo: LoanHttpRepository) => new LoanUseCase(repo),
		deps: [LOAN_REPOSITORY],
	},

	// User repository
	{
		provide: USER_REPOSITORY,
		useClass: UserHttpRepository,
	},

	// User use case
	{
		provide: USER_USE_CASE,
		useFactory: (repo: UserHttpRepository) => new UserUseCase(repo),
		deps: [USER_REPOSITORY],
	},

	// Address repository
	{
		provide: ADDRESS_REPOSITORY,
		useClass: AddressHttpRepository,
	},

	// Address use case
	{
		provide: ADDRESS_USE_CASE,
		useFactory: (repo: AddressHttpRepository) => new AddressUseCase(repo),
		deps: [ADDRESS_REPOSITORY],
	},

	// Working repository
	{
		provide: WORKING_REPOSITORY,
		useClass: WorkingHttpRepository,
	},

	// Working use case
	{
		provide: WORKING_USE_CASE,
		useFactory: (repo: WorkingHttpRepository) => new WorkingUseCase(repo),
		deps: [WORKING_REPOSITORY],
	},

	// Product repository
	{
		provide: PRODUCT_REPOSITORY,
		useClass: ProductHttpRepository,
	},

	// Product use case
	{
		provide: PRODUCT_USE_CASE,
		useFactory: (repo: ProductHttpRepository) => new ProductUseCase(repo),
		deps: [PRODUCT_REPOSITORY],
	},

	// Document repository
	{
		provide: DOCUMENT_REPOSITORY,
		useClass: DocumentHttpRepository,
	},

	// Document use case
	{
		provide: DOCUMENT_USE_CASE,
		useFactory: (repo: DocumentHttpRepository) => new DocumentUseCase(repo),
		deps: [DOCUMENT_REPOSITORY],
	},

	// Term repository
	{
		provide: TERM_REPOSITORY,
		useClass: TermHttpRepository,
	},

	// Term use case
	{
		provide: TERM_USE_CASE,
		useFactory: (repo: TermHttpRepository) => new TermUseCase(repo),
		deps: [TERM_REPOSITORY],
	},

	// Contact repository
	{
		provide: CONTACT_REPOSITORY,
		useClass: ContactHttpRepository,
	},

	// Contact use case
	{
		provide: CONTACT_USE_CASE,
		useFactory: (repo: ContactHttpRepository) => new ContactUseCase(repo),
		deps: [CONTACT_REPOSITORY],
	},
];
