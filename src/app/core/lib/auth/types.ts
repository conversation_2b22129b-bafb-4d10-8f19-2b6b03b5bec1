export type AuthFlowType = "implicit" | "pkce";

export type GoTrueClientOptions = {
	client_id: string;
	/* The URL of the GoTrue server. */
	url?: string;
	/* Any additional headers to send to the GoTrue server. */
	headers?: { [key: string]: string };
	/* Optional key name used for storing tokens in local storage. */
	storageKey?: string;
	/* Set to "true" if you want to automatically detects OAuth grants in the URL and signs in the user. */
	detectSessionInUrl?: boolean;
	/* Set to "true" if you want to automatically refresh the token before expiring. */
	autoRefreshToken?: boolean;
	/* Set to "true" if you want to automatically save the user session into local storage. If set to false, session will just be saved in memory. */
	persistSession?: boolean;
	/* Provide your own local storage implementation to use instead of the browser's local storage. */
	// storage?: SupportedStorage
	/* A custom fetch implementation. */
	// fetch?: Fetch
	/* If set to 'pkce' PKCE flow. Defaults to the 'implicit' flow otherwise */
	flowType?: AuthFlowType;
	/* If debug messages are emitted. Can be used to inspect the behavior of the library. If set to a function, the provided function will be used instead of `console.log()` to perform the logging. */
	debug?: boolean | ((message: string, ...args: any[]) => void);
	/**
	 * Provide your own locking mechanism based on the environment. By default no locking is done at this time.
	 *
	 * @experimental
	 */
	// lock?: LockFunc
};
