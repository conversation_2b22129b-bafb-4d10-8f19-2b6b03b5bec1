import dayjs from "dayjs";
import { StarmoneyService } from "../../application/starmoney.service";

interface Session {
	access_token: string;
	refresh_token: string;
	token_type: string;
	expires_at: number;
}

export default class AuthClient {
	constructor(private starmoneyService: StarmoneyService) {}
	public async signInAnonymous(txn_id: string) {
		try {
			const response = await this.starmoneyService.signInAnonymous({ txn_id });
			if (response.statusCode === 201 && response.data !== null) {
				const session = response.data.session;
				const newSession: Session = {
					access_token: session.access_token,
					refresh_token: session.refresh_token,
					token_type: session.token_type,
					expires_at: dayjs().unix() + session.expires_in,
				};

				// Update local storage with the new session
				localStorage.setItem("auth_session", JSON.stringify(newSession));
			} else {
				console.error(response?.message);
			}
		} catch (error: any) {
			console.error("Error:", error);
		}
	}

	/**
	 * Get the current session from local storage.
	 */
	async getSession(): Promise<{
		data: { session: Session | null };
		error: any;
	}> {
		try {
			const session: any = localStorage.getItem("auth_session");
			const parsedSession: Session = JSON.parse(session);
			if (!parsedSession?.access_token) {
				throw new Error("No session from local storage");
			}

			// Check if the token is expired
			if (this.isTokenExpired(parsedSession.expires_at)) {
				const refreshedSession = await this.refreshToken(
					parsedSession.refresh_token,
				);
				return { data: { session: refreshedSession }, error: null };
			}

			return { data: { session: parsedSession }, error: null };
		} catch (error) {
			throw error;
		}
	}

	/**
	 * Check if the token is expired.
	 */
	private isTokenExpired(expiresAt: number): boolean {
		const now = dayjs().unix();
		return expiresAt <= now;
	}

	/**
	 * Refresh the access token using the refresh token.
	 */
	private async refreshToken(refreshToken: string): Promise<Session | null> {
		try {
			const response = await this.starmoneyService.refreshToken({
				txn_id: refreshToken,
			});
			if (response.statusCode === 201 && response.data !== null) {
				const session = response.data.session;
				const newSession: Session = {
					access_token: session.access_token,
					refresh_token: session.refresh_token,
					token_type: session.token_type,
					expires_at: dayjs().unix() + session.expires_in,
				};
				// Update local storage with the new session
				localStorage.setItem("auth_session", JSON.stringify(newSession));
				return newSession;
			}
			return null;
		} catch (error: any) {
			console.error("Error refreshing token:", error);
			const message = error?.error.message || "";
			if (message === "Invalid Refresh Token: Already Used") {
				localStorage.removeItem("auth_session");
				window.location.reload();
				throw new Error("Invalid Refresh Token: Already Used");
			} else {
				throw new Error("No session returned from refresh token");
			}
		}
	}
}
