import { HttpErrorResponse } from "@angular/common/http";
import { computed, effect, inject, Injectable, signal } from "@angular/core";
import { LoanCategory } from "@domain/models/loan.model";
import { AuthService } from "./auth.service";
import { StarmoneyService } from "./starmoney.service";
import { AppUtilsService } from "./app-utils.service";

export interface MenuCategory {
	categoryId: number;
	label: string;
	disabled?: boolean;
	children: PrimaryMenuItem[];
}

/**
 * Primary menu item interface
 */
export interface PrimaryMenuItem {
	id: string;
	label: string;
	icon?: {
		inactive?: string;
		active?: string;
	};
	route?: string;
	permission?: string;
	disabled?: boolean;
	secondaryMenus: SecondaryMenuItem[];
}

/**
 * Secondary menu item interface
 */
export interface SecondaryMenuItem {
	id: number;
	mappedId: string;
	label: string;
	description?: string;
	icon?: string;
	route?: string;
	permission?: string;
	parentId: string; // Reference to parent primary menu
}

/**
 * Navigation state interface
 */
export interface NavigationState {
	menuCategories: MenuCategory[];
	activeMenuCategory: MenuCategory | null;
	activePrimaryMenu: PrimaryMenuItem | null;
	activeSecondaryMenu: SecondaryMenuItem | null;
	isLoading: boolean;
	error: string | null;
}

@Injectable({
	providedIn: "root",
})
export class DashboardService {
	// Primary menus data
	private readonly LOAN_PRIMARY_ID = "loan";

	// Initial navigation state
	private initialMenusState: MenuCategory[] = [
		{
			categoryId: 1,
			label: "เมนูการทำรายการ",
			children: [
				{
					id: "loan",
					label: "สมัครสินเชื่อ",
					icon: {
						inactive: "assets/svgs/inactive/devices.svg",
						active: "assets/svgs/active/devices.svg",
					},
					route: "/loan",
					permission: "loan",
					secondaryMenus: [],
				},
			],
		},
		{
			categoryId: 2,
			label: "เมนูการตั้งค่า",
			children: [
				{
					id: "sign-document",
					label: "Sign Document",
					icon: {
						inactive: "assets/svgs/inactive/draw.svg",
						active: "assets/svgs/active/draw.svg",
					},
					route: "/sign-document",
					permission: "sign-document",
					disabled: false,
					secondaryMenus: [],
				},
			],
		},
	];

	private initialState: NavigationState = {
		menuCategories: this.initialMenusState,
		activeMenuCategory: null,
		activePrimaryMenu: null,
		activeSecondaryMenu: null,
		isLoading: false,
		error: null,
	};

	// Starmoney Onboarding Service API
	private starmoneyService = inject(StarmoneyService);
	private authService = inject(AuthService);
	private appUtilsService = inject(AppUtilsService);

	// Navigation state signal
	private state = signal<NavigationState>({ ...this.initialState });
	// Readonly signals for components to consume
	readonly menuCategories = computed(() => this.state().menuCategories);
	readonly activeMenuCategory = computed(() => this.state().activeMenuCategory);
	readonly activePrimaryMenu = computed(() => this.state().activePrimaryMenu);
	readonly activeSecondaryMenu = computed(() => this.state().activeSecondaryMenu);
	readonly secondaryMenus = computed(() => {
		const active = this.state().activePrimaryMenu;
		return active ? active.secondaryMenus : [];
	});

	readonly isLoading = computed(() => this.state().isLoading);
	readonly error = computed(() => this.state().error);

	constructor() {
		effect(() => {
			console.log("Active menu category:", this.activeMenuCategory());
			console.log("Active primary menu:", this.activePrimaryMenu());
			console.log("Active secondary menu:", this.activeSecondaryMenu());
			console.log("Secondary menus:", this.secondaryMenus());
		});
	}

	public selectMenuCategory(menuCategory: MenuCategory): void {
		this.state.update((state) => ({
			...state,
			activeMenuCategory: menuCategory,
		}));
	}

	/**
	 * Select a primary menu by ID
	 * @param menuId Primary menu ID
	 * @returns boolean indicating if selection was successful
	 */
	async selectPrimaryMenu(menu: PrimaryMenuItem): Promise<boolean> {
		try {
			// if primary selected loan, do fetch loan categories
			if (menu.id === this.LOAN_PRIMARY_ID) {
				// start transaction before fetching loan categories
				await this.onStartKycTransaction();
				// fetch loan categories
				await this.fetchLoanCategories();
			}

			// Then update navigation state
			const primaryMenu = this.getPrimaryMenuById(menu.id);
			if (!primaryMenu) {
				return false;
			}

			// update navigation state
			this.state.update((state) => ({
				...state,
				activePrimaryMenu: primaryMenu,
				activeSecondaryMenu: null,
			}));

			// If primary menu has secondary menus, default to the first one
			if (primaryMenu.secondaryMenus.length > 0) {
				this.selectSecondaryMenu(primaryMenu.secondaryMenus[0].mappedId);
			}

			return true;
		} catch (error) {
			await this.handleError(error, "Failed to select primary menu");
			return false;
		}
	}

	async onStartKycTransaction() {
		const txnId = this.authService.getTxnId();
		if (!txnId) {
			console.error("No transaction ID available");
			return;
		}

		await this.starmoneyService.startKycTransaction({
			txn_id: txnId,
		});
	}

	getPrimaryMenuById(id: string): PrimaryMenuItem | undefined {
		return this.state()
			.menuCategories.flatMap((menu) => menu.children)
			.find((item) => item.id === id);
	}

	selectSecondaryMenu(menuId: string): boolean {
		const activePrimary = this.state().activePrimaryMenu;
		if (!activePrimary) {
			return false;
		}

		const secondaryMenu = activePrimary.secondaryMenus.find((menu) => menu.mappedId === menuId);
		if (!secondaryMenu) {
			return false;
		}

		this.state.update((state) => ({
			...state,
			activeSecondaryMenu: secondaryMenu,
		}));

		return true;
	}

	async fetchLoanCategories() {
		try {
			// Set loading state
			this.state.update((state) => ({
				...state,
				isLoading: true,
				error: null,
			}));

			const apiResponse = await this.starmoneyService.getLoanCategories();
			if (apiResponse.isSuccess() && apiResponse.hasData()) {
				this.updateLoanMenuItems(apiResponse.data);
			} else {
				console.error("Invalid response format or empty data", apiResponse);
				this.state.update((state) => ({
					...state,
					error: "Invalid or empty loan categories data",
				}));
			}
		} catch (error) {
			console.error("Error fetching loan categories:", error);
			this.state.update((state) => ({
				...state,
				error: error instanceof Error ? error.message : "Failed to fetch loan categories",
			}));
		} finally {
			// Reset loading state
			this.state.update((state) => ({
				...state,
				isLoading: false,
			}));
		}
	}

	private updateLoanMenuItems(categories: LoanCategory[]) {
		const LoanIconMap: Record<number, string> = {
			2: "assets/svgs/hire-purchase.svg",
			6: "assets/svgs/personal-loan.svg",
		};

		const LoanMenuIdMap: Record<number, string> = {
			2: "hire-purchase",
			6: "personal-loan",
		};

		const menuItems: SecondaryMenuItem[] = categories.map((category) => ({
			id: category.id,
			mappedId: LoanMenuIdMap[category.id],
			label: category.name,
			description: category.description,
			icon: LoanIconMap[category.id],
			parentId: this.LOAN_PRIMARY_ID,
		}));

		// set menu items to loan menu items
		// find menu category with id 1
		const loanMenuCategory = this.getMenuCategoryById(1);
		if (loanMenuCategory) {
			loanMenuCategory.children.forEach((menu) => {
				if (menu.id === this.LOAN_PRIMARY_ID) {
					menu.secondaryMenus = menuItems;
				}
			});
		}

		// if active menu category is loan, select secondary menu automatically
		const activeMenuCategory = this.activeMenuCategory();
		if (activeMenuCategory?.categoryId === 1 && menuItems.length > 0) {
			this.selectSecondaryMenu(menuItems[0].mappedId);
		}

		console.log("Updated loan menu items:", this.state().menuCategories);
		console.log("Active primary menu:", this.state().activeMenuCategory);
		console.log("Active secondary menu:", this.state().activeSecondaryMenu);
	}

	getMenuCategoryById(id: number): MenuCategory | undefined {
		return this.state().menuCategories.find((menu) => menu.categoryId === id);
	}

	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage ? `${errorMessage} : ${apiMessage}` : errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
