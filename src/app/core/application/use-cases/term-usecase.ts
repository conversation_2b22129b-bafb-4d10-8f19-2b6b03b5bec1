import { AcceptTermsRequestDto } from "@domain/dto/terms/accept-term.dto";
import {
    GetTermRequestDto,
    GetTermResponseDto,
} from "@domain/dto/terms/get-term.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { TermUseCasePort } from "@ports/in/term.port";
import { TermRepositoryPort } from "@ports/out/term-repository.port";

export class TermUseCase implements TermUseCasePort {
	constructor(private repo: TermRepositoryPort) {}

	getTerm(
		request: GetTermRequestDto,
	): Promise<ApiResponse<GetTermResponseDto>> {
		return this.repo.getTerm(request);
	}

	acceptTerm(request: AcceptTermsRequestDto): Promise<ApiResponse<boolean>> {
		return this.repo.acceptTerm(request);
	}
}
