import { ConfirmProductRequestDto } from "@domain/dto/products/confirm-product.dto";
import {
	GetProductListRequestDto,
	GetProductListResponseDto,
} from "@domain/dto/products/get-product-list.dto";
import {
	GetProductRequestDto,
	GetProductResponseDto,
} from "@domain/dto/products/get-product.dto";
import { SetProductRequestDto } from "@domain/dto/products/set-product.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { ProductUseCasePort } from "@ports/in/product.port";
import { ProductRepositoryPort } from "@ports/out/product-repository.port";

export class ProductUseCase implements ProductUseCasePort {
	constructor(private repo: ProductRepositoryPort) {}

	getProductList(
		request: GetProductListRequestDto,
	): Promise<ApiResponse<GetProductListResponseDto[]>> {
		return this.repo.getProductList(request);
	}

	setProduct(request: SetProductRequestDto): Promise<ApiResponse<boolean>> {
		return this.repo.setProduct(request);
	}

	getProduct(
		request: GetProductRequestDto,
	): Promise<ApiResponse<GetProductResponseDto>> {
		return this.repo.getProduct(request);
	}

	confirmProduct(request: ConfirmProductRequestDto): Promise<ApiResponse<any>> {
		return this.repo.confirmProduct(request);
	}
}
