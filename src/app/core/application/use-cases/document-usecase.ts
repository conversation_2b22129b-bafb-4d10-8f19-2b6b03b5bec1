import {
	GetUploadDocsRequestDto,
	GetUploadDocsResponseDto,
} from "@domain/dto/document/get-upload-docs.dto";
import {
	UploadDocRequestDto,
	UploadDocResponseDto,
} from "@domain/dto/document/upload-doc.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { DocumentUseCasePort } from "@ports/in/document.port";
import { DocumentRepositoryPort } from "@ports/out/document-repository.port";

export class DocumentUseCase implements DocumentUseCasePort {
	constructor(private repo: DocumentRepositoryPort) {}

	getUploadDocuments(
		request: GetUploadDocsRequestDto,
	): Promise<ApiResponse<GetUploadDocsResponseDto>> {
		return this.repo.getUploadDocuments(request);
	}

	uploadDocument(
		request: UploadDocRequestDto,
	): Promise<ApiResponse<UploadDocResponseDto>> {
		return this.repo.uploadDocument(request);
	}
}
