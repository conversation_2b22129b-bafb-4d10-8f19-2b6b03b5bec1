import {
	GetAddressInfoRequestDto,
	GetAddressInfoResponseDto,
} from "@domain/dto/address/get-address-info.dto";
import {
	GetDistrictRequestDto,
	GetDistrictResponseDto,
} from "@domain/dto/address/get-district.dto";
import {
	GetHousingTypeRequestDto,
	GetHousingTypeResponseDto,
} from "@domain/dto/address/get-housing-type.dto";
import {
	GetPostcodeRequestDto,
	GetPostcodeResponseDto,
} from "@domain/dto/address/get-postcode.dto";
import {
	GetProvinceRequestDto,
	GetProvinceResponseDto,
} from "@domain/dto/address/get-province.dto";
import {
	GetSubDistrictRequestDto,
	GetSubDistrictResponseDto,
} from "@domain/dto/address/get-sub-district.dto";
import { UpdateAddressInfoRequestDto } from "@domain/dto/address/update-address-info.dto";
import { UpdateHousingTypeRequest } from "@domain/dto/address/update-housing-type.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { AddressUseCasePort } from "@ports/in/address-usecase.port";
import { AddressRepositoryPort } from "@ports/out/address-repository.port";

export class AddressUseCase implements AddressUseCasePort {
	constructor(private repo: AddressRepositoryPort) {}

	async getAddressInfo(
		request: GetAddressInfoRequestDto,
	): Promise<ApiResponse<GetAddressInfoResponseDto[]>> {
		return await this.repo.getAddressInfo(request);
	}

	async getProvinces(
		request: GetProvinceRequestDto,
	): Promise<ApiResponse<GetProvinceResponseDto[]>> {
		return await this.repo.getProvinces(request);
	}

	async getDistricts(
		request: GetDistrictRequestDto,
	): Promise<ApiResponse<GetDistrictResponseDto[]>> {
		return await this.repo.getDistricts(request);
	}

	async getSubDistricts(
		request: GetSubDistrictRequestDto,
	): Promise<ApiResponse<GetSubDistrictResponseDto[]>> {
		return await this.repo.getSubDistricts(request);
	}

	async getPostalCodes(
		request: GetPostcodeRequestDto,
	): Promise<ApiResponse<GetPostcodeResponseDto[]>> {
		return await this.repo.getPostalCodes(request);
	}

	async getHousingTypes(
		request: GetHousingTypeRequestDto,
	): Promise<ApiResponse<GetHousingTypeResponseDto[]>> {
		return await this.repo.getHousingTypes(request);
	}

	updateAddressInfo(
		request: UpdateAddressInfoRequestDto,
	): Promise<ApiResponse<GetAddressInfoResponseDto>> {
		return this.repo.updateAddressInfo(request);
	}
	updateHousingType(
		request: UpdateHousingTypeRequest,
	): Promise<ApiResponse<GetHousingTypeResponseDto>> {
		return this.repo.updateHousingType(request);
	}
}
