import {
	GetOccupationGroupRequestDto,
	GetOccupationGroupResponseDto,
} from "@domain/dto/working/get-occupation-group.dto";
import {
	GetOccupationRequestDto,
	GetOccupationResponseDto,
} from "@domain/dto/working/get-occupation.dto";
import {
	GetWorkingInfoRequestDto,
	GetWorkingInfoResponseDto,
} from "@domain/dto/working/get-working-info.dto";
import {
	UpdateWorkingInfoRequestDto,
	UpdateWorkingInfoResponseDto,
} from "@domain/dto/working/update-working-info.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { WorkingUseCasePort } from "@ports/in/working.port";
import { WorkingRepositoryPort } from "@ports/out/working-repository.port";

export class WorkingUseCase implements WorkingUseCasePort {
	constructor(private repo: WorkingRepositoryPort) {}

	/**
	 * Sign in anonymously
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing authentication data
	 */
	async getWorkingInfo(
		request: GetWorkingInfoRequestDto,
	): Promise<ApiResponse<GetWorkingInfoResponseDto>> {
		return this.repo.getWorkingInfo(request);
	}

	/**
	 * Update working information
	 * @param request The request containing transaction ID and working information
	 * @returns Promise of ApiResponse containing updated working information
	 */
	async updateWorkingInfo(
		request: UpdateWorkingInfoRequestDto,
	): Promise<ApiResponse<UpdateWorkingInfoResponseDto>> {
		return this.repo.updateWorkingInfo(request);
	}

	/**
	 * Get occupation groups
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing an array of occupation groups
	 */
	async getOccupationGroups(
		request: GetOccupationGroupRequestDto,
	): Promise<ApiResponse<GetOccupationGroupResponseDto[]>> {
		return this.repo.getOccupationGroups(request);
	}

	/**
	 * Get occupations
	 * @param request The request containing transaction ID and occupation group code
	 * @returns Promise of ApiResponse containing an array of occupations
	 */
	async getOccupations(
		request: GetOccupationRequestDto,
	): Promise<ApiResponse<GetOccupationResponseDto[]>> {
		return this.repo.getOccupations(request);
	}
}
