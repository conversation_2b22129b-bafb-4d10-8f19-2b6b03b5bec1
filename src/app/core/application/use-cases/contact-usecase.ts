import {
	GetContactRequestDto,
	GetContactResponseDto,
} from "@domain/dto/contact/get-contact.dto";
import {
	SendOtpRequestDto,
	SendOtpResponseDto,
} from "@domain/dto/contact/send-otp.dto";
import { UpdateContactRequestDto } from "@domain/dto/contact/update-contact.dto";
import {
	VerifyOtpRequestDto
} from "@domain/dto/contact/verify-otp.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { ContactUseCasePort } from "@ports/in/contact.port";
import { ContactRepositoryPort } from "@ports/out/contact-repository.port";

export class ContactUseCase implements ContactUseCasePort {
	constructor(private repo: ContactRepositoryPort) {}

	updateContact(
		request: UpdateContactRequestDto,
	): Promise<ApiResponse<boolean>> {
		return this.repo.updateContact(request);
	}

	getContact(
		request: GetContactRequestDto,
	): Promise<ApiResponse<GetContactResponseDto>> {
		return this.repo.getContact(request);
	}

	sendOtp(
		request: SendOtpRequestDto,
	): Promise<ApiResponse<SendOtpResponseDto>> {
		return this.repo.sendOtp(request);
	}

	verifyOtp(
		request: VerifyOtpRequestDto,
	): Promise<ApiResponse<boolean>> {
		return this.repo.verifyOtp(request);
	}
}
