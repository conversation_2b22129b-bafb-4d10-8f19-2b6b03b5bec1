import { TransactionIdDto } from "@domain/dto/transaction.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { UserUseCasePort } from "@ports/in/user.port";
import { UserRepositoryPort } from "@ports/out/user-repository.port";

/**
 * User use case implementation
 * This class implements the UserUseCasePort interface
 */
export class UserUseCase implements UserUseCasePort {
	constructor(private repo: UserRepositoryPort) {}

	/**
	 * Sign in anonymously
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing authentication data
	 */
	async signInAnonymous(
		request: TransactionIdDto,
	): Promise<ApiResponse<Record<string, unknown>>> {
		return this.repo.signInAnonymous(request);
	}

	/**
	 * Refresh token
	 * @param request The request containing transaction ID
	 * @returns Promise of ApiResponse containing refreshed token data
	 */
	async refreshToken(
		request: TransactionIdDto,
	): Promise<ApiResponse<Record<string, unknown>>> {
		return this.repo.refreshToken(request);
	}
}
