import { GetLoanCategoriesResponseDto } from "@domain/dto/loan/get-loan-categories.dto";
import {
	GetLoanTermRequestDto,
	GetLoanTermResponseDto,
} from "@domain/dto/loan/get-loan-term.dto";
import {
	GetLoanTypeRequestDto,
	GetLoanTypeResponseDto,
} from "@domain/dto/loan/get-loan-type.dto";
import { SetLoanTypeRequestDto } from "@domain/dto/loan/set-loan-type.dto";
import {
	StartKycTransactionRequestDto,
	StartKycTransactionResponseDto,
} from "@domain/dto/loan/start-transaction.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { LoanUseCasePort } from "@ports/in/loan-usecase.port";
import { LoanRepositoryPort } from "@ports/out/loan-repository.port";

/**
 * Consolidated use case for all loan-related operations
 * This class implements the LoanUseCasePort interface
 */
export class LoanUseCase implements LoanUseCasePort {
	constructor(private repo: LoanRepositoryPort) {}

	/**
	 * Start a KYC transaction
	 * @param request The transaction ID
	 * @returns Promise of an API response containing the transaction data
	 */
	async startKycTransaction(
		request: StartKycTransactionRequestDto,
	): Promise<ApiResponse<StartKycTransactionResponseDto>> {
		return this.repo.startKycTransaction(request);
	}

	/**
	 * Get all available loan categories
	 * @returns Promise of an API response containing an array of loan categories
	 */
	async getLoanCategories(): Promise<
		ApiResponse<GetLoanCategoriesResponseDto[]>
	> {
		return this.repo.getLoanCategories();
	}

	/**
	 * Get the loan type for a transaction
	 * @param transactionId The transaction ID
	 * @returns Promise of an API response containing the loan type
	 */
	async getLoanType(
		request: GetLoanTypeRequestDto,
	): Promise<ApiResponse<GetLoanTypeResponseDto>> {
		return this.repo.getLoanType(request);
	}

	/**
	 * Set the loan type for a transaction
	 * @param transactionId The transaction ID
	 * @param loanTypeId The loan type ID to set
	 * @returns Promise of an API response with void data
	 */
	async setLoanType(
		request: SetLoanTypeRequestDto,
	): Promise<ApiResponse<boolean>> {
		return this.repo.setLoanType(request);
	}

	/**
	 * Get all available loan terms for a transaction
	 * @param transactionId The transaction ID
	 * @returns Promise of an API response containing an array of loan terms
	 */
	async getLoanTerms(
		request: GetLoanTermRequestDto,
	): Promise<ApiResponse<GetLoanTermResponseDto>> {
		return this.repo.getLoanTerms(request);
	}
}
