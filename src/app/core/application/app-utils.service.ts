import { inject, Injectable } from "@angular/core";
import { ConfirmationService, MessageService } from "primeng/api";
import { BehaviorSubject } from "rxjs";
import { map } from "rxjs/operators";

export interface AlertDialogOptions {
	header: string;
	message: string;
	subMessage?: string;
	acceptLabel?: string;
	acceptCallback?: () => void;
	rejectLabel?: string;
	rejectCallback?: () => void;
	styleClass?: string;
}

@Injectable({
	providedIn: "root",
})
export class AppUtilsService {
	private loadingState = new BehaviorSubject<boolean>(false);
	private loadingMessage = new BehaviorSubject<string>("กำลังโหลด...");

	readonly isLoading$ = this.loadingState.asObservable().pipe(map((state) => state ?? false));
	readonly loadingMessage$ = this.loadingMessage.asObservable().pipe(map((msg) => msg ?? ""));
	private messageService = inject(MessageService);
	private confirmationService = inject(ConfirmationService);

	constructor() {}

	// Loading Methods
	async showLoading(message = "กำลังโหลด...") {
		this.loadingMessage.next(message);
		this.loadingState.next(true);
	}

	async hideLoading() {
		this.loadingState.next(false);
	}

	// Toast Methods
	async showInfo(message: string) {
		this.messageService.add({
			severity: "info",
			summary: "การดำเนินการ",
			detail: message,
			life: 3000,
		});
	}

	async showSuccess(message: string) {
		this.messageService.add({
			severity: "success",
			summary: "สำเร็จ",
			detail: message,
			life: 3000,
		});
	}

	async showError(title: string, message: string) {
		this.messageService.add({
			severity: "error",
			summary: title,
			detail: message,
			life: 3000,
		});
	}

	showRestApiDetail(
		severity: "success" | "error" | "info" | "warn",
		summary: string,
		statusCode: number,
		message: string,
		method: string,
		url: string,
	) {
		const timestamp = new Date().toLocaleTimeString();
		this.messageService.add({
			severity,
			summary,
			detail: JSON.stringify({ statusCode, message, method, url, timestamp }),
			life: 3000,
		});
	}

	// Utility wrapper
	async withLoading<T>(operation: () => Promise<T>, loadingMessage = "กำลังโหลด..."): Promise<T> {
		try {
			await this.showLoading(loadingMessage);
			return await operation();
		} finally {
			await this.hideLoading();
		}
	}

	/**
	 * แสดง Alert Dialog แบบมีปุ่ม Accept เพียงปุ่มเดียว
	 * @param options ตัวเลือกสำหรับ Alert Dialog
	 */
	public showAlertDialog(options: AlertDialogOptions): void {
		const subMessageHTML = options.subMessage
			? `<p class="text-sm text-gray-800 break-words overflow-hidden text-wrap">${options.subMessage}</p>`
			: "";

		// Clear any existing confirmation dialog first
		this.confirmationService.close();

		const confirmOptions: any = {
			header: options.header,
			message: `<div class="flex flex-col items-start justify-center px-6 py-4">
                <p class="text-sm mb-6 text-gray-800 break-words overflow-hidden text-wrap">${options.message}</p>
                ${subMessageHTML}
            </div>`,
			acceptLabel: options.acceptLabel || "ตกลง",
			acceptButtonStyleClass: "p-button-primary w-full",
			accept: () => {
				if (options.acceptCallback) {
					options.acceptCallback();
				}
				// Force close and reset the dialog
				this.confirmationService.close();
			},
			rejectLabel: options.rejectLabel || "ยกเลิก",
			rejectButtonStyleClass: "p-button-outlined p-button-secondary w-full",
			reject: () => {
				if (options.rejectCallback) {
					options.rejectCallback();
				}
				// Force close and reset the dialog
				this.confirmationService.close();
			},
			rejectVisible: options.rejectCallback ? true : false,
			styleClass: "product-confirm-dialog",
			closeOnEscape: false,
			closable: false,
			draggable: false,
		};

		this.confirmationService.confirm(confirmOptions);
	}

	/**
	 * แสดง Dialog Alert สำหรับการแสดงข้อความแจ้งเตือน
	 * @param options ตัวเลือกสำหรับ Dialog Alert
	 */
	public showDialogAlert(options: {
		header: string;
		message: string;
		subMessage?: string;
		acceptLabel?: string;
		rejectLabel?: string;
		acceptCallback?: () => void;
		rejectCallback?: () => void;
	}) {
		// Clear any existing confirmation dialog first
		this.confirmationService.close();

		const subMessageHTML = options.subMessage
			? `<p class="text-sm text-gray-800 break-words overflow-hidden text-wrap">${options.subMessage}</p>`
			: "";
		const confirmOptions: any = {
			header: options.header,
			message: `<div class="flex flex-col items-start justify-center px-6 py-4">
                <p class="text-sm mb-6 text-gray-800 break-words overflow-hidden text-wrap">${options.message}</p>
                ${subMessageHTML}
            </div>`,
			acceptLabel: options.acceptLabel || "ตกลง",
			acceptButtonStyleClass: "p-button-primary w-full",
			accept: () => {
				if (options.acceptCallback) {
					options.acceptCallback();
				}
				// Force close and reset the dialog
				this.confirmationService.close();
			},
			styleClass: "product-confirm-dialog",
			closeOnEscape: false,
			closable: false,
			draggable: false,
		};

		// เพิ่ม rejectLabel และส่วนที่เกี่ยวข้องเฉพาะเมื่อมีค่า
		if (options.rejectLabel) {
			confirmOptions.rejectLabel = options.rejectLabel;
			confirmOptions.rejectButtonStyleClass = "p-button-outlined p-button-secondary w-full";
			confirmOptions.reject = () => {
				if (options.rejectCallback) {
					options.rejectCallback();
				}
				// Force close and reset the dialog
				this.confirmationService.close();
			};
		} else {
			confirmOptions.reject = () => {
				// Force close and reset the dialog
				this.confirmationService.close();
			};
			confirmOptions.rejectVisible = false;
		}

		this.confirmationService.confirm(confirmOptions);
	}
}
