import { computed, effect, inject, Injectable, signal } from "@angular/core";
import { ErrorEventPayload, WorkflowEvent } from "../../domain/workflow/workflow-event.model";
import { Workflow, WorkflowCondition, WorkflowStep } from "../../domain/workflow/workflow.model";
import { ModalService } from "./modal.service";
import { WorkflowRegistryService } from "./workflow-registry.service";

/**
 * Workflow state interface
 */
export interface WorkflowState {
	workflowId: string | null;
	currentStepId: string | null;
	completedSteps: string[];
	workflowData: Record<string, any>;
	activeSubflow: string | null;
	error: ErrorEventPayload | null;
}

/**
 * Workflow modal service
 * Manages workflow navigation and state using modals
 */
@Injectable({
	providedIn: "root",
})
export class WorkflowModalService {
	private modalService = inject(ModalService);
	private workflowRegistry = inject(WorkflowRegistryService);

	// State signals
	private state = signal<WorkflowState>({
		workflowId: null,
		currentStepId: null,
		completedSteps: [],
		workflowData: {},
		activeSubflow: null,
		error: null,
	});

	// Computed signals
	readonly currentState = computed(() => this.state());
	readonly currentWorkflowId = computed(() => this.state().workflowId);
	readonly currentStepId = computed(() => this.state().currentStepId);
	readonly currentStepName = computed(() => {
		const step = this.currentStep();
		return step ? step.stepName : "";
	});
	readonly completedSteps = computed(() => this.state().completedSteps);
	readonly workflowData = computed(() => this.state().workflowData);
	readonly activeSubflow = computed(() => this.state().activeSubflow);
	readonly hasError = computed(() => this.state().error !== null);
	readonly error = computed(() => this.state().error);

	// Current workflow and step
	readonly currentWorkflow = computed(() => {
		const workflowId = this.currentWorkflowId();
		if (!workflowId) return null;
		return this.workflowRegistry.getWorkflow(workflowId);
	});

	readonly currentStep = computed(() => {
		const workflow = this.currentWorkflow();
		const stepId = this.currentStepId();
		if (!workflow || !stepId) return null;
		return this.findStepById(stepId);
	});

	// Is in subflow
	readonly isInSubflow = computed(() => this.activeSubflow() !== null);

	// Is workflow initialized
	readonly isWorkflowInitialized = computed(() => this.currentWorkflowId() !== null);

	constructor() {
		// Effect to handle state changes
		effect(() => {
			const step = this.currentStep();
			if (step) {
				console.log(`Current step: ${step.stepName} (${step.stepId})`);
			}
		});
	}

	/**
	 * Dispatch a workflow event
	 * @param event Workflow event to dispatch
	 */
	async dispatch(event: WorkflowEvent): Promise<void> {
		console.log("Dispatching workflow event:", event);

		try {
			switch (event.command) {
				case "initialize":
					console.log("Handling initialize command");
					await this.handleInitialize(event);
					break;
				case "start":
					console.log("Handling start command");
					await this.handleStart(event);
					break;
				case "next":
					console.log("Handling next command");
					await this.handleNext(event);
					break;
				case "back":
					console.log("Handling back command");
					await this.handleBack(event);
					break;
				case "close":
					console.log("Handling close command");
					await this.handleClose(event);
					break;
				case "start_subflow":
					console.log("Handling start_subflow command");
					await this.handleStartSubflow(event);
					break;
				case "next_subflow":
					console.log("Handling next_subflow command");
					await this.handleNextSubflow(event);
					break;
				case "back_subflow":
					console.log("Handling back_subflow command");
					await this.handleBackSubflow(event);
					break;
				case "close_subflow":
					console.log("Handling close_subflow command");
					await this.handleCloseSubflow(event);
					break;
				case "resume":
					console.log("Handling resume command");
					await this.handleResume(event);
					break;
				case "jump_to":
					console.log("Handling jump_to command");
					await this.handleJumpTo(event);
					break;
				case "error":
					console.log("Handling error command");
					await this.handleError(event);
					break;
				default:
					console.error("Unknown workflow command:", event.command);
			}
		} catch (error) {
			console.error("Error dispatching workflow event:", error);
		}
	}

	/**
	 * Handle 'initialize' command
	 * Initialize a workflow without starting it
	 * @param event Workflow event
	 */
	private async handleInitialize(event: WorkflowEvent): Promise<void> {
		console.log("Initializing workflow with event:", event);

		const workflowId = event.targetStepId || "loan-application-workflow";
		console.log("Looking for workflow with ID:", workflowId);

		const workflow = this.workflowRegistry.getWorkflow(workflowId);
		console.log("Found workflow:", workflow);

		if (!workflow) {
			console.error(`Workflow with ID ${workflowId} not found`);
			return;
		}

		// Extract any initial completed steps from the payload
		const initialCompletedSteps = event.payload?._initialCompletedSteps || [];

		// Remove the _initialCompletedSteps property from the payload
		const workflowData = { ...event.payload };
		if (workflowData._initialCompletedSteps) {
			delete workflowData._initialCompletedSteps;
		}

		console.log("Initial completed steps:", initialCompletedSteps);

		// Initialize state without opening any modal
		this.state.set({
			workflowId,
			currentStepId: null, // Don't set current step yet
			completedSteps: [...initialCompletedSteps],
			workflowData: workflowData || {},
			activeSubflow: null,
			error: null,
		});

		console.log("Workflow initialized without starting:", this.state());
	}

	/**
	 * Handle 'start' command
	 * @param event Workflow event
	 */
	private async handleStart(event: WorkflowEvent): Promise<void> {
		console.log("Starting workflow with event:", event);

		// Check if workflow is already initialized
		const currentWorkflowId = this.currentWorkflowId();
		let workflowId: string;
		let workflow: any;

		if (currentWorkflowId) {
			// Use already initialized workflow
			console.log("Using already initialized workflow:", currentWorkflowId);
			workflowId = currentWorkflowId;
			workflow = this.currentWorkflow();
		} else {
			// Initialize new workflow
			workflowId = event.targetStepId || "loan-application-workflow";
			console.log("Looking for workflow with ID:", workflowId);

			workflow = this.workflowRegistry.getWorkflow(workflowId);
			console.log("Found workflow:", workflow);

			if (!workflow) {
				console.error(`Workflow with ID ${workflowId} not found`);
				return;
			}

			// Extract any initial completed steps from the payload
			const initialCompletedSteps = event.payload?._initialCompletedSteps || [];

			// Remove the _initialCompletedSteps property from the payload
			const workflowData = { ...event.payload };
			if (workflowData._initialCompletedSteps) {
				delete workflowData._initialCompletedSteps;
			}

			console.log("Initial completed steps:", initialCompletedSteps);

			// Initialize state
			this.state.set({
				workflowId,
				currentStepId: null, // Will be set below
				completedSteps: [...initialCompletedSteps],
				workflowData: workflowData || {},
				activeSubflow: null,
				error: null,
			});
		}

		// Determine starting step
		const startStepId = event.payload?.startStepId || workflow.start;
		console.log("Starting at step:", startStepId);

		// Update current step
		this.state.update((state) => ({
			...state,
			currentStepId: startStepId,
		}));

		console.log("State updated for start:", this.state());

		// Open the starting step modal
		console.log("Opening step modal for step:", startStepId);
		await this.openStepModal(startStepId);
	}

	/**
	 * Handle 'next' command
	 * @param event Workflow event
	 */
	private async handleNext(event: WorkflowEvent): Promise<void> {
		console.log("Handling next command with event:", event);

		const workflow = this.currentWorkflow();
		const currentStepId = this.currentStepId();

		console.log("Current workflow:", workflow);
		console.log("Current step ID:", currentStepId);

		if (!workflow || !currentStepId) {
			console.error("No active workflow or step");
			return;
		}

		const currentStep = this.findStepById(currentStepId);
		console.log("Current step:", currentStep);

		if (!currentStep) {
			console.error(`Step with ID ${currentStepId} not found`);
			return;
		}

		// Update workflow data with payload
		if (event.payload) {
			console.log("Updating workflow data with payload:", event.payload);
			this.updateWorkflowData(event.payload);
		}

		// Mark current step as completed
		console.log("Marking step as completed:", currentStepId);
		this.markStepCompleted(currentStepId);
		// Note: checkAndMarkParentCompleted is now called inside markStepCompleted

		// Find parent step if this is a child step (needed for navigation logic)
		const parentStep = this.findParentStep(workflow, currentStepId);
		console.log("Parent step:", parentStep);

		// Determine next step
		console.log("Determining next step for current step:", currentStep);
		console.log("Using workflow data:", this.workflowData());
		const nextStepId = this.determineNextStep(currentStep, this.workflowData());
		console.log("Next step ID:", nextStepId);

		if (!nextStepId) {
			console.log("No next step defined");

			// If this is a child step and there's no next step, go to parent's next step
			if (parentStep) {
				console.log("This is a child step with no next step, going to parent's next step");
				const parentNextStepId = this.determineNextStep(parentStep, this.workflowData());

				if (parentNextStepId) {
					// Close current modal
					console.log("Closing current modal");
					await this.closeCurrentModal();

					// Update state with parent's next step
					console.log("Updating state with parent's next step ID:", parentNextStepId);
					this.state.update((state) => ({
						...state,
						currentStepId: parentNextStepId,
					}));

					// Open parent's next step modal
					console.log("Opening parent's next step modal for step ID:", parentNextStepId);
					await this.openStepModal(parentNextStepId);
				}
			}
			return;
		}

		// Close current modal
		console.log("Closing current modal");
		await this.closeCurrentModal();

		// Update state with next step
		console.log("Updating state with next step ID:", nextStepId);
		this.state.update((state) => ({
			...state,
			currentStepId: nextStepId,
		}));
		console.log("Updated state:", this.state());

		// Open next step modal
		console.log("Opening next step modal for step ID:", nextStepId);
		await this.openStepModal(nextStepId);
	}

	/**
	 * Handle 'back' command
	 * @param event Workflow event
	 */
	private async handleBack(event: WorkflowEvent): Promise<void> {
		console.log("Handling back command with event:", event);

		const workflow = this.currentWorkflow();
		const currentStepId = this.currentStepId();

		console.log("Current workflow:", workflow);
		console.log("Current step ID:", currentStepId);

		if (!workflow || !currentStepId) {
			console.error("No active workflow or step");
			return;
		}

		const currentStep = this.findStepById(currentStepId);
		console.log("Current step:", currentStep);

		if (!currentStep) {
			console.error(`Step with ID ${currentStepId} not found`);
			return;
		}

		// Find parent step if this is a child step
		const parentStep = this.findParentStep(workflow, currentStepId);
		console.log("Parent step:", parentStep);

		if (!currentStep.back) {
			console.log(`Step with ID ${currentStepId} has no back navigation`);

			// If this is a child step with no back navigation, go to parent
			if (parentStep) {
				console.log("This is a child step with no back navigation, going to parent");

				// Close current modal
				console.log("Closing current modal");
				await this.closeCurrentModal();

				// Update state with parent step
				console.log("Updating state with parent step ID:", parentStep.stepId);
				this.state.update((state) => ({
					...state,
					currentStepId: parentStep.stepId,
				}));

				// Open parent step modal
				console.log("Opening parent step modal for step ID:", parentStep.stepId);
				await this.openStepModal(parentStep.stepId);
			}
			return;
		}

		// Close current modal
		console.log("Closing current modal");
		await this.closeCurrentModal();

		// Update state with previous step
		console.log("Updating state with previous step ID:", currentStep.back);
		this.state.update((state) => ({
			...state,
			currentStepId: currentStep.back as string,
		}));

		// Open previous step modal
		console.log("Opening previous step modal for step ID:", currentStep.back);
		await this.openStepModal(currentStep.back);
	}

	/**
	 * Handle 'close' command
	 * @param event Workflow event
	 */
	private async handleClose(event: WorkflowEvent): Promise<void> {
		console.log("Handling close command with event:", event);

		// Close all modals
		await this.modalService.closeAllModals();

		// Reset state
		this.state.set({
			workflowId: null,
			currentStepId: null,
			completedSteps: [],
			workflowData: {},
			activeSubflow: null,
			error: null,
		});

		console.log("All modals closed and state reset");
	}

	/**
	 * Handle 'start_subflow' command
	 * @param event Workflow event
	 */
	private async handleStartSubflow(event: WorkflowEvent): Promise<void> {
		console.log("Starting subflow with event:", event);

		const workflow = this.currentWorkflow();
		const currentStepId = this.currentStepId();

		if (!workflow || !currentStepId) {
			console.error("No active workflow or step");
			return;
		}

		const currentStep = this.findStepById(currentStepId);
		if (!currentStep || !currentStep.subflow) {
			console.error(`Step with ID ${currentStepId} has no subflow`);
			return;
		}

		const subflow = currentStep.subflow;

		// Determine which step to start with
		// Use event.targetStepId if provided, otherwise use subflow.start
		const startStepId = event.targetStepId || subflow.start;
		console.log("Starting subflow with step:", startStepId);

		// Update state with subflow
		this.state.update((state) => ({
			...state,
			activeSubflow: subflow.id,
			currentStepId: startStepId,
		}));

		// Open subflow modal
		await this.openSubflowModal(startStepId, currentStepId);
	}

	/**
	 * Handle 'next_subflow' command
	 * @param event Workflow event
	 */
	private async handleNextSubflow(event: WorkflowEvent): Promise<void> {
		console.log("Handling next_subflow command with event:", event);

		if (!this.isInSubflow()) {
			console.error("Not in a subflow");
			return;
		}

		const workflow = this.currentWorkflow();
		const currentStepId = this.currentStepId();

		if (!workflow || !currentStepId) {
			console.error("No active workflow or step");
			return;
		}

		const currentStep = this.findStepById(currentStepId);
		if (!currentStep) {
			console.error(`Step with ID ${currentStepId} not found`);
			return;
		}

		// Update workflow data with payload
		if (event.payload) {
			this.updateWorkflowData(event.payload);
		}

		// Mark current step as completed
		this.markStepCompleted(currentStepId);
		// Note: checkAndMarkParentCompleted is now called inside markStepCompleted

		// Determine next step
		const nextStepId = this.determineNextStep(currentStep, this.workflowData());
		console.log("Next step ID determined:", nextStepId);

		if (!nextStepId) {
			// If no next step, exit subflow
			console.log("No next step found, closing subflow");
			await this.handleCloseSubflow(event);
			return;
		}

		// Get all active modals
		const allModals = Array.from(this.modalService.modals().keys());
		console.log("All active modals:", allModals);

		// Find the current modal ID by looking for one that starts with 'subflow-modal-'
		const currentModalId = allModals.find((id) => id.startsWith("subflow-modal-"));
		console.log("Current subflow modal ID (by prefix):", currentModalId);

		try {
			// Close current subflow modal using direct approach
			if (currentModalId) {
				console.log(`Directly dismissing modal with ID: ${currentModalId}`);
				await this.modalService.modalController.dismiss(undefined, undefined, currentModalId);
				console.log(`Modal with ID: ${currentModalId} dismissed directly`);

				// Remove from state manually
				this.modalService.modals.update((modals) => {
					const newModals = new Map(modals);
					newModals.delete(currentModalId);
					return newModals;
				});
			} else {
				// Try to close all modals as a fallback
				console.log("No specific subflow modal found, trying to close all subflow modals");
				await this.modalService.closeAllSubflowModals();
			}
		} catch (error) {
			console.warn("Error closing current subflow modal:", error);
			// Try fallback method
			await this.modalService.closeAllSubflowModals();
			// If all else fails, try the deprecated method
			await this.closeCurrentSubflowModal();
		}

		// Update state with next step
		this.state.update((state) => ({
			...state,
			currentStepId: nextStepId,
		}));

		// Wait a short time to ensure the previous modal is fully closed
		// This helps prevent visual glitches when opening the new modal
		await new Promise((resolve) => setTimeout(resolve, 300));

		// Open next subflow step modal
		console.log("Opening new subflow modal for step:", nextStepId);
		await this.openSubflowModal(nextStepId);
	}

	/**
	 * Handle 'back_subflow' command
	 * @param event Workflow event
	 */
	private async handleBackSubflow(event: WorkflowEvent): Promise<void> {
		console.log("Handling back_subflow command with event:", event);

		if (!this.isInSubflow()) {
			console.error("Not in a subflow");
			return;
		}

		const workflow = this.currentWorkflow();
		const currentStepId = this.currentStepId();

		if (!workflow || !currentStepId) {
			console.error("No active workflow or step");
			return;
		}

		const currentStep = this.findStepById(currentStepId);
		if (!currentStep || !currentStep.back) {
			console.error(`Step with ID ${currentStepId} has no back navigation`);
			return;
		}

		const backStepId = currentStep.back as string;
		console.log("Going back to step:", backStepId);

		// Get all active modals
		const allModals = Array.from(this.modalService.modals().keys());
		console.log("All active modals:", allModals);

		// Find the current modal ID by looking for one that starts with 'subflow-modal-'
		const currentModalId = allModals.find((id) => id.startsWith("subflow-modal-"));
		console.log("Current subflow modal ID (by prefix):", currentModalId);

		try {
			// Close current subflow modal using direct approach
			if (currentModalId) {
				console.log(`Directly dismissing modal with ID: ${currentModalId}`);
				await this.modalService.modalController.dismiss(undefined, undefined, currentModalId);
				console.log(`Modal with ID: ${currentModalId} dismissed directly`);

				// Remove from state manually
				this.modalService.modals.update((modals) => {
					const newModals = new Map(modals);
					newModals.delete(currentModalId);
					return newModals;
				});
			} else {
				// Try to close all modals as a fallback
				console.log("No specific subflow modal found, trying to close all subflow modals");
				await this.modalService.closeAllSubflowModals();
			}
		} catch (error) {
			console.warn("Error closing current subflow modal:", error);
			// Try fallback method
			await this.modalService.closeAllSubflowModals();
			// If all else fails, try the deprecated method
			await this.closeCurrentSubflowModal();
		}

		// Update state with previous step
		this.state.update((state) => ({
			...state,
			currentStepId: backStepId,
		}));

		// Wait a short time to ensure the previous modal is fully closed
		await new Promise((resolve) => setTimeout(resolve, 300));

		// Open previous subflow step modal
		console.log("Opening new subflow modal for previous step:", backStepId);
		await this.openSubflowModal(backStepId);
	}

	/**
	 * Handle 'close_subflow' command
	 * @param event Workflow event
	 */
	private async handleCloseSubflow(event: WorkflowEvent): Promise<void> {
		console.log("Handling close_subflow command with event:", event);
		console.log("Current state:", this.state());
		console.log("Is in subflow?", this.isInSubflow());
		console.log("Active subflow:", this.activeSubflow());

		// Force subflow mode if we're coming from a step that starts with 'selfie.'
		const fromStepId = event.fromStepId;
		const isFromSelfieStep = fromStepId && fromStepId.startsWith("selfie.");

		if (isFromSelfieStep) {
			console.log("Forcing subflow mode because event is from a selfie step");
			// Force set the active subflow to 'selfie'
			this.state.update((state) => ({
				...state,
				activeSubflow: "selfie",
			}));
		}

		// Check again after potential update
		if (!this.isInSubflow() && !isFromSelfieStep) {
			console.error("Not in a subflow");
			return;
		}

		const workflow = this.currentWorkflow();
		const subflowId = this.activeSubflow() || (isFromSelfieStep ? "selfie" : null);

		if (!workflow || !subflowId) {
			console.error("No active workflow or subflow");
			return;
		}

		// Find the parent step that contains this subflow
		const parentStep = this.findStepWithSubflow(workflow, subflowId);
		if (!parentStep || !parentStep.subflow) {
			console.error(`Parent step for subflow ${subflowId} not found`);

			// If we can't find the parent step, try to close modals anyway
			// Get all active modals
			const allModals = Array.from(this.modalService.modals().keys());
			console.log("All active modals:", allModals);

			// Find all subflow modals
			const subflowModals = allModals.filter((id) => id.startsWith("subflow-modal-"));
			console.log("Subflow modals to close:", subflowModals);

			// Close each subflow modal
			for (const modalId of subflowModals) {
				try {
					console.log(`Directly dismissing modal with ID: ${modalId}`);
					await this.modalService.modalController.dismiss(undefined, undefined, modalId);
					console.log(`Modal with ID: ${modalId} dismissed directly`);

					// Remove from state manually
					this.modalService.modals.update((modals) => {
						const newModals = new Map(modals);
						newModals.delete(modalId);
						return newModals;
					});
				} catch (error) {
					console.warn(`Error dismissing modal ${modalId}:`, error);
				}
			}

			// Reset subflow state and try to return to a sensible step
			// If we have a fromStepId, try to use that as the return step
			const returnToStepId = "1.5"; // Hard-code to '1.5' as this is the parent step we know

			this.state.update((state) => ({
				...state,
				currentStepId: returnToStepId,
				activeSubflow: null,
			}));

			console.log("Subflow closed, returning to step:", returnToStepId);

			// We don't need to open a new modal for the parent step
			// The parent step modal should already be open in the background
			// Just let the subflow modal close and the parent modal will be visible again

			return;
		}

		const returnToStepId = parentStep.subflow!.returnTo;
		console.log("Returning to parent step:", returnToStepId);

		// Get all active modals
		const allModals = Array.from(this.modalService.modals().keys());
		console.log("All active modals:", allModals);

		// Find all subflow modals
		const subflowModals = allModals.filter((id) => id.startsWith("subflow-modal-"));
		console.log("Subflow modals to close:", subflowModals);

		// Close each subflow modal
		for (const modalId of subflowModals) {
			try {
				console.log(`Directly dismissing modal with ID: ${modalId}`);
				await this.modalService.modalController.dismiss(undefined, undefined, modalId);
				console.log(`Modal with ID: ${modalId} dismissed directly`);

				// Remove from state manually
				this.modalService.modals.update((modals) => {
					const newModals = new Map(modals);
					newModals.delete(modalId);
					return newModals;
				});
			} catch (error) {
				console.warn(`Error dismissing modal ${modalId}:`, error);
				// Try fallback
				await this.modalService.closeModal(modalId);
			}
		}

		// If no modals were found or closed, try the general method
		if (subflowModals.length === 0) {
			console.log("No specific subflow modals found, trying to close all subflow modals");
			await this.modalService.closeAllSubflowModals();
		}

		// Update state to return to parent step
		this.state.update((state) => ({
			...state,
			currentStepId: returnToStepId,
			activeSubflow: null,
		}));

		console.log("Subflow closed, returned to parent step:", returnToStepId);

		// We don't need to open a new modal for the parent step
		// The parent step modal should already be open in the background
		// Just let the subflow modal close and the parent modal will be visible again
	}

	/**
	 * Handle 'resume' command
	 * @param event Workflow event
	 */
	private async handleResume(event: WorkflowEvent): Promise<void> {
		const workflowId =
			event.targetStepId || this.currentWorkflowId() || "loan-application-workflow";
		if (!workflowId) {
			console.error("No workflow ID specified for resume");
			return;
		}

		const workflow = this.workflowRegistry.getWorkflow(workflowId);
		if (!workflow) {
			console.error(`Workflow with ID ${workflowId} not found`);
			return;
		}

		// If we have state data in the payload, restore it
		if (event.payload && event.payload.state) {
			this.state.set(event.payload.state);
		} else if (event.payload && event.payload.stepId) {
			// If we have a specific stepId to resume to
			const stepId = event.payload.stepId;
			const step = this.findStepById(stepId, workflowId);

			if (!step) {
				console.error(`Step with ID ${stepId} not found in workflow ${workflowId}`);
				return;
			}

			// Initialize or update state with the specified step
			if (this.currentWorkflowId()) {
				// Update existing state
				this.state.update((state) => ({
					...state,
					currentStepId: stepId,
					activeSubflow: null, // Reset subflow when resuming to a specific step
				}));
			} else {
				// Initialize new state
				this.state.set({
					workflowId,
					currentStepId: stepId,
					completedSteps: [],
					workflowData: event.payload.workflowData || {},
					activeSubflow: null,
					error: null,
				});
			}

			// Open the specified step modal
			await this.openStepModal(stepId);
			return;
		}

		// Open the current step modal
		const currentStepId = this.currentStepId();
		if (currentStepId) {
			if (this.isInSubflow()) {
				await this.openSubflowModal(currentStepId);
			} else {
				await this.openStepModal(currentStepId);
			}
		} else {
			// If no current step, start from the beginning
			this.state.update((state) => ({
				...state,
				currentStepId: workflow.start,
			}));
			await this.openStepModal(workflow.start);
		}
	}

	/**
	 * Resume workflow to a specific step by stepId
	 * @param stepId The step ID to resume to
	 * @param workflowData Optional workflow data
	 */
	async resumeToStep(stepId: string, workflowData?: Record<string, any>): Promise<void> {
		console.log(`Resuming workflow to step: ${stepId}`);

		// Make sure we have a workflow loaded first
		const workflowId = "loan-application-workflow";
		const workflow = this.workflowRegistry.getWorkflow(workflowId);

		if (!workflow) {
			console.error(`Workflow with ID ${workflowId} not found`);
			return;
		}

		// Initialize workflow if not already initialized
		if (!this.currentWorkflowId()) {
			console.log("Initializing workflow before resuming to step");

			// Initialize state with the workflow
			this.state.set({
				workflowId,
				currentStepId: stepId,
				completedSteps: [],
				workflowData: workflowData || {},
				activeSubflow: null,
				error: null,
			});

			// Open the step modal directly
			await this.openStepModal(stepId);
		} else {
			// If workflow is already initialized, use dispatch
			await this.dispatch({
				command: "resume",
				payload: {
					stepId,
					workflowData,
				},
			});
		}
	}

	/**
	 * Initialize a workflow without starting it
	 * @param workflowId Workflow ID to initialize
	 * @param workflowData Optional initial workflow data
	 * @param completedStepIds Optional array of step IDs to mark as completed
	 */
	async initializeWorkflow(
		workflowId: string = "loan-application-workflow",
		workflowData: Record<string, any> = {},
		completedStepIds: string[] = [],
	): Promise<void> {
		console.log(`Initializing workflow: ${workflowId}`);

		await this.dispatch({
			command: "initialize",
			targetStepId: workflowId,
			payload: {
				...workflowData,
				_initialCompletedSteps: completedStepIds,
			},
		});
	}

	/**
	 * Start a workflow (can be used after initialization or standalone)
	 * @param startStepId Optional step ID to start at (defaults to workflow.start)
	 * @param workflowData Optional workflow data (will be merged with existing data)
	 */
	async startWorkflow(startStepId?: string, workflowData: Record<string, any> = {}): Promise<void> {
		console.log(`Starting workflow at step: ${startStepId || "default"}`);

		// Check if workflow is already initialized
		if (!this.isWorkflowInitialized()) {
			throw new Error(
				"Workflow must be initialized before starting. Call initializeWorkflow() first.",
			);
		}

		await this.dispatch({
			command: "start",
			payload: {
				startStepId,
				...workflowData,
			},
		});
	}

	/**
	 * Initialize and start a workflow in one call (for convenience)
	 * @param workflowId Workflow ID to initialize and start
	 * @param startStepId Optional step ID to start at (defaults to workflow.start)
	 * @param workflowData Optional workflow data
	 * @param completedStepIds Optional array of step IDs to mark as completed
	 */
	async initializeAndStartWorkflow(
		workflowId: string = "loan-application-workflow",
		startStepId?: string,
		workflowData: Record<string, any> = {},
		completedStepIds: string[] = [],
	): Promise<void> {
		console.log(
			`Initializing and starting workflow: ${workflowId} at step: ${startStepId || "default"}`,
		);

		// First initialize
		await this.initializeWorkflow(workflowId, workflowData, completedStepIds);

		// Then start
		await this.startWorkflow(startStepId);
	}

	/**
	 * Start a workflow directly at a specific step, skipping previous steps
	 * @param targetStepId The step ID to start at
	 * @param completedStepIds Array of step IDs to mark as completed
	 * @param workflowData Optional workflow data
	 * @param workflowId Optional workflow ID (defaults to "loan-application-workflow")
	 */
	async startWorkflowAtStep(
		targetStepId: string,
		completedStepIds: string[] = [],
		workflowData: Record<string, any> = {},
		workflowId: string = "loan-application-workflow",
	): Promise<void> {
		console.log(`Starting workflow directly at step: ${targetStepId}`);
		console.log(`Marking steps as completed: ${completedStepIds.join(", ")}`);

		// Get the workflow
		const workflow = this.workflowRegistry.getWorkflow(workflowId);
		if (!workflow) {
			console.error(`Workflow with ID ${workflowId} not found`);
			return;
		}

		// Validate that the target step exists
		const targetStep = this.findStepById(targetStepId, workflowId);
		if (!targetStep) {
			console.error(`Target step with ID ${targetStepId} not found in workflow ${workflowId}`);
			return;
		}

		// Initialize the workflow state directly at the target step
		this.state.set({
			workflowId,
			currentStepId: targetStepId,
			completedSteps: [...completedStepIds],
			workflowData,
			activeSubflow: null,
			error: null,
		});

		// Open the modal for the target step
		await this.openStepModal(targetStepId);

		// Make sure all specified steps are marked as completed
		// This is a safety measure to ensure the UI reflects the correct state
		setTimeout(() => {
			completedStepIds.forEach((stepId) => {
				if (!this.isStepCompleted(stepId)) {
					this.markStepCompleted(stepId);
				}
			});
		}, 100);
	}

	/**
	 * Resume workflow to a specific page by pageId
	 * @param pageId The page ID to resume to
	 * @param workflowData Optional workflow data
	 */
	async resumeToPage(pageId: string, workflowData?: Record<string, any>): Promise<void> {
		console.log(`Resuming workflow to page: ${pageId}`);

		// Find the workflow step with the specified pageId
		const workflowId = "loan-application-workflow";
		const workflow = this.workflowRegistry.getWorkflow(workflowId);
		if (!workflow) {
			console.error(`Workflow with ID ${workflowId} not found`);
			return;
		}

		console.log("Looking for step with pageId:", pageId);

		// Helper function to find a step by pageId
		const findStepByPageId = (steps: any[]): any => {
			for (const step of steps) {
				console.log("Checking step:", step.stepId, "meta:", step.meta);

				if (step.meta?.pageId === pageId) {
					console.log(`Found matching step with pageId ${pageId}:`, step);
					return step;
				}

				// Check children
				if (step.children && step.children.length > 0) {
					const foundInChildren = findStepByPageId(step.children);
					if (foundInChildren) {
						return foundInChildren;
					}
				}

				// Check subflow
				if (step.subflow && step.subflow.steps && step.subflow.steps.length > 0) {
					const foundInSubflow = findStepByPageId(step.subflow.steps);
					if (foundInSubflow) {
						return foundInSubflow;
					}
				}
			}

			return null;
		};

		// Get all steps from all groups
		let allSteps: any[] = [];

		// Process each step group
		workflow.steps.forEach((group) => {
			// Process children of each group
			if (group.children && group.children.length > 0) {
				// Log all children for debugging
				console.log(`Group ${group.stepId} has ${group.children.length} children`);
				group.children.forEach((child) => {
					console.log(`- Child: ${child.stepId}, pageId: ${child.meta?.pageId}`);
				});

				allSteps = [...allSteps, ...group.children];
			}
		});

		// Find the step with the matching pageId
		const step = findStepByPageId(allSteps);

		if (!step) {
			console.error(`No step found with pageId: ${pageId}`);
			return;
		}

		console.log(`Found step with pageId ${pageId}:`, step);

		// Resume to the found step
		await this.resumeToStep(step.stepId, workflowData);
	}

	/**
	 * Generate workflow by ID
	 * @param workflowId Workflow ID
	 */
	generateWorkflowById(workflowId: string): Workflow | null {
		const workflow = this.workflowRegistry.getWorkflow(workflowId);
		if (!workflow) {
			console.error(`Workflow with ID ${workflowId} not found`);
			return null;
		}

		return workflow;
	}

	/**
	 * Load current state from storage and resume workflow
	 */
	async loadAndResumeWorkflow(): Promise<void> {
		const savedState = localStorage.getItem("loan-workflow-state");
		if (!savedState) {
			console.error("No saved workflow state found");
			return;
		}

		try {
			const state = JSON.parse(savedState);
			await this.dispatch({
				command: "resume",
				payload: { state },
			});
		} catch (error) {
			console.error("Error parsing saved workflow state:", error);
		}
	}

	/**
	 * Handle 'jump_to' command
	 * @param event Workflow event
	 */
	private async handleJumpTo(event: WorkflowEvent): Promise<void> {
		if (!event.targetStepId) {
			console.error("No target step ID specified for jump_to");
			return;
		}

		const workflow = this.currentWorkflow();
		if (!workflow) {
			console.error("No active workflow");
			return;
		}

		const targetStep = this.findStepById(event.targetStepId);
		if (!targetStep) {
			console.error(`Target step with ID ${event.targetStepId} not found`);
			return;
		}

		// Close current modals
		if (this.isInSubflow()) {
			await this.modalService.closeAllSubflowModals();
		} else {
			await this.closeCurrentModal();
		}

		// Update state with target step
		this.state.update((state) => ({
			...state,
			currentStepId: event.targetStepId!,
			activeSubflow: null, // Reset subflow when jumping
		}));

		// Open target step modal
		await this.openStepModal(event.targetStepId);
	}

	/**
	 * Handle 'error' command
	 * @param event Workflow event
	 */
	private async handleError(event: WorkflowEvent): Promise<void> {
		if (!event.payload) {
			console.error("No error payload provided");
			return;
		}

		// Set error state
		this.state.update((state) => ({
			...state,
			error: event.payload as ErrorEventPayload,
		}));

		// TODO: Open error modal or handle error in the current step
	}

	/**
	 * Open a step modal
	 * @param stepId Step ID to open
	 */
	private async openStepModal(stepId: string): Promise<void> {
		console.log("Opening step modal for step ID:", stepId);

		const workflow = this.currentWorkflow();
		console.log("Current workflow:", workflow);

		if (!workflow) {
			console.error("No active workflow");
			return;
		}

		const step = this.findStepById(stepId);
		console.log("Found step:", step);

		if (!step) {
			console.error(`Step with ID ${stepId} not found`);
			return;
		}

		// Find parent step if this is a child step
		const parentStep = this.findParentStep(workflow, stepId);

		// Check if this is a child step and if all siblings are completed
		if (parentStep && parentStep.children) {
			// Check if all other children of the parent are completed
			const completedSteps = this.completedSteps();
			const allOtherChildrenCompleted = parentStep.children
				.filter((child: any) => child.stepId !== stepId) // Exclude current step
				.every((child: any) => completedSteps.includes(child.stepId));

			// If all other children are completed and current step is already completed,
			// mark the parent as completed too
			if (allOtherChildrenCompleted && completedSteps.includes(stepId)) {
				console.log(
					`All children of step ${parentStep.stepId} are completed, marking parent as completed`,
				);
				this.markStepCompleted(parentStep.stepId);
			}
		}

		// Check if this step has children - if so, navigate to the first child
		if (step.children && Array.isArray(step.children) && step.children.length > 0) {
			const firstChild = step.children[0];
			console.log("Step has children, navigating to first child:", firstChild.stepId);

			// Update state to the first child step
			this.state.update((state) => ({
				...state,
				currentStepId: firstChild.stepId,
			}));

			// Open the first child step
			await this.openStepModal(firstChild.stepId);
			return;
		}

		// Check if this step has a subflow - but don't start it automatically
		// We'll let the user start it manually with a button
		// Just proceed with showing the step's own page if it has meta.pageId

		// If no children or subflow, proceed with normal step
		if (!step.meta?.pageId) {
			console.error(`Step with ID ${stepId} has no pageId`);
			return;
		}

		console.log("Step page ID:", step.meta.pageId);

		try {
			// Get component asynchronously
			console.log("Getting component for page ID:", step.meta.pageId);
			const component = await this.modalService.getComponentForPage(step.meta.pageId);
			console.log("Component loaded:", component);

			if (!component) {
				console.error(`No component found for page ID ${step.meta.pageId}`);
				return;
			}

			// Import the WorkflowContainerComponent dynamically
			const { WorkflowContainerComponent } = await import(
				"../../../adapters/primary/workflow/container/workflow-container.component"
			);

			// Create modal props for the container
			const containerProps = {
				currentComponent: component,
				data: {
					stepId,
					stepData: step,
					workflowData: this.workflowData(),
					isSubflow: false,
					...step.meta,
				},
				stepperTitle: "ขั้นตอนการสมัคร",
			};
			console.log("Container props:", containerProps);

			// Open modal with the container component
			console.log("Opening modal with container component");
			await this.modalService.openModal(
				WorkflowContainerComponent,
				containerProps,
				`workflow-modal-${stepId}`,
			);
			console.log("Modal opened successfully");
		} catch (error) {
			console.error(`Error loading component for step ${stepId}:`, error);
		}
	}

	/**
	 * Open a subflow step modal
	 * @param stepId Subflow step ID to open
	 * @param parentStepId Parent step ID (optional)
	 */
	private async openSubflowModal(stepId: string, parentStepId?: string): Promise<void> {
		const workflow = this.currentWorkflow();
		if (!workflow) {
			console.error("No active workflow");
			return;
		}

		const step = this.findStepById(stepId);
		if (!step || !step.meta?.pageId) {
			console.error(`Step with ID ${stepId} not found or has no pageId`);
			return;
		}

		try {
			// Get component asynchronously
			const component = await this.modalService.getComponentForPage(step.meta.pageId);
			if (!component) {
				console.error(`No component found for page ID ${step.meta.pageId}`);
				return;
			}

			// Import the WorkflowContainerComponent dynamically
			const { WorkflowContainerComponent } = await import(
				"../../../adapters/primary/workflow/container/workflow-container.component"
			);

			// Create modal props for the container
			const containerProps = {
				currentComponent: component,
				data: {
					stepId,
					stepData: step,
					workflowData: this.workflowData(),
					isSubflow: true, // Explicitly set isSubflow to true
					...step.meta,
				},
				stepperTitle: "ขั้นตอนการถ่ายรูป",
			};
			console.log(
				"Subflow container props:",
				containerProps,
				"isSubflow:",
				containerProps.data.isSubflow,
			);

			// Open modal with the container component
			await this.modalService.openModal(
				WorkflowContainerComponent,
				containerProps,
				`subflow-modal-${stepId}`,
				parentStepId ? `workflow-modal-${parentStepId}` : undefined,
			);
		} catch (error) {
			console.error(`Error loading component for subflow step ${stepId}:`, error);
		}
	}

	/**
	 * Close the current modal
	 */
	private async closeCurrentModal(): Promise<void> {
		const currentModal = this.modalService.currentModal();
		if (currentModal) {
			await this.modalService.closeModal(currentModal[0]);
		}
	}

	/**
	 * Close the current subflow modal - DEPRECATED, use direct modal dismissal instead
	 * This method is kept for backward compatibility
	 */
	private async closeCurrentSubflowModal(): Promise<void> {
		console.log("Closing current subflow modal (deprecated method)");

		// Get all active modals
		const allModals = Array.from(this.modalService.modals().keys());
		console.log("All active modals:", allModals);

		// Find the current modal ID by looking for one that starts with 'subflow-modal-'
		const currentModalId = allModals.find((id) => id.startsWith("subflow-modal-"));

		if (currentModalId) {
			console.log("Current subflow modal ID to close:", currentModalId);
			try {
				// Force close the modal directly using the Ionic ModalController
				await this.modalService.modalController.dismiss(undefined, undefined, currentModalId);
				console.log(
					`Subflow modal with ID ${currentModalId} dismissed directly via ModalController`,
				);

				// Remove from state manually to ensure it's gone
				this.modalService.modals.update((modals) => {
					const newModals = new Map(modals);
					newModals.delete(currentModalId);
					return newModals;
				});
			} catch (error) {
				console.warn(`Error dismissing subflow modal directly:`, error);

				// Fallback to our service method
				await this.modalService.closeModal(currentModalId);
			}
		} else {
			console.warn("No current subflow modal found to close");
			// Try to close all subflow modals as a fallback
			await this.modalService.closeAllSubflowModals();
		}
	}

	/**
	 * Update workflow data
	 * @param data New data to merge with existing workflow data
	 */
	private updateWorkflowData(data: Record<string, any>): void {
		this.state.update((state) => ({
			...state,
			workflowData: { ...state.workflowData, ...data },
		}));
	}

	/**
	 * Get all steps from the current workflow
	 * @returns Array of all workflow steps
	 */
	getAllSteps(): any[] {
		const workflow = this.currentWorkflow();
		if (!workflow) {
			return [];
		}

		// Return all top-level steps
		return workflow.steps;
	}

	/**
	 * Determine the next step based on navigation conditions
	 * @param step Current step
	 * @param data Workflow data
	 */
	private determineNextStep(step: WorkflowStep, data: Record<string, any>): string | null {
		console.log("Determining next step for:", step);
		console.log("Step next property:", step.next);

		if (!step.next || step.next.length === 0) {
			console.log("No next steps defined");
			return null;
		}

		console.log("Evaluating navigation options:", step.next);

		for (const nav of step.next) {
			console.log("Evaluating navigation option:", nav);

			if (typeof nav === "string") {
				console.log("Simple navigation to:", nav);
				return nav;
			} else {
				// It's a conditional navigation
				const condition = nav.condition;
				console.log("Evaluating condition:", condition, "with data:", data);

				const result = this.evaluateCondition(condition, data);
				console.log("Condition evaluation result:", result);

				if (result) {
					console.log("Condition matched, navigating to:", nav.goTo);
					return nav.goTo;
				}
			}
		}

		// If no conditions match, use the first navigation as default
		console.log("No conditions matched, using default navigation");
		const firstNav = step.next[0];
		const defaultNext = typeof firstNav === "string" ? firstNav : firstNav.goTo;
		console.log("Default next step:", defaultNext);
		return defaultNext;
	}

	/**
	 * Evaluate a condition expression
	 * @param condition Condition expression or field condition object
	 * @param data Data to evaluate against
	 */
	private evaluateCondition(condition: string | any, data: Record<string, any>): boolean {
		try {
			// Check if condition is a field condition object
			if (typeof condition === "object" && condition.field && condition.operator) {
				console.log("Evaluating field condition:", condition);
				const { field, operator, value } = condition;

				// Get the field value from data
				const fieldValue = data[field];
				console.log(`Comparing ${field}: ${fieldValue} ${operator} ${value}`);

				// Evaluate based on operator
				switch (operator) {
					case "==":
						return fieldValue == value;
					case "===":
						return fieldValue === value;
					case "!=":
						return fieldValue != value;
					case "!==":
						return fieldValue !== value;
					case ">":
						return fieldValue > value;
					case ">=":
						return fieldValue >= value;
					case "<":
						return fieldValue < value;
					case "<=":
						return fieldValue <= value;
					default:
						console.error(`Unknown operator: ${operator}`);
						return false;
				}
			} else if (typeof condition === "string") {
				// Legacy string condition evaluation
				console.log("Evaluating string condition:", condition);
				const keys = Object.keys(data);
				const values = Object.values(data);

				// Create a function that evaluates the condition with the data
				const func = new Function(...keys, `return ${condition};`);
				return func(...values);
			} else {
				console.error("Invalid condition format:", condition);
				return false;
			}
		} catch (error) {
			console.error("Error evaluating condition:", error);
			return false;
		}
	}

	/**
	 * Find a step that contains a specific subflow
	 * @param workflow Workflow to search in
	 * @param subflowId Subflow ID to find
	 */
	private findStepWithSubflow(workflow: Workflow, subflowId: string): WorkflowStep | any {
		for (const step of workflow.steps) {
			// Cast step to any to avoid TypeScript errors
			const anyStep = step as any;
			if (anyStep.subflow && anyStep.subflow.id === subflowId) {
				return anyStep;
			}

			// Check children
			if (anyStep.children && Array.isArray(anyStep.children)) {
				for (const child of anyStep.children) {
					if (child.subflow && child.subflow.id === subflowId) {
						return child;
					}

					// Recursively check deeper children
					if (child.children && Array.isArray(child.children)) {
						const found = this.findStepWithSubflow(
							{ ...workflow, steps: child.children as any },
							subflowId,
						);
						if (found) {
							return found;
						}
					}
				}
			}
		}

		return null;
	}

	/**
	 * Find the parent step of a child step
	 * @param workflow Workflow to search in
	 * @param childStepId Child step ID to find parent for
	 */
	private findParentStep(
		workflow: Workflow | WorkflowStep | any,
		childStepId: string,
	): WorkflowStep | any {
		// If it's a workflow, search in its steps
		const steps =
			"steps" in workflow ? workflow.steps : "children" in workflow ? workflow.children || [] : [];

		for (const step of steps) {
			// Check if this step has the child in its children
			if (step.children && Array.isArray(step.children)) {
				for (const child of step.children) {
					if (child.stepId === childStepId) {
						return step;
					}

					// Recursively check deeper children
					if (child.children && Array.isArray(child.children)) {
						const found = this.findParentStep(child, childStepId);
						if (found) {
							return found;
						}
					}
				}
			}

			// Check if this step has the child in its subflow
			if (step.subflow && step.subflow.steps) {
				for (const subflowStep of step.subflow.steps) {
					if (subflowStep.stepId === childStepId) {
						return step;
					}

					// Recursively check subflow children
					if (subflowStep.children && Array.isArray(subflowStep.children)) {
						const found = this.findParentStep(subflowStep, childStepId);
						if (found) {
							return step; // Return the step with the subflow, not the subflow step
						}
					}
				}
			}
		}

		return null;
	}

	/**
	 * Check if a step is completed
	 * @param stepId The ID of the step to check
	 * @returns True if the step is completed, false otherwise
	 */
	isStepCompleted(stepId: string): boolean {
		const isCompleted = this.state().completedSteps?.includes(stepId) || false;
		console.log(
			`WorkflowService.isStepCompleted: Step ${stepId} is ${
				isCompleted ? "completed" : "not completed"
			}`,
		);
		return isCompleted;
	}

	/**
	 * Update current step ID for display purposes without triggering navigation
	 * @param stepId The step ID to set as current
	 */
	updateCurrentStepForDisplay(stepId: string): void {
		this.state.update((state) => ({
			...state,
			currentStepId: stepId,
		}));
	}

	/**
	 * Mark a step as completed
	 */
	markStepCompleted(stepId: string): void {
		if (!this.completedSteps()) {
			this.state.update((state) => ({
				...state,
				completedSteps: [],
			}));
		}

		// Only add if not already in the array
		if (!this.completedSteps().includes(stepId)) {
			console.log(`Marking step ${stepId} as completed`);
			this.completedSteps().push(stepId);

			// If this is a child step (e.g., "1.3.2" or "1.3"), mark parent steps as completed
			// when all their children are completed
			if (stepId.includes(".")) {
				const parts = stepId.split(".");

				// Handle different levels of nesting
				if (parts.length >= 2) {
					// Check immediate parent first (for steps like "1.3.2", the parent would be "1.3")
					if (parts.length >= 3) {
						const immediateParentStepId = parts.slice(0, 2).join(".");
						console.log(
							`Checking if immediate parent ${immediateParentStepId} should be marked as completed`,
						);
						this.checkAndMarkParentAsCompleted(immediateParentStepId);
					}

					// Also check the top-level parent (e.g., "1")
					const topLevelParentStepId = parts[0];
					console.log(
						`Checking if top-level parent ${topLevelParentStepId} should be marked as completed`,
					);
					this.checkAndMarkParentAsCompleted(topLevelParentStepId);
				}
			}

			// Check if this step is the last child of a parent step
			// This is needed for cases where the parent step ID doesn't follow the dot notation pattern
			const allSteps: WorkflowStep[] = [];
			this.currentWorkflow()?.steps.forEach((group) => {
				// Check if this step is a direct child of a top-level step group
				if (group.children && group.children.some((child) => child.stepId === stepId)) {
					console.log(`Step ${stepId} is a direct child of top-level group ${group.stepId}`);
					this.checkAndMarkParentAsCompleted(group.stepId);
				}
				allSteps.push(...group.children);
			});

			// Check if this step is a child of any other step
			for (const step of allSteps) {
				if (step.children && step.children.some((child) => child.stepId === stepId)) {
					console.log(`Step ${stepId} is a child of step ${step.stepId}`);
					this.checkAndMarkParentAsCompleted(step.stepId);
				}
			}

			// Check if this step has a condition with forceCompleteParent flag
			const step = this.findStepById(stepId);
			if (step && step.next) {
				// Find if any of the next conditions has forceCompleteParent flag
				for (const nextItem of step.next) {
					if (typeof nextItem !== "string" && nextItem.forceCompleteParent) {
						// Check if this condition would be true based on the current context
						const condition = nextItem as WorkflowCondition;
						try {
							const result = this.evaluateCondition(condition.condition, this.workflowData());
							if (result) {
								console.log(
									`Step ${stepId} has a condition with forceCompleteParent flag that evaluates to true`,
								);

								// If we're in a child step, mark the parent step as completed
								if (stepId.includes(".")) {
									const mainStepId = stepId.split(".")[0];
									console.log(
										`Force marking main step ${mainStepId} as completed due to forceCompleteParent flag in markStepCompleted`,
									);

									if (!this.completedSteps().includes(mainStepId)) {
										this.completedSteps().push(mainStepId);
									}
								}

								break;
							}
						} catch (error) {
							console.error("Error evaluating condition:", error);
						}
					}
				}
			}

			// Update state to trigger UI refresh
			this.state.update((state) => ({ ...state }));
		}
	}

	/**
	 * Check if a parent step should be marked as completed and mark it if needed
	 */
	private checkAndMarkParentAsCompleted(parentStepId: string): void {
		console.log(`Checking if parent step ${parentStepId} should be marked as completed`);

		// Check if all children of this parent are completed
		const allChildrenCompleted = this.areAllChildrenCompleted(parentStepId);

		console.log(`All children of parent step ${parentStepId} completed: ${allChildrenCompleted}`);

		// If all children are completed, mark the parent as completed too
		if (allChildrenCompleted && !this.completedSteps()?.includes(parentStepId)) {
			this.completedSteps()?.push(parentStepId);
			console.log(
				`Marking parent step ${parentStepId} as completed because all children are completed`,
			);

			// If this is a top-level step, also check if we need to update the UI
			this.state.update((state) => ({ ...state }));
		} else if (!allChildrenCompleted) {
			console.log(`Not all children of parent step ${parentStepId} are completed yet`);
		} else if (this.completedSteps()?.includes(parentStepId)) {
			console.log(`Parent step ${parentStepId} is already marked as completed`);
		}
	}

	/**
	 * Check if all children of a step are completed
	 */
	private areAllChildrenCompleted(parentStepId: string): boolean {
		// Find the parent step
		const parentStep = this.findStepById(parentStepId);
		if (!parentStep || !parentStep.children || parentStep.children.length === 0) {
			// If we can't find the parent step using findStepById, try to find it in the main steps
			// This is needed because findStepById doesn't search through the top-level step groups
			const mainStep = this.currentWorkflow()?.steps.find((step) => step.stepId === parentStepId);
			if (mainStep && mainStep.children && mainStep.children.length > 0) {
				// Check if all children of the main step are completed
				return mainStep.children.every((child) => this.completedSteps()?.includes(child.stepId));
			}
			return false;
		}

		// Check if all children are in the completedSteps array
		return parentStep.children.every((child: any) => this.completedSteps()?.includes(child.stepId));
	}

	/**
	 * Find a step by ID in the workflow
	 * @param stepId The step ID to find
	 * @param workflowId Optional workflow ID (defaults to current workflow)
	 */
	findStepById(stepId: string, workflowId?: string): WorkflowStep | any {
		// Get the workflow - either current or specified
		const workflow = workflowId
			? this.workflowRegistry.getWorkflow(workflowId)
			: this.currentWorkflow();

		if (!workflow) {
			console.error("No workflow available for findStepById");
			return null;
		}

		console.log(`Finding step with ID: ${stepId} in workflow: ${workflow.id}`);

		// First, check if this is a top-level step group
		const topLevelStep = workflow.steps.find((step) => step.stepId === stepId);
		if (topLevelStep) {
			console.log(`Found top-level step: ${stepId}`);
			return topLevelStep as WorkflowStep;
		}

		// Helper function to search recursively through steps
		const findStep = (steps: WorkflowStep[]): WorkflowStep | null => {
			for (const step of steps) {
				if (step.stepId === stepId) {
					console.log(`Found step: ${stepId}`);
					return step;
				}

				// Check children if they exist
				if (step.children && step.children.length > 0) {
					const foundInChildren = findStep(step.children);
					if (foundInChildren) {
						return foundInChildren;
					}
				}

				// Check subflow if it exists
				if (step.subflow && step.subflow.steps && step.subflow.steps.length > 0) {
					const foundInSubflow = findStep(step.subflow.steps);
					if (foundInSubflow) {
						return foundInSubflow;
					}
				}
			}
			return null;
		};

		// Flatten the step groups and search
		const allSteps: WorkflowStep[] = [];
		workflow.steps.forEach((group) => {
			if (group.children && group.children.length > 0) {
				allSteps.push(...group.children);
			}
		});

		const result = findStep(allSteps);
		if (!result) {
			console.log(`Step with ID ${stepId} not found in workflow ${workflow.id}`);
		}

		return result;
	}
}
