import { Injectable } from "@angular/core";
import { loanApplicationWorkflow } from "../../domain/workflow/loan-application.workflow";
import { Workflow } from "../../domain/workflow/workflow.model";

/**
 * Workflow Registry Service
 * Manages all available workflows in the application
 */
@Injectable({
	providedIn: "root",
})
export class WorkflowRegistryService {
	private workflows: Map<string, Workflow> = new Map();

	constructor() {
		// Register built-in workflows
		console.log("Registering loan application workflow:", loanApplicationWorkflow);
		this.registerWorkflow(loanApplicationWorkflow);
	}

	/**
	 * Register a workflow
	 * @param workflow Workflow to register
	 */
	registerWorkflow(workflow: Workflow): void {
		console.log(`Registering workflow with ID: ${workflow.id}`);
		this.workflows.set(workflow.id, workflow);
		console.log("Current registered workflows:", Array.from(this.workflows.keys()));
	}

	/**
	 * Get a workflow by ID
	 * @param id Workflow ID
	 */
	getWorkflow(id: string): Workflow | undefined {
		console.log(`Getting workflow with ID: ${id}`);
		const workflow = this.workflows.get(id);
		console.log("Found workflow:", workflow);
		return workflow;
	}

	/**
	 * Get all registered workflows
	 */
	getAllWorkflows(): Workflow[] {
		return Array.from(this.workflows.values());
	}

	/**
	 * Check if a workflow exists
	 * @param id Workflow ID
	 */
	hasWorkflow(id: string): boolean {
		return this.workflows.has(id);
	}

	/**
	 * Remove a workflow
	 * @param id Workflow ID
	 */
	removeWorkflow(id: string): boolean {
		return this.workflows.delete(id);
	}
}
