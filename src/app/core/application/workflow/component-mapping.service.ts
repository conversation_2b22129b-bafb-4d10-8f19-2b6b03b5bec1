import { Injectable } from "@angular/core";
import { PageId } from "../../domain/workflow/workflow.model";

/**
 * Service for mapping page IDs to components
 */
@Injectable({
	providedIn: "root",
})
export class ComponentMappingService {
	/**
	 * Get component for a page ID
	 * @param pageId Page ID
	 */
	getComponentForPage(pageId: PageId): Promise<any> {
		console.log("Getting component for page ID:", pageId);

		// Map page IDs to component imports
		switch (pageId) {
			case PageId.LOAN_TYPE:
				console.log("Loading LoanTypeComponent");
				return import("../../../adapters/primary/loan/loan-type/loan-type.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.LoanTypeComponent;
					})
					.catch((error) => {
						console.error("Error loading LoanTypeModalComponent:", error);
						throw error;
					});

			case PageId.CUSTOMER_TYPE:
				console.log("Loading CustomerTypeModalComponent");
				return import("../../../adapters/primary/loan/customer-type/customer-type.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.CustomerTypeComponent;
					})
					.catch((error) => {
						console.error("Error loading CustomerTypeModalComponent:", error);
						throw error;
					});

			case PageId.READ_ID_CARD_GUIDELINE:
				console.log("Loading IdCardGuidelineModalComponent");
				return import(
					"../../../adapters/primary/loan/id-card-guideline/id-card-guideline.component"
				)
					.then((m) => {
						console.log("Loaded module:", m);
						return m.IdCardGuidelineModalComponent;
					})
					.catch((error) => {
						console.error("Error loading IdCardGuidelineModalComponent:", error);
						throw error;
					});

			case PageId.READ_ID_CARD_QR:
				console.log("Loading IdCardQrModalComponent");
				return import("../../../adapters/primary/loan/id-card-qr/id-card-qr.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.IdCardQrComponent;
					})
					.catch((error) => {
						console.error("Error loading IdCardQrModalComponent:", error);
						throw error;
					});

			case PageId.PERSONAL_INFO:
				console.log("Loading AddressContactModalComponent");
				return import("../../../adapters/primary/loan/personal-info/personal-info.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.PersonalInfoComponent;
					})
					.catch((error) => {
						console.error("Error loading AddressContactModalComponent:", error);
						throw error;
					});

			case PageId.ID_PHOTO:
				console.log("Loading IdPhotoModalComponent");
				return import("../../../adapters/primary/loan/id-photo/id-photo.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.IdPhotoModalComponent;
					})
					.catch((error) => {
						console.error("Error loading IdPhotoModalComponent:", error);
						throw error;
					});

			case PageId.TAKE_ID_PHOTO_GUIDE:
				console.log("Loading PhotoGuideModalComponent");
				return import(
					"../../../adapters/primary/loan/take-id-photo-guideline/take-id-photo-guideline.component"
				)
					.then((m) => {
						console.log("Loaded module:", m);
						return m.PhotoGuideModalComponent;
					})
					.catch((error) => {
						console.error("Error loading PhotoGuideModalComponent:", error);
						throw error;
					});

			case PageId.WEBCAM_CHECK:
				console.log("Loading WebcamCheckComponent");
				return import("../../../adapters/primary/loan/webcam-check/webcam-check.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.WebcamCheckComponent;
					})
					.catch((error) => {
						console.error("Error loading CameraCheckModalComponent:", error);
						throw error;
					});

			case PageId.TAKE_ID_PHOTO:
				console.log("Loading TakeSelfieModalComponent");
				return import("../../../adapters/primary/loan/take-id-photo/take-id-photo.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.TakeIdPhotoComponent;
					})
					.catch((error) => {
						console.error("Error loading TakeSelfieModalComponent:", error);
						throw error;
					});

			case PageId.CONFIRM_ID_PHOTO:
				console.log("Loading ConfirmSelfieModalComponent");
				return import("../../../adapters/primary/loan/confirm-id-photo/confirm-id-photo.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.ConfirmIdPhotoComponent;
					})
					.catch((error) => {
						console.error("Error loading ConfirmSelfieModalComponent:", error);
						throw error;
					});

			case PageId.ADDRESS_CONTACT:
				console.log("Loading PersonalInfoModalComponent");
				return import("../../../adapters/primary/loan/address-contact/address-contact.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.AddressContactComponent;
					})
					.catch((error) => {
						console.error("Error loading PersonalInfoModalComponent:", error);
						throw error;
					});

			case PageId.WORK_INFO:
				console.log("Loading WorkInfoComponent");
				return import("../../../adapters/primary/loan/work-info/work-info.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.WorkInfoComponent;
					})
					.catch((error) => {
						console.error("Error loading EmploymentModalComponent:", error);
						throw error;
					});

			case PageId.SELECT_INSTALLMENT:
				console.log("Loading SelectInstallmentComponent");
				return import(
					"../../../adapters/primary/loan/select-installment/select-installment.component"
				)
					.then((m) => {
						console.log("Loaded module:", m);
						return m.SelectInstallmentComponent;
					})
					.catch((error) => {
						console.error("Error loading SelectInstallmentComponent:", error);
						throw error;
					});

			case PageId.ATTACH_DOCUMENTS:
				console.log("Loading UploadDocsModalComponent");
				return import("../../../adapters/primary/loan/attach-evidence/attach-evidence.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.AttachEvidenceComponent;
					})
					.catch((error) => {
						console.error("Error loading UploadDocsModalComponent:", error);
						throw error;
					});

			case PageId.REVIEW_PRE_SUBMIT:
				console.log("Loading ReviewPreSubmitModalComponent");
				return import(
					"../../../adapters/primary/loan/review-pre-submit/review-pre-submit.component"
				)
					.then((m) => {
						console.log("Loaded module:", m);
						return m.ReviewPresubmitComponent;
					})
					.catch((error) => {
						console.error("Error loading ReviewSubmitModalComponent:", error);
						throw error;
					});

			case PageId.LOAN_SUBMISSION_CONFIRMATION:
				console.log("Loading LoanSubmissionConfirmationComponent");
				return import(
					"../../../adapters/primary/loan/loan-submission-confirmation/loan-submission-confirmation.component"
				)
					.then((m) => {
						console.log("Loaded module:", m);
						return m.LoanSubmissionConfirmationComponent;
					})
					.catch((error) => {
						console.error("Error loading LoanSubmissionConfirmationComponent:", error);
						throw error;
					});

			case PageId.CUSTOMER_CONFIRM_GUIDELINE:
				console.log("Loading CustomerConfirmGuidelineComponent");
				return import(
					"../../../adapters/primary/loan/customer-confirm-guideline/customer-confirm-guideline.component"
				)
					.then((m) => {
						console.log("Loaded module:", m);
						return m.CustomerConfirmGuidelineComponent;
					})
					.catch((error) => {
						console.error("Error loading CustomerConfirmGuidelineComponent:", error);
						throw error;
					});

			case PageId.TERMS_CONDITIONS:
				console.log("Loading TermsComponent");
				return import("../../../adapters/primary/loan/terms/terms.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.TermsComponent;
					})
					.catch((error) => {
						console.error("Error loading TermsComponent:", error);
						throw error;
					});

			case PageId.OTP_PHONE:
				console.log("Loading OtpPhoneComponent");
				return import("../../../adapters/primary/loan/otp-phone/otp-phone.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.OtpPhoneComponent;
					})
					.catch((error) => {
						console.error("Error loading OtpPhoneComponent:", error);
						throw error;
					});

			case PageId.OTP_EMAIL:
				console.log("Loading OtpEmailComponent");
				return import("../../../adapters/primary/loan/otp-email/otp-email.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.OtpEmailComponent;
					})
					.catch((error) => {
						console.error("Error loading OtpEmailComponent:", error);
						throw error;
					});

			case PageId.CONSENT:
				console.log("Loading ConsentComponent");
				return import("../../../adapters/primary/loan/consent/consent.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.ConsentComponent;
					})
					.catch((error) => {
						console.error("Error loading ConsentComponent:", error);
						throw error;
					});

			case PageId.REVIEW_FINAL_SUBMIT:
				console.log("Loading ReviewFinalSubmitComponent");
				return import(
					"../../../adapters/primary/loan/review-final-submit/review-final-submit.component"
				)
					.then((m) => {
						console.log("Loaded module:", m);
						return m.ReviewFinalSubmitComponent;
					})
					.catch((error) => {
						console.error("Error loading ReviewFinalSubmitComponent:", error);
						throw error;
					});

			case PageId.FACE_PHOTO:
				console.log("Loading FacePhotoComponent");
				return import("../../../adapters/primary/loan/face-photo/face-photo.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.FacePhotoComponent;
					})
					.catch((error) => {
						console.error("Error loading FacePhotoComponent:", error);
						throw error;
					});

			case PageId.TAKE_FACE_PHOTO_GUIDE:
				console.log("Loading TakeFacePhotoGuideComponent");
				return import(
					"../../../adapters/primary/loan/take-face-photo-guideline/take-face-photo-guideline.component"
				)
					.then((m) => {
						console.log("Loaded module:", m);
						return m.TakeFacePhotoGuideComponent;
					})
					.catch((error) => {
						console.error("Error loading TakeFacePhotoGuideComponent:", error);
						throw error;
					});

			case PageId.TAKE_FACE_PHOTO:
				console.log("Loading FaceDetectionComponent");
				return import("../../../adapters/primary/loan/take-face-photo/take-face-photo.component")
					.then((m) => {
						console.log("Loaded module:", m);
						return m.TakeFacePhotoComponent;
					})
					.catch((error) => {
						console.error("Error loading FaceDetectionComponent:", error);
						throw error;
					});

			case PageId.CONFIRM_FACE_PHOTO:
				console.log("Loading ConfirmFacePhotoComponent");
				return import(
					"../../../adapters/primary/loan/confirm-face-photo/confirm-face-photo.component"
				)
					.then((m) => {
						console.log("Loaded module:", m);
						return m.ConfirmFacePhotoComponent;
					})
					.catch((error) => {
						console.error("Error loading ConfirmFacePhotoComponent:", error);
						throw error;
					});

			default:
				console.error(`No component mapping found for page ID: ${pageId}`);
				return Promise.reject(`No component mapping found for page ID: ${pageId}`);
		}
	}
}
