import { computed, inject, Injectable, signal } from "@angular/core";
import { <PERSON><PERSON><PERSON>ontroller, ModalOptions } from "@ionic/angular/standalone";
import { ModalState } from "../../domain/workflow/workflow-event.model";
import { PageId } from "../../domain/workflow/workflow.model";
import { ComponentMappingService } from "./component-mapping.service";

/**
 * Modal service for handling workflow modals
 */
@Injectable({
	providedIn: "root",
})
export class ModalService {
	// Make modalController public so it can be accessed by WorkflowModalService
	public modalController = inject(ModalController);
	private componentMapping = inject(ComponentMappingService);

	// Store active modals with their IDs - public so WorkflowModalService can access it
	public modals = signal<Map<string, ModalState>>(new Map());

	// Store active general modals (not part of workflow)
	private activeGeneralModals: HTMLIonModalElement[] = [];

	// Computed signal for checking if any modal is open
	readonly hasOpenModals = computed(() => this.modals().size > 0);

	// Computed signal for the current top-level modal
	readonly currentModal = computed(() => {
		const modalsMap = this.modals();
		if (modalsMap.size === 0) return null;

		// Find non-subflow modals
		const mainModals = Array.from(modalsMap.entries()).filter(([_, state]) => !state.isSubflow);

		if (mainModals.length === 0) return null;

		// Return the last main modal
		return mainModals[mainModals.length - 1];
	});

	// Computed signal for the current subflow modal (if any)
	readonly currentSubflowModal = computed(() => {
		const modalsMap = this.modals();
		if (modalsMap.size === 0) return null;

		// Find subflow modals
		const subflowModals = Array.from(modalsMap.entries()).filter(([_, state]) => state.isSubflow);

		if (subflowModals.length === 0) return null;

		// Return the last subflow modal
		return subflowModals[subflowModals.length - 1];
	});

	/**
	 * Open a new modal
	 * @param component Component to display in the modal
	 * @param props Component properties
	 * @param modalId Unique ID for the modal
	 * @param isSubflow Whether this is a subflow modal
	 * @param parentModalId Parent modal ID (for subflows)
	 */
	async openModal(
		component: any,
		props: Record<string, any> = {},
		modalId: string,
		parentModalId?: string,
	): Promise<HTMLIonModalElement> {
		console.log("Opening modal with ID:", modalId);
		console.log("Modal component:", component);
		console.log("Modal props:", props);

		try {
			// Dismiss any existing modals with the same ID to prevent conflicts
			try {
				await this.modalController.dismiss(undefined, undefined, modalId);
				console.log(`Dismissed existing modal with ID: ${modalId}`);
			} catch (dismissError) {
				// It's okay if there's no modal to dismiss
				console.log(`No existing modal with ID: ${modalId} to dismiss`);
			}

			// Create modal with improved configuration
			console.log("Creating modal with component:", component);

			// Determine CSS classes to apply
			let cssClasses = [];

			// Check if this is a subflow modal by checking the isSubflow property
			// or by checking if the modalId starts with 'subflow-modal-'
			const isSubflowModal =
				props["isSubflow"] === true || (modalId && modalId.startsWith("subflow-modal-"));

			console.log(
				"Is subflow modal?",
				isSubflowModal,
				"props[isSubflow]:",
				props["isSubflow"],
				"modalId:",
				modalId,
			);

			if (isSubflowModal) {
				cssClasses.push("subflow-modal");
				cssClasses.push("modal-blur-backdrop");
			} else {
				cssClasses.push("workflow-modal");
				cssClasses.push("fullscreen-modal"); // Add explicit fullscreen class
			}

			// Add any additional classes from props
			if (props["cssClass"]) {
				if (Array.isArray(props["cssClass"])) {
					cssClasses = [...cssClasses, ...props["cssClass"]];
				} else if (typeof props["cssClass"] === "string") {
					cssClasses.push(props["cssClass"]);
				}
			}

			console.log("Applying CSS classes to modal:", cssClasses);

			// Set additional options for fullscreen
			const modalOptions: any = {
				component,
				componentProps: props,
				backdropDismiss: false,
				cssClass: cssClasses,
				id: modalId, // Set the modal ID explicitly
				showBackdrop: true,
				animated: true,
				keyboardClose: true,
				presentingElement: (await this.modalController.getTop()) || undefined,
			};

			// Add fullscreen option for workflow modals
			// if (!props['isSubflow']) {
			//   modalOptions.mode = 'ios'; // Force iOS mode for consistent rendering
			//   modalOptions.initialBreakpoint = 1; // Full height
			//   modalOptions.breakpoints = [0, 1]; // Only allow full height
			// }

			const modal = await this.modalController.create(modalOptions);

			console.log("Modal created:", modal);

			// Store modal state
			this.modals.update((modals) => {
				const newModals = new Map(modals);
				newModals.set(modalId, {
					isOpen: true,
					component,
					componentProps: props,
					isSubflow: props["isSubflow"],
					parentModalId,
				});
				return newModals;
			});
			console.log("Modal state stored, current modals:", this.modals());

			// Present the modal
			console.log("Presenting modal...");
			await modal.present();
			console.log("Modal presented successfully");

			// Set up listeners for modal events
			modal.onDidDismiss().then(() => {
				console.log(`Modal with ID: ${modalId} was dismissed`);
				this.modals.update((modals) => {
					const newModals = new Map(modals);
					newModals.delete(modalId);
					return newModals;
				});
			});

			// Return the modal element
			return modal;
		} catch (error) {
			console.error("Error opening modal:", error);
			// Remove the modal from our state if there was an error
			this.modals.update((modals) => {
				const newModals = new Map(modals);
				newModals.delete(modalId);
				return newModals;
			});
			throw error;
		}
	}

	/**
	 * Close a modal by ID
	 * @param modalId Modal ID to close
	 */
	async closeModal(modalId: string): Promise<void> {
		console.log(`Closing modal with ID: ${modalId}`);

		const modalsMap = this.modals();
		if (!modalsMap.has(modalId)) {
			console.warn(`Modal with ID ${modalId} not found in our state`);
			// Try to dismiss it anyway in case it exists in the DOM but not in our state
		}

		try {
			// Dismiss the modal
			console.log(`Dismissing modal with ID: ${modalId}`);
			await this.modalController.dismiss(undefined, undefined, modalId);
			console.log(`Modal with ID: ${modalId} dismissed successfully`);
		} catch (error) {
			console.warn(`Error dismissing modal with ID ${modalId}:`, error);
			// Continue with removing it from our state even if the dismiss fails
		} finally {
			// Remove from our state regardless of whether the dismiss succeeded
			this.modals.update((modals) => {
				const newModals = new Map(modals);
				newModals.delete(modalId);
				return newModals;
			});
			console.log(`Modal with ID: ${modalId} removed from state`);
		}
	}

	/**
	 * Close all modals
	 */
	async closeAllModals(): Promise<void> {
		console.log("Closing all modals");

		try {
			// Try to dismiss all modals
			await this.modalController.dismiss();
			console.log("All modals dismissed successfully");
		} catch (error) {
			console.warn("Error dismissing all modals:", error);

			// If the global dismiss fails, try to dismiss each modal individually
			const modalIds = Array.from(this.modals().keys());
			console.log("Attempting to dismiss modals individually:", modalIds);

			for (const modalId of modalIds) {
				try {
					await this.closeModal(modalId);
				} catch (individualError) {
					console.warn(`Error dismissing individual modal ${modalId}:`, individualError);
				}
			}
		} finally {
			// Clear our state regardless of whether the dismiss succeeded
			this.modals.set(new Map());
			console.log("All modals removed from state");
		}
	}

	/**
	 * Close all subflow modals
	 */
	async closeAllSubflowModals(): Promise<void> {
		console.log("Closing all subflow modals");

		const modalsMap = this.modals();
		const subflowModalIds = Array.from(modalsMap.entries())
			.filter(([_, state]) => state.isSubflow)
			.map(([id]) => id);

		console.log("Subflow modal IDs to close:", subflowModalIds);

		for (const id of subflowModalIds) {
			try {
				await this.closeModal(id);
			} catch (error) {
				console.warn(`Error closing subflow modal ${id}:`, error);
			}
		}

		console.log("All subflow modals closed");
	}

	/**
	 * Get component for a page ID
	 * @param pageId Page ID
	 */
	getComponentForPage(pageId: PageId): Promise<any> {
		return this.componentMapping.getComponentForPage(pageId);
	}

	/**
	 * Open a general modal (not part of the workflow)
	 * @param options Modal options
	 * @param cssClass Additional CSS class(es) to apply
	 * @returns The created modal element
	 */
	async openGeneralModal(
		options: ModalOptions,
		cssClass: string | string[] = "general-modal",
	): Promise<HTMLIonModalElement> {
		console.log("Opening general modal with options:", options);

		// Prepare CSS classes
		let cssClasses: string[] = [];

		// Add the provided cssClass parameter
		if (typeof cssClass === "string") {
			cssClasses.push(cssClass);
		} else if (Array.isArray(cssClass)) {
			cssClasses = [...cssClass];
		}

		// Add modal-blur-backdrop class
		cssClasses.push("modal-blur-backdrop");

		// Add any additional classes from options
		if (options.cssClass) {
			if (Array.isArray(options.cssClass)) {
				cssClasses = [...cssClasses, ...options.cssClass];
			} else if (typeof options.cssClass === "string") {
				cssClasses.push(options.cssClass);
			}
		}

		console.log("Using CSS classes:", cssClasses);

		// Create modal
		const modal = await this.modalController.create({
			...options,
			cssClass: cssClasses,
			backdropDismiss: options.backdropDismiss ?? true,
			showBackdrop: options.showBackdrop ?? true,
			animated: options.animated ?? true,
			keyboardClose: options.keyboardClose ?? true,
		});

		// Present modal
		await modal.present();
		console.log("General modal presented successfully");

		// Store modal reference
		this.activeGeneralModals.push(modal);

		// Set up dismiss handler to remove from activeGeneralModals
		modal.onDidDismiss().then(() => {
			console.log("General modal dismissed");
			const index = this.activeGeneralModals.indexOf(modal);
			if (index > -1) {
				this.activeGeneralModals.splice(index, 1);
			}
		});

		return modal;
	}

	/**
	 * Close a general modal
	 * @param modal Modal element to close
	 * @param data Data to pass back when the modal closes
	 * @param role Role to pass back when the modal closes
	 */
	async closeGeneralModal(data?: any, role?: string): Promise<boolean> {
		console.log("Closing general modal");

		try {
			const lastModal = this.activeGeneralModals[this.activeGeneralModals.length - 1];
			await lastModal.dismiss(data, role);
			console.log("General modal dismissed successfully");
			return true;
		} catch (error) {
			console.error("Error dismissing general modal:", error);
			return false;
		}
	}

	/**
	 * Close the most recently opened general modal
	 * @param data Data to pass back when the modal closes
	 * @param role Role to pass back when the modal closes
	 */
	async closeLastGeneralModal(data?: any, role?: string): Promise<boolean> {
		if (this.activeGeneralModals.length === 0) {
			console.warn("No general modals to close");
			return false;
		}

		return this.closeGeneralModal(data, role);
	}

	/**
	 * Close all general modals
	 */
	async closeAllGeneralModals(): Promise<void> {
		console.log("Closing all general modals");

		// Create a copy of the array to avoid issues with elements being removed during iteration
		const modals = [...this.activeGeneralModals];

		for (const modal of modals) {
			try {
				await modal.dismiss();
			} catch (error) {
				console.warn("Error dismissing general modal:", error);
			}
		}

		// Clear the array
		this.activeGeneralModals = [];
		console.log("All general modals closed");
	}
}
