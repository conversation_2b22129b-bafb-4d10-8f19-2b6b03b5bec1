# Workflow Initialization Examples

## Overview

The WorkflowModalService now supports initializing workflows before starting them. This allows for more flexible workflow management where you can:

1. Initialize a workflow with initial data and completed steps
2. Start the workflow later at any desired step
3. Check if a workflow is already initialized

## New Methods

### `initializeWorkflow()`

Initialize a workflow without starting it.

```typescript
async initializeWorkflow(
  workflowId: string = "loan-application-workflow",
  workflowData: Record<string, any> = {},
  completedStepIds: string[] = []
): Promise<void>
```

### `startWorkflow()`

Start a workflow (must be used after initialization).

```typescript
async startWorkflow(
  startStepId?: string,
  workflowData: Record<string, any> = {}
): Promise<void>
```

### `initializeAndStartWorkflow()`
Initialize and start a workflow in one call (for convenience).

```typescript
async initializeAndStartWorkflow(
  workflowId: string = "loan-application-workflow",
  startStepId?: string,
  workflowData: Record<string, any> = {},
  completedStepIds: string[] = []
): Promise<void>
```

### `isWorkflowInitialized`

Computed signal to check if a workflow is initialized.

```typescript
readonly isWorkflowInitialized = computed(() => this.currentWorkflowId() !== null);
```

## Usage Examples

### Example 1: Initialize and Start Later

```typescript
// In your component
constructor(private workflowService: WorkflowModalService) {}

async initializeMyWorkflow() {
  // Initialize the workflow with some initial data
  await this.workflowService.initializeWorkflow(
    "loan-application-workflow",
    {
      userType: "existing-customer",
      loanAmount: 50000
    },
    ["1.1", "1.2"] // Mark these steps as completed
  );

  console.log("Workflow initialized:", this.workflowService.isWorkflowInitialized());
}

async startMyWorkflow() {
  // Start the workflow at a specific step
  await this.workflowService.startWorkflow(
    undefined, // Use already initialized workflow
    "1.3",     // Start at step 1.3
    { additionalData: "value" }
  );
}
```

### Example 2: Initialize and Start Immediately

```typescript
async quickStart() {
  // Initialize workflow
  await this.workflowService.initializeWorkflow(
    "loan-application-workflow",
    { userType: "new-customer" }
  );

  // Start immediately at the beginning
  await this.workflowService.startWorkflow();
}
```

### Example 3: Check and Initialize if Needed

```typescript
async ensureWorkflowReady() {
  if (!this.workflowService.isWorkflowInitialized()) {
    await this.workflowService.initializeWorkflow(
      "loan-application-workflow",
      { source: "mobile-app" }
    );
  }

  // Now start at a specific step
  await this.workflowService.startWorkflow(
    undefined,
    "2.1"
  );
}
```

### Example 4: Different Workflows

```typescript
async initializeDifferentWorkflows() {
  // Initialize loan application workflow
  await this.workflowService.initializeWorkflow(
    "loan-application-workflow",
    { productType: "personal-loan" }
  );

  // Later, you could initialize a different workflow
  // (This would replace the current one)
  await this.workflowService.initializeWorkflow(
    "account-opening-workflow",
    { accountType: "savings" }
  );
}
```

### Example 5: Using with Conditional Logic

```typescript
async conditionalWorkflowStart() {
  const userProfile = await this.getUserProfile();

  if (userProfile.isExistingCustomer) {
    // Initialize with completed KYC steps
    await this.workflowService.initializeWorkflow(
      "loan-application-workflow",
      { customerId: userProfile.id },
      ["1.1", "1.2", "1.3"] // Skip KYC steps
    );

    // Start at loan details step
    await this.workflowService.startWorkflow(undefined, "2.1");
  } else {
    // Initialize for new customer
    await this.workflowService.initializeWorkflow(
      "loan-application-workflow",
      { isNewCustomer: true }
    );

    // Start from the beginning
    await this.workflowService.startWorkflow();
  }
}
```

## Benefits

1. **Separation of Concerns**: Initialize workflow state separately from starting the UI flow
2. **Flexibility**: Start at any step after initialization
3. **State Management**: Pre-populate workflow data and completed steps
4. **Conditional Flows**: Different initialization based on user context
5. **Performance**: Initialize once, start multiple times if needed

## Migration from Old Methods

### Before:

```typescript
// Old way - had to start immediately
await this.workflowService.dispatch({
	command: "start",
	targetStepId: "loan-application-workflow",
	payload: { userData: data },
});
```

### After:

```typescript
// New way - more control
await this.workflowService.initializeWorkflow("loan-application-workflow", { userData: data });

// Start when ready
await this.workflowService.startWorkflow(undefined, "1.1");
```
