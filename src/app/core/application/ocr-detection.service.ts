import { Injectable } from "@angular/core";
import { OCRClient } from "tesseract-wasm";

export const ENGLISH_FAST_MODEL: string =
	"../../../assets/tesseract/models/eng-fast.traineddata";
export const WORKER_URL: string =
	"../../../assets/tesseract/tesseract-worker.js";

@Injectable({
	providedIn: "root",
})
export class OcrDetectionService {
	private ocrClient: OCRClient | null = null;
	private currentModel: string | null = null;

	constructor() {}

	public getOcrClient(): OCRClient | null {
		return this.ocrClient;
	}

	public async initialize(model: string = ENGLISH_FAST_MODEL): Promise<void> {
		try {
			this.ocrClient = new OCRClient({ workerURL: WORKER_URL });
			if (!this.ocrClient) {
				console.warn("OcrService: OCR client is not initialized");
				return;
			}

			await this.ocrClient.loadModel(model);
			this.currentModel = model;
		} catch (error) {
			throw error;
		}
	}

	public isInitialized(): boolean {
		return !!this.ocrClient;
	}

	public isModelLoaded(): boolean {
		return !!this.currentModel;
	}

	/**
	 * Processes the given bitmap image and returns the HOCR data.
	 */
	public async getOCR(
		bitmap: ImageBitmap,
		shouldClear: boolean = true,
	): Promise<string> {
		if (!this.ocrClient) {
			throw new Error("OCR client is not initialized");
		}

		try {
			// Load the image and process OCR
			await this.ocrClient.loadImage(bitmap);
			const text = await this.ocrClient.getText();

			return text;
		} catch (error) {
			console.error("Error getting OCR text:", error);
			throw error;
		} finally {
			// Clear image only if the flag is true
			if (shouldClear) {
				await this.ocrClient?.clearImage();
			}
		}
	}

	/**
	 * Processes the given bitmap image and returns the HOCR data.
	 */
	public async getHOCR(bitmap: ImageBitmap): Promise<string> {
		try {
			if (!this.ocrClient) {
				throw new Error("OCR client is not initialized");
			}

			// Load the image only if it's different
			await this.ocrClient.loadImage(bitmap);
			return await this.ocrClient.getHOCR();
		} catch (error) {
			throw error;
		} finally {
			// clean up
			await this.ocrClient?.clearImage();
		}
	}

	/**
	 * Destroys the OCR client and resets the state.
	 */
	public destroyOcrClient(): void {
		if (this.ocrClient) {
			this.ocrClient.destroy();
			this.ocrClient = null;
			this.currentModel = null;
		}
	}

	/**
	 * Switches the current OCR model to the specified one.
	 */
	public async switchModel(newModel: string): Promise<void> {
		if (!this.ocrClient) {
			throw new Error("OCR client is not initialized");
		}

		// Load the model only if it's different
		if (this.currentModel !== newModel) {
			await this.initialize(newModel);
		}
	}

	public getCurrentModel(): string | null {
		return this.currentModel;
	}
}
