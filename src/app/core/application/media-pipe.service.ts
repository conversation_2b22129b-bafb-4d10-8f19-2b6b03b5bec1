import { Injectable } from "@angular/core";
import {
	Detection,
	FaceDetector,
	FaceDetectorOptions,
	FaceLandmarker,
	FaceLandmarkerOptions,
	FaceLandmarkerResult,
	FilesetResolver,
} from "@mediapipe/tasks-vision";

export type RunningMode = "IMAGE" | "VIDEO";

export type ProcessingUnit = "GPU" | "CPU";

@Injectable({
	providedIn: "root",
})
export class MediaPipeService {
	// Configuration
	private readonly vision = "../../../assets/media-pipe/wasm";
	private readonly faceDetectionModel =
		"../../../assets/media-pipe/blaze_face_short_range.tflite";
	private readonly faceLandmarkerModel =
		"../../../assets/media-pipe/face_landmarker.task";

	// Detector instances
	private faceLandmarker!: FaceLandmarker;
	private faceDetector!: FaceDetector;

	// Configurable options
	private minDetectionConfidence: number = 0.7;
	private minSuppressionThreshold: number = 0.3;
	private minFacePresenceConfidence: number = 0.7;
	private minTrackingConfidence: number = 0.7;
	private numFaces: number = 1;
	private runningMode: RunningMode = "IMAGE";
	private processingUnit: ProcessingUnit = "GPU";

	constructor() {}

	/**
	 * Initializes MediaPipe FaceDetector and FaceLandmarker.
	 * @returns {Promise<boolean>} True if initialization is successful, otherwise throws an error.
	 */
	async initialize(): Promise<boolean> {
		try {
			// Initialize FaceDetector
			this.faceDetector = await this.createFaceDetector();

			// Initialize FaceLandmarker
			this.faceLandmarker = await this.createFaceLandmarker();

			return true;
		} catch (error: any) {
			console.error("Error initializing MediaPipe:", error);

			// Fallback to CPU if GPU fails
			if (error.message.includes("Failed to initialize with GPU")) {
				console.warn("Failed to initialize with GPU. Falling back to CPU...");
				this.processingUnit = "CPU";

				try {
					// Retry with CPU
					this.faceDetector = await this.createFaceDetector();
					this.faceLandmarker = await this.createFaceLandmarker();

					return true;
				} catch (cpuError) {
					throw cpuError;
				}
			}

			throw error;
		}
	}

	/**
	 * Creates a FaceDetector instance.
	 * @returns {Promise<FaceDetector>} A FaceDetector instance.
	 */
	private async createFaceDetector(): Promise<FaceDetector> {
		const options: FaceDetectorOptions = {
			runningMode: this.runningMode,
			minDetectionConfidence: this.minDetectionConfidence,
			minSuppressionThreshold: this.minSuppressionThreshold,
			baseOptions: {
				modelAssetPath: this.faceDetectionModel,
				delegate: this.processingUnit,
			},
		};

		const filesetResolver = await FilesetResolver.forVisionTasks(this.vision);
		return FaceDetector.createFromOptions(filesetResolver, options);
	}

	/**
	 * Creates a FaceLandmarker instance.
	 * @returns {Promise<FaceLandmarker>} A FaceLandmarker instance.
	 */
	private async createFaceLandmarker(): Promise<FaceLandmarker> {
		const options: FaceLandmarkerOptions = {
			outputFaceBlendshapes: true,
			runningMode: this.runningMode,
			numFaces: this.numFaces,
			minFaceDetectionConfidence: this.minDetectionConfidence,
			minFacePresenceConfidence: this.minFacePresenceConfidence,
			minTrackingConfidence: this.minTrackingConfidence,
			baseOptions: {
				modelAssetPath: this.faceLandmarkerModel,
				delegate: this.processingUnit,
			},
		};

		const filesetResolver = await FilesetResolver.forVisionTasks(this.vision);
		return FaceLandmarker.createFromOptions(filesetResolver, options);
	}

	/**
	 * Checks if MediaPipe is initialized.
	 * @returns {boolean} True if both FaceDetector and FaceLandmarker are initialized.
	 */
	public isInitialized(): boolean {
		return !!this.faceDetector && !!this.faceLandmarker;
	}

	/**
	 * Detects faces in an image.
	 * @param {HTMLImageElement} imageElement - The image element to detect faces from.
	 * @returns {Detection[]} An array of detected faces.
	 */
	public detectFaces(imageElement: HTMLImageElement): Detection[] {
		if (!this.faceDetector) {
			throw new Error("FaceDetector is not initialized");
		}
		return this.faceDetector.detect(imageElement).detections;
	}

	/**
	 * Detects face landmarks in an image.
	 * @param {HTMLImageElement} imageElement - The image element to detect landmarks from.
	 * @returns {FaceLandmarkerResult} The detected face landmarks.
	 */
	public detectLandmarks(imageElement: HTMLImageElement): FaceLandmarkerResult {
		if (!this.faceLandmarker) {
			throw new Error("FaceLandmarker is not initialized");
		}
		return this.faceLandmarker.detect(imageElement);
	}

	/**
	 * Closes MediaPipe resources.
	 */
	public dispose(): void {
		this.faceLandmarker?.close();
		this.faceDetector?.close();
	}
}
