import { Injectable } from "@angular/core";
import { CircularProgressLoadingModalComponent } from "src/app/adapters/primary/shared/components/circular-progress-loading-modal/circular-progress-loading-modal.component";
import { StatusNotificationModalComponent } from "src/app/adapters/primary/shared/components/status-notification-modal/status-notification-modal.component";
import { ModalService } from "./workflow/modal.service";

export enum StatusType {
	SUCCESS = "success",
	WARNING = "warning",
	ERROR = "error",
	INFO = "info",
}

export interface StatusNotificationOptions {
	title?: string;
	message: string;
	description?: string;
	errorCode?: string;
	errorLabel?: string;
	primaryButtonLabel?: string;
	secondaryButtonLabel?: string;
	onPrimaryAction?: () => void;
	onSecondaryAction?: () => void;
	backdropDismiss?: boolean;
	fullscreen?: boolean; // Whether to display the modal in fullscreen mode
}

export interface CircularProgressLoadingOptions {
	title?: string;
	statusMessage?: string;
	description?: string;
	progress?: number;
	autoProgress?: boolean;
	progressDuration?: number;
	progressStep?: number;
	showIcon?: boolean;
	isComplete?: boolean;
	showButtons?: boolean;
	cancelButtonLabel?: string;
	actionButtonLabel?: string;
	onCancel?: () => void;
	onAction?: () => void;
	autoClose?: boolean;
	autoCloseDelay?: number;
	backdropDismiss?: boolean;
	fullscreen?: boolean;
	// For async operations
	asyncOperation?: () => Promise<any>;
	onOperationComplete?: (result: any) => void;
	onOperationError?: (error: any) => void;
	indeterminateProgress?: boolean; // Show indeterminate progress (pulsing animation)
}

@Injectable({
	providedIn: "root",
})
export class StatusNotificationService {
	private activeModal: HTMLIonModalElement | null = null;
	private activeLoadingModal: HTMLIonModalElement | null = null;

	constructor(private modalService: ModalService) {}

	/**
	 * Show a success notification modal
	 * @param options Configuration options for the notification modal
	 * @returns Promise that resolves when the modal is shown
	 */
	async showSuccess(options: StatusNotificationOptions): Promise<HTMLIonModalElement> {
		return this.showNotification({
			title: "อ่านข้อมูลสำเร็จ",
			...options,
			statusType: StatusType.SUCCESS,
		});
	}

	/**
	 * Show a warning notification modal
	 * @param options Configuration options for the notification modal
	 * @returns Promise that resolves when the modal is shown
	 */
	async showWarning(options: StatusNotificationOptions): Promise<HTMLIonModalElement> {
		return this.showNotification({
			title: "คำเตือน",
			...options,
			statusType: StatusType.WARNING,
		});
	}

	/**
	 * Show an error notification modal
	 * @param options Configuration options for the notification modal
	 * @returns Promise that resolves when the modal is shown
	 */
	async showError(options: StatusNotificationOptions): Promise<HTMLIonModalElement> {
		return this.showNotification({
			title: "ขออภัย! เกิดข้อผิดพลาด",
			...options,
			statusType: StatusType.ERROR,
		});
	}

	/**
	 * Show an info notification modal
	 * @param options Configuration options for the notification modal
	 * @returns Promise that resolves when the modal is shown
	 */
	async showInfo(options: StatusNotificationOptions): Promise<HTMLIonModalElement> {
		return this.showNotification({
			title: "ข้อมูล",
			...options,
			statusType: StatusType.INFO,
		});
	}

	/**
	 * Show a notification modal with custom status type
	 * @param options Configuration options for the notification modal
	 * @returns Promise that resolves when the modal is shown
	 */
	private async showNotification(
		options: StatusNotificationOptions & { statusType: StatusType },
	): Promise<HTMLIonModalElement> {
		// Close any existing modal
		if (this.activeModal) {
			await this.closeNotification();
		}

		// Create and show the modal
		this.activeModal = await this.modalService.openGeneralModal({
			component: StatusNotificationModalComponent,
			componentProps: {
				title: options.title || "",
				statusType: options.statusType,
				message: options.message,
				description: options.description || "",
				errorCode: options.errorCode || "",
				errorLabel: options.errorLabel || "Code:",
				primaryButtonLabel: options.primaryButtonLabel || "ตกลง",
				secondaryButtonLabel: options.secondaryButtonLabel || "",
				onPrimaryAction: options.onPrimaryAction || null,
				onSecondaryAction: options.onSecondaryAction || null,
			},
			backdropDismiss: options.backdropDismiss ?? false,
			cssClass: ["status-notification-modal", "modal-blur-backdrop"],
		}); // Use the fullscreen option or default to false

		return this.activeModal;
	}

	/**
	 * Close the current notification modal
	 * @param data Optional data to pass back when closing
	 * @returns Promise that resolves when the modal is closed
	 */
	async closeNotification(data?: any): Promise<boolean> {
		if (!this.activeModal) {
			return false;
		}

		await this.activeModal.dismiss(data);
		this.activeModal = null;
		return true;
	}

	/**
	 * Show the circular progress loading modal
	 * @param options Configuration options for the loading modal
	 * @returns Promise that resolves when the modal is shown
	 */
	async showLoading(options: CircularProgressLoadingOptions = {}): Promise<HTMLIonModalElement> {
		// Close any existing loading modal
		if (this.activeLoadingModal) {
			await this.hideLoading();
		}

		// Create and show the modal
		this.activeLoadingModal = await this.modalService.openGeneralModal({
			component: CircularProgressLoadingModalComponent,
			componentProps: {
				title: options.title || "ตรวจสอบข้อมูล",
				statusMessage: options.statusMessage || "กำลังตรวจสอบข้อมูล",
				description:
					options.description || "อยู่ระหว่างการตรวจสอบข้อมูล\nกรุณารอจนกว่าระบบจะทำงานเสร็จสิ้น",
				progress: options.progress ?? 0,
				autoProgress: options.indeterminateProgress ? false : (options.autoProgress ?? true),
				progressDuration: options.progressDuration ?? 10000,
				progressStep: options.progressStep ?? 1,
				showIcon: options.showIcon ?? false,
				isComplete: options.isComplete ?? false,
				showButtons: options.showButtons ?? false,
				cancelButtonLabel: options.cancelButtonLabel || "",
				actionButtonLabel: options.actionButtonLabel || "",
				onCancel: options.onCancel || null,
				onAction: options.onAction || null,
				autoClose: options.autoClose ?? false,
				autoCloseDelay: options.autoCloseDelay ?? 1000,
				indeterminateProgress: options.indeterminateProgress ?? false,
			},
			backdropDismiss: options.backdropDismiss ?? false,
			cssClass: ["circular-progress-loading-modal", "modal-blur-backdrop"],
		});

		return this.activeLoadingModal;
	}

	/**
	 * Update the progress of the current loading modal
	 * @param progress New progress value (0-100)
	 */
	updateProgress(progress: number): void {
		if (this.activeLoadingModal) {
			// ไม่สามารถเรียกใช้เมธอดของ component ได้โดยตรง
			// เพราะ componentProps เป็นเพียง object ไม่ใช่ instance ของ component จริงๆ
			if (this.activeLoadingModal.componentProps) {
				// กำหนดค่า progress โดยตรง
				this.activeLoadingModal.componentProps["progress"] = Math.min(Math.max(progress, 0), 100);

				// ถ้า progress เป็น 100 ให้กำหนด isComplete เป็น true
				if (progress >= 100) {
					this.activeLoadingModal.componentProps["isComplete"] = true;
				}
			}
		}
	}

	/**
	 * Set the loading modal as complete
	 */
	setLoadingComplete(): void {
		if (this.activeLoadingModal) {
			// ไม่สามารถเรียกใช้เมธอดของ component ได้โดยตรง
			// เพราะ componentProps เป็นเพียง object ไม่ใช่ instance ของ component จริงๆ
			if (this.activeLoadingModal.componentProps) {
				// กำหนดค่า progress และ isComplete โดยตรง
				this.activeLoadingModal.componentProps["progress"] = 100;
				this.activeLoadingModal.componentProps["isComplete"] = true;
			}
		}
	}

	/**
	 * Hide the loading modal
	 * @param data Optional data to pass back to the caller
	 * @returns Promise that resolves when the modal is hidden
	 */
	async hideLoading(data?: any): Promise<boolean> {
		if (!this.activeLoadingModal) {
			return false;
		}

		await this.activeLoadingModal.dismiss(data);
		this.activeLoadingModal = null;
		return true;
	}

	/**
	 * Close all modals (both notification and loading)
	 */
	async closeAll(): Promise<void> {
		await this.closeNotification();
		await this.hideLoading();
	}

	/**
	 * Show loading modal with an async operation
	 * This method will show a loading modal, execute the provided async operation,
	 * and automatically handle the loading state and completion
	 *
	 * @param options Configuration options for the loading modal and operation
	 * @returns Promise that resolves with the result of the async operation
	 */
	async showLoadingWithOperation<T>(options: CircularProgressLoadingOptions): Promise<T> {
		if (!options.asyncOperation) {
			throw new Error("asyncOperation is required for showLoadingWithOperation");
		}

		// Show the loading modal
		const loadingOptions: CircularProgressLoadingOptions = {
			...options,
			// If indeterminateProgress is true, disable autoProgress
			autoProgress: options.indeterminateProgress ? false : (options.autoProgress ?? true),
		};

		await this.showLoading(loadingOptions);

		try {
			// Execute the async operation
			const result = await options.asyncOperation();

			// Set loading as complete
			this.setLoadingComplete();

			// If autoClose is enabled, hide the loading modal after the specified delay
			if (options.autoClose) {
				setTimeout(() => {
					this.hideLoading();
				}, options.autoCloseDelay ?? 1000);
			}

			// Call the onOperationComplete callback if provided
			if (options.onOperationComplete) {
				options.onOperationComplete(result);
			}

			return result;
		} catch (error) {
			// Handle error
			console.error("Error in async operation:", error);

			// Call the onOperationError callback if provided
			if (options.onOperationError) {
				options.onOperationError(error);
			}

			// Hide the loading modal
			await this.hideLoading();

			throw error;
		}
	}
}
