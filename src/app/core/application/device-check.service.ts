import { Injectable, signal } from "@angular/core";
import { Capacitor } from "@capacitor/core";
import { BehaviorSubject } from "rxjs";
import {
	DeviceCheckResult,
	UaInfoService,
} from "../../adapters/primary/shared/services/ua-info.service";
import { WebcamService } from "../../adapters/primary/shared/services/webcam.service";
import { DeviceManagerUtils } from "../utils/device-manager-utils";
import { MediaPipeService } from "./media-pipe.service";
import {
	ENGLISH_FAST_MODEL,
	OcrDetectionService,
} from "./ocr-detection.service";

export enum CheckStatus {
	Pending = "pending",
	Checking = "checking",
	Success = "success",
	Error = "failed",
}

export interface DeviceCheckItem {
	id: string;
	name: string;
	description: string;
	status: CheckStatus;
	isRequired: boolean;
	errorMessage?: string;
}

export interface ChecksState {
	isChecking: boolean;
	isComplete: boolean;
	hasErrors: boolean;
}

export interface CameraCheckResult {
	isAvailable: boolean;
	errors: string[];
}

@Injectable({
	providedIn: "root",
})
export class DeviceCheckService {
	private readonly checksSubject = new BehaviorSubject<DeviceCheckItem[]>([
		{
			id: "device-access",
			name: "Device Access",
			description: "ยินยอมการเปิดใช้งานกล้อง",
			status: CheckStatus.Pending,
			isRequired: true,
		},
		{
			id: "camera-device",
			name: "Preparing Camera Device",
			description: "ตรวจสอบฟังก์ชันกล้อง",
			status: CheckStatus.Pending,
			isRequired: true,
		},
		{
			id: "device-capabilities",
			name: "Device Access and Capabilities",
			description: "ตรวจสอบระบบการทำงานของกล้อง",
			status: CheckStatus.Pending,
			isRequired: true,
		},
		{
			id: "ai-model",
			name: "Preparing AI Model",
			description: "ตรวจสอบสเปคอุปกรณ์ที่ใช้ยืนยันตัวตน",
			status: CheckStatus.Pending,
			isRequired: true,
		},
		{
			id: "browser-compatibility",
			name: "Browser Compatibility",
			description: `ตรวจสอบความพร้อมของระบบปฏิบัติการและบราวเซอร์ - ${
				this.uaInfoService.getBrowser().name
			} - ${this.uaInfoService.getBrowser().version}`,
			status: CheckStatus.Pending,
			isRequired: true,
		},
	]);

	public checksState: ChecksState = {
		isChecking: false,
		isComplete: false,
		hasErrors: false,
	};

	public compatibility: DeviceCheckResult | null = null;

	private _checkItems = signal<DeviceCheckItem[]>([]);
	readonly checkItems = this._checkItems.asReadonly();

	private _progress = signal<number>(0);
	readonly progress = this._progress.asReadonly();

	private _isAllChecksCompleted = signal<boolean>(false);
	readonly isAllChecksCompleted = this._isAllChecksCompleted.asReadonly();

	private _isAllChecksPassed = signal<boolean>(false);
	readonly isAllChecksPassed = this._isAllChecksPassed.asReadonly();

	private _isDeviceSupported = signal<boolean>(true);
	readonly isDeviceSupported = this._isDeviceSupported.asReadonly();

	private _deviceCode = signal<string>("abcd Jan 7, 2025, 01:11:11");
	readonly deviceCode = this._deviceCode.asReadonly();

	constructor(
		private webcamService: WebcamService,
		private uaInfoService: UaInfoService,
		private ocrDetectionService: OcrDetectionService,
		private mediaPipeService: MediaPipeService,
	) {
		this.checkSystemCompatibility();

		this.checksSubject.subscribe((checks) => {
			this._checkItems.set(checks);

			const allCompleted = checks.every(
				(check) =>
					check.status === CheckStatus.Success ||
					check.status === CheckStatus.Error,
			);
			this._isAllChecksCompleted.set(allCompleted);

			const allPassed = checks.every(
				(check) => !check.isRequired || check.status === CheckStatus.Success,
			);
			this._isAllChecksPassed.set(allPassed);
		});
	}

	isStateCheckBrowserError(): boolean {
		return (
			this.checksState.isComplete &&
			this.checkItems()[4].status === CheckStatus.Error
		);
	}

	private async checkSystemCompatibility(): Promise<void> {
		const browserCompatibility =
			await this.uaInfoService.checkOSAndHardwareCompatibility();
		const isSupported = browserCompatibility.passed;
		this._isDeviceSupported.set(isSupported);

		const now = new Date();
		const code = `abcd ${now.toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		})}, ${now.toLocaleTimeString("en-US", {
			hour: "2-digit",
			minute: "2-digit",
			second: "2-digit",
			hour12: false,
		})}`;

		this._deviceCode.set(code);
	}

	setDeviceSupported(isSupported: boolean): void {
		this._isDeviceSupported.set(isSupported);
	}

	async startChecks(): Promise<boolean> {
		this.initializeChecks();
		await this.executeCameraAccess();
		await this.executePrepareCamera();
		await this.executeCameraCapability();
		await this.executePrepareAiModel();
		await this.executeBrowserCompatibility();
		this.finalizeChecks();
		return !this.checksState.hasErrors;
	}

	async checkDeviceAndCamera(): Promise<boolean> {
		await this.executeCheck(0, async (): Promise<boolean> => {
			try {
				const permissionState: string =
					await this.webcamService.checkCameraPermission();
				if (permissionState !== "granted") {
					const permissions =
						await this.webcamService.instance.requestPermissions();
					return permissions.camera === "granted";
				}
				return true;
			} catch (error) {
				console.error("Error during device and camera check:", error);
				throw error;
			}
		});

		await this.executeCheck(1, async () => {
			try {
				await new Promise((resolve) => setTimeout(resolve, 1000));

				const isMobileOrTablet =
					this.uaInfoService.isMobile() || this.uaInfoService.isTablet();
				const result = isMobileOrTablet
					? await this.checkMobileDevices()
					: await this.checkDesktopDevices();

				if (!result.isAvailable) {
					console.warn(
						"Camera preparation check failed:",
						result.errors.join(", "),
					);
					throw new Error(result.errors.join(", "));
				}

				return result.isAvailable;
			} catch (error) {
				console.error("Camera preparation check failed:", error);
				throw error;
			}
		});

		return (
			this.checksSubject.value[0].status === CheckStatus.Success &&
			this.checksSubject.value[1].status === CheckStatus.Success
		);
	}

	async checkCameraAccess(): Promise<boolean> {
		await this.executeCheck(0, async (): Promise<boolean> => {
			try {
				const permissionState: string =
					await this.webcamService.checkCameraPermission();
				if (permissionState !== "granted") {
					const permissions =
						await this.webcamService.instance.requestPermissions();
					return permissions.camera === "granted";
				}
				return true;
			} catch (error) {
				console.error("Error during camera access check:", error);
				throw error;
			}
		});

		return this.checksSubject.value[0].status === CheckStatus.Success;
	}

	async prepareAiModel(): Promise<boolean> {
		await this.executeCheck(3, async () => {
			try {
				await Promise.all([
					this.ocrDetectionService.initialize(ENGLISH_FAST_MODEL),
					this.mediaPipeService.initialize(),
				]);
				return true;
			} catch (error) {
				throw error;
			}
		});

		return this.checksSubject.value[3].status === CheckStatus.Success;
	}

	getErrorMessage(index: number): string | undefined {
		if (index >= 0 && index < this.checksSubject.value.length) {
			return this.checksSubject.value[index].errorMessage;
		}
		return undefined;
	}

	initializeChecks(): void {
		this.checksState.isChecking = true;
		this.checksState.isComplete = false;
		this.checksState.hasErrors = false;

		const initialChecks = this.checksSubject.value.map((check) => ({
			...check,
			status: CheckStatus.Pending,
			errorMessage: undefined,
		}));

		this.checksSubject.next(initialChecks);
	}

	private finalizeChecks(): void {
		const currentChecks = this.checksSubject.value;
		this.checksState.isChecking = false;
		this.checksState.isComplete = true;
		this.checksState.hasErrors = currentChecks.some(
			(check) => check.status === CheckStatus.Error,
		);

		this.updateProgress(currentChecks);
	}

	private updateProgress(checks: DeviceCheckItem[]): void {
		const successChecks = checks.filter(
			(check) => check.status === CheckStatus.Success,
		).length;

		const totalValidChecks = checks.filter(
			(check) => check.status !== CheckStatus.Error,
		).length;

		const progress =
			totalValidChecks > 0
				? Math.round((successChecks / totalValidChecks) * 100)
				: 0;

		this._progress.set(progress);
	}

	private async executeCheck(
		index: number,
		checkFn: () => Promise<boolean>,
	): Promise<void> {
		const currentChecks = [...this.checksSubject.value];
		currentChecks[index].status = CheckStatus.Checking;
		this.checksSubject.next(currentChecks);

		try {
			await checkFn();
			currentChecks[index].status = CheckStatus.Success;
		} catch (error) {
			currentChecks[index].status = CheckStatus.Error;
			currentChecks[index].errorMessage =
				error instanceof Error ? error.message : String(error);
		}

		this.checksSubject.next(currentChecks);
		this.updateProgress(currentChecks);

		await new Promise((resolve) => setTimeout(resolve, 500));
	}

	private async executeCameraAccess(): Promise<void> {
		await this.executeCheck(0, async (): Promise<boolean> => {
			try {
				const permissionState: string =
					await this.webcamService.checkCameraPermission();
				if (permissionState !== "granted") {
					const permissions =
						await this.webcamService.instance.requestPermissions();
					return permissions.camera === "granted";
				}
				return true;
			} catch (error) {
				console.error("Error during camera access check:", error);
				throw error;
			}
		});
	}

	private async executePrepareCamera(): Promise<void> {
		await this.executeCheck(1, async () => {
			try {
				const isMobileOrTablet =
					this.uaInfoService.isMobile() || this.uaInfoService.isTablet();
				const result = isMobileOrTablet
					? await this.checkMobileDevices()
					: await this.checkDesktopDevices();

				if (!result.isAvailable) {
					console.warn(
						"Camera preparation check failed:",
						result.errors.join(", "),
					);
					throw new Error(result.errors.join(", "));
				}

				return result.isAvailable;
			} catch (error) {
				console.error("Camera preparation check failed:", error);
				throw error;
			}
		});
	}

	private async checkMobileDevices(): Promise<CameraCheckResult> {
		const dmu = new DeviceManagerUtils();
		const videoDevices = await this.webcamService.instance.getVideoDevices();
		const cameras = {
			back: await dmu.selectCamera(videoDevices, "user"),
			front: await dmu.selectCamera(videoDevices, "environment"),
		};

		const errors: string[] = [];
		if (!cameras.back) errors.push("Back camera not available");
		if (!cameras.front) errors.push("Front camera not available");

		return {
			isAvailable: cameras.back !== null && cameras.front !== null,
			errors,
		};
	}

	private async checkDesktopDevices(): Promise<CameraCheckResult> {
		const videoDevices = await this.webcamService.instance.getVideoDevices();
		return {
			isAvailable: videoDevices.length > 0,
			errors: videoDevices.length === 0 ? ["No camera devices found"] : [],
		};
	}

	private async executeCameraCapability(): Promise<void> {
		await this.executeCheck(2, async () => {
			try {
				await new Promise((resolve) => setTimeout(resolve, 1000));

				const isMobileOrTablet =
					this.uaInfoService.isMobile() ||
					this.uaInfoService.isTablet() ||
					this.uaInfoService.isIPad();

				const result = isMobileOrTablet
					? await this.checkMobileDevicesCapabilities()
					: await this.checkDesktopDevicesCapabilities();

				return result.isAvailable;
			} catch (error) {
				throw error;
			}
		});
	}

	private async checkMobileDevicesCapabilities(): Promise<CameraCheckResult> {
		const dmu = new DeviceManagerUtils();
		const videoDevices = await this.webcamService.instance.getVideoDevices();
		const cameras = {
			back: await dmu.selectCamera(videoDevices, "user"),
			front: await dmu.selectCamera(videoDevices, "environment"),
		};

		if (!cameras.back || !cameras.front) {
			throw new Error("Back or front camera not available");
		}

		// Note: Camera capabilities check removed as it's not available in CameraService
		const isBackCapabilitiesSupported = true;
		const isFrontCapabilitiesSupported = true;

		const errors: string[] = [];
		if (!isBackCapabilitiesSupported) {
			errors.push("Back Camera capabilities not supported");
		}
		if (!isFrontCapabilitiesSupported) {
			errors.push("Front Camera capabilities not supported");
		}

		return {
			isAvailable: isBackCapabilitiesSupported && isFrontCapabilitiesSupported,
			errors,
		};
	}

	private async checkDesktopDevicesCapabilities(): Promise<CameraCheckResult> {
		const videoDevices = await this.webcamService.instance.getVideoDevices();
		if (videoDevices.length === 0) {
			return {
				isAvailable: false,
				errors: ["No camera devices found"],
			};
		}

		// Note: Camera capabilities check removed as it's not available in CameraService
		const isCapabilitiesSupported = true;

		return {
			isAvailable: isCapabilitiesSupported,
			errors: isCapabilitiesSupported
				? []
				: ["Camera capabilities not supported"],
		};
	}

	private async executePrepareAiModel(): Promise<void> {
		await this.executeCheck(3, async () => {
			try {
				await Promise.all([
					this.ocrDetectionService.initialize(ENGLISH_FAST_MODEL),
					this.mediaPipeService.initialize(),
				]);
				return true;
			} catch (error) {
				throw error;
			}
		});
	}

	private async executeBrowserCompatibility(): Promise<void> {
		await this.executeCheck(4, async () => {
			if (!("mediaDevices" in navigator)) {
				throw new Error(
					"System does not meet minimum requirements: mediaDevices not available",
				);
			}

			if (!Capacitor.isNativePlatform()) {
				this.compatibility =
					await this.uaInfoService.checkBrowserCompatibility();
				if (!this.compatibility?.passed) {
					const errors = this.compatibility?.details
						.filter((detail) => !detail.passed && detail.message)
						.map((detail) => detail.message)
						.join(", ");

					throw new Error(
						`Browser does not meet minimum requirements: ${errors}`,
					);
				}
			}

			return true;
		});
	}

	/**
	 * Open target browser for compatibility
	 */
	openTargetBrowser(): void {
		const redirectUrl = this.compatibility?.details[1]?.data?.targetBrowserUrl;
		if (!redirectUrl) {
			console.warn("Safari redirect URL not found");
			return;
		}

		// Create a temporary link element and click it
		const link = document.createElement("a");
		link.href = redirectUrl;
		link.target = "_blank";
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}
}
