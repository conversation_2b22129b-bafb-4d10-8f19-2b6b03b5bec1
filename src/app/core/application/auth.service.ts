import { inject, Injectable, signal } from "@angular/core";
import AuthClient from "../../core/lib/auth/client";
import { StarmoneyService } from "./starmoney.service";

@Injectable({
	providedIn: "root",
})
export class AuthService {
	// Inject StarmoneyService
	private starmoneyService = inject(StarmoneyService);

	// Keep track of txnId
	private auth: AuthClient | null = null;
	private txnIdSignal = signal<string | null>(null);
	public readonly txnId = this.txnIdSignal.asReadonly();

	public setTxnId(txnId: string | null) {
		this.txnIdSignal.set(txnId);
	}

	public getTxnId(): string | null {
		return this.txnIdSignal();
	}

	constructor() {
		this.auth = new AuthClient(this.starmoneyService);
	}

	async signInAnonymous(txn_id: string) {
		return this.auth?.signInAnonymous(txn_id);
	}

	public async getSession() {
		return this.auth?.getSession();
	}
}
