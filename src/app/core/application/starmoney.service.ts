import { Inject, Injectable } from "@angular/core";
import { GetAddressInfoRequestDto } from "@domain/dto/address/get-address-info.dto";
import { GetDistrictRequestDto } from "@domain/dto/address/get-district.dto";
import { GetHousingTypeRequestDto } from "@domain/dto/address/get-housing-type.dto";
import { GetPostcodeRequestDto } from "@domain/dto/address/get-postcode.dto";
import { GetProvinceRequestDto } from "@domain/dto/address/get-province.dto";
import { GetSubDistrictRequestDto } from "@domain/dto/address/get-sub-district.dto";
import { UpdateAddressInfoRequestDto } from "@domain/dto/address/update-address-info.dto";
import { UpdateHousingTypeRequest } from "@domain/dto/address/update-housing-type.dto";
import { GetContactRequestDto } from "@domain/dto/contact/get-contact.dto";
import { SendOtpRequestDto } from "@domain/dto/contact/send-otp.dto";
import { UpdateContactRequestDto } from "@domain/dto/contact/update-contact.dto";
import { VerifyOtpRequestDto } from "@domain/dto/contact/verify-otp.dto";
import { GetUploadDocsRequestDto } from "@domain/dto/document/get-upload-docs.dto";
import { UploadDocRequestDto } from "@domain/dto/document/upload-doc.dto";
import { GetLoanTermRequestDto } from "@domain/dto/loan/get-loan-term.dto";
import { GetLoanTypeRequestDto } from "@domain/dto/loan/get-loan-type.dto";
import { SetLoanTypeRequestDto } from "@domain/dto/loan/set-loan-type.dto";
import { StartKycTransactionRequestDto } from "@domain/dto/loan/start-transaction.dto";
import { ConfirmProductRequestDto } from "@domain/dto/products/confirm-product.dto";
import { GetProductListRequestDto } from "@domain/dto/products/get-product-list.dto";
import { GetProductRequestDto } from "@domain/dto/products/get-product.dto";
import { SetProductRequestDto } from "@domain/dto/products/set-product.dto";
import { AcceptTermsRequestDto } from "@domain/dto/terms/accept-term.dto";
import { GetTermRequestDto } from "@domain/dto/terms/get-term.dto";
import { TransactionIdDto } from "@domain/dto/transaction.dto";
import { GetOccupationGroupRequestDto } from "@domain/dto/working/get-occupation-group.dto";
import { GetOccupationRequestDto } from "@domain/dto/working/get-occupation.dto";
import { GetWorkingInfoRequestDto } from "@domain/dto/working/get-working-info.dto";
import { UpdateWorkingInfoRequestDto } from "@domain/dto/working/update-working-info.dto";
import {
	AddressInfo,
	District,
	HousingType,
	PostalCode,
	Province,
	SubDistrict,
} from "@domain/models/address.model";
import { ApiResponse } from "@domain/models/api-response.model";
import { Contact, SendOtp } from "@domain/models/contact.model";
import { LoanCategory, LoanTerm, LoanType } from "@domain/models/loan.model";
import { Product, ProductList } from "@domain/models/product.model";
import { StartKycTransaction } from "@domain/models/start-kyc-transaction.model";
import { Term } from "@domain/models/term.model";
import {
	Occupation,
	OccupationGroup,
	WorkingInfo,
} from "@domain/models/working.model";
import { AddressUseCasePort } from "@ports/in/address-usecase.port";
import { ContactUseCasePort } from "@ports/in/contact.port";
import { DocumentUseCasePort } from "@ports/in/document.port";
import {
	ADDRESS_USE_CASE,
	CONTACT_USE_CASE,
	DOCUMENT_USE_CASE,
	LOAN_USE_CASE,
	PRODUCT_USE_CASE,
	TERM_USE_CASE,
	USER_USE_CASE,
	WORKING_USE_CASE,
} from "@ports/in/injection-token";
import { LoanUseCasePort } from "@ports/in/loan-usecase.port";
import { ProductUseCasePort } from "@ports/in/product.port";
import { TermUseCasePort } from "@ports/in/term.port";
import { UserUseCasePort } from "@ports/in/user.port";
import { WorkingUseCasePort } from "@ports/in/working.port";

@Injectable({
	providedIn: "root",
})
export class StarmoneyService {
	constructor(
		@Inject(LOAN_USE_CASE) private loanUseCase: LoanUseCasePort,
		@Inject(USER_USE_CASE) private userUseCase: UserUseCasePort,
		@Inject(ADDRESS_USE_CASE) private addressUseCase: AddressUseCasePort,
		@Inject(WORKING_USE_CASE) private workingUseCase: WorkingUseCasePort,
		@Inject(PRODUCT_USE_CASE) private productUseCase: ProductUseCasePort,
		@Inject(DOCUMENT_USE_CASE) private documentUseCase: DocumentUseCasePort,
		@Inject(TERM_USE_CASE) private termUseCase: TermUseCasePort,
		@Inject(CONTACT_USE_CASE) private contactUseCase: ContactUseCasePort,
	) {}

	/**
	 * ======================================================================================================
	 * Auth
	 * ======================================================================================================
	 */

	async signInAnonymous(request: TransactionIdDto): Promise<ApiResponse<any>> {
		return this.userUseCase.signInAnonymous(request);
	}

	async refreshToken(request: TransactionIdDto): Promise<ApiResponse<any>> {
		return this.userUseCase.refreshToken(request);
	}

	/**
	 * Start a KYC transaction
	 * @param request The transaction ID
	 * @returns Promise of an API response containing the transaction ID
	 */
	async startKycTransaction(
		request: StartKycTransactionRequestDto,
	): Promise<ApiResponse<StartKycTransaction>> {
		const response = await this.loanUseCase.startKycTransaction(request);
		const model = response.isSuccess()
			? StartKycTransaction.fromJson(response.data)
			: ({} as StartKycTransaction);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * ======================================================================================================
	 * Loan
	 * ======================================================================================================
	 */

	/**
	 * Get all available loan categories
	 * @returns Promise of an API response containing an array of loan categories
	 */
	async getLoanCategories(): Promise<ApiResponse<LoanCategory[]>> {
		const response = await this.loanUseCase.getLoanCategories();
		const model = response.isSuccess()
			? response.data.map((item) => LoanCategory.fromJson(item))
			: ([] as LoanCategory[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * Get the loan type for a transaction
	 * @param transactionId The transaction ID
	 * @returns Promise of an API response containing the loan type
	 */
	async getLoanType(
		request: GetLoanTypeRequestDto,
	): Promise<ApiResponse<LoanType>> {
		const response = await this.loanUseCase.getLoanType(request);
		const model = response.isSuccess()
			? LoanType.fromJson(response.data)
			: ({} as LoanType);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * Set the loan type for a transaction
	 * @param transactionId The transaction ID
	 * @param loanTypeId The loan type ID to set
	 * @returns Promise of an API response with boolean data
	 */
	async setLoanType(
		request: SetLoanTypeRequestDto,
	): Promise<ApiResponse<boolean>> {
		return await this.loanUseCase.setLoanType(request);
	}

	/**
	 * Get all available loan terms for a transaction
	 * @param transactionId The transaction ID
	 * @returns Promise of an API response containing an array of loan terms
	 */
	async getLoanTerms(
		request: GetLoanTermRequestDto,
	): Promise<ApiResponse<LoanTerm>> {
		const response = await this.loanUseCase.getLoanTerms(request);
		const model = response.isSuccess()
			? LoanTerm.fromJson(response.data)
			: ({} as LoanTerm);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * ======================================================================================================
	 * Address
	 * ======================================================================================================
	 */

	async getAddressInfo(
		request: GetAddressInfoRequestDto,
	): Promise<ApiResponse<AddressInfo[]>> {
		const response = await this.addressUseCase.getAddressInfo(request);
		const model = response.isSuccess()
			? response.data.map((item) => AddressInfo.fromJson(item))
			: ([] as AddressInfo[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async getProvinces(
		request: GetProvinceRequestDto,
	): Promise<ApiResponse<Province[]>> {
		const response = await this.addressUseCase.getProvinces(request);
		const model = response.isSuccess()
			? response.data.map((item) => Province.fromJson(item))
			: ([] as Province[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async getDistricts(
		request: GetDistrictRequestDto,
	): Promise<ApiResponse<District[]>> {
		const response = await this.addressUseCase.getDistricts(request);
		const model = response.isSuccess()
			? response.data.map((item) => District.fromJson(item))
			: ([] as District[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async getSubDistricts(
		request: GetSubDistrictRequestDto,
	): Promise<ApiResponse<SubDistrict[]>> {
		const response = await this.addressUseCase.getSubDistricts(request);
		const model = response.isSuccess()
			? response.data.map((item) => SubDistrict.fromJson(item))
			: ([] as SubDistrict[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async getPostalCodes(
		request: GetPostcodeRequestDto,
	): Promise<ApiResponse<PostalCode[]>> {
		const response = await this.addressUseCase.getPostalCodes(request);
		const model = response.isSuccess()
			? response.data.map((item) => PostalCode.fromJson(item))
			: ([] as PostalCode[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async getHousingTypes(
		request: GetHousingTypeRequestDto,
	): Promise<ApiResponse<HousingType[]>> {
		const response = await this.addressUseCase.getHousingTypes(request);
		const model = response.isSuccess()
			? response.data.map((item) => HousingType.fromJson(item))
			: ([] as HousingType[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async updateAddressInfo(
		request: UpdateAddressInfoRequestDto,
	): Promise<ApiResponse<AddressInfo>> {
		const response = await this.addressUseCase.updateAddressInfo(request);
		const model = response.isSuccess()
			? AddressInfo.fromJson(response.data)
			: ({} as AddressInfo);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async updateHousingType(
		request: UpdateHousingTypeRequest,
	): Promise<ApiResponse<HousingType>> {
		const response = await this.addressUseCase.updateHousingType(request);
		const model = response.isSuccess()
			? HousingType.fromJson(response.data)
			: ({} as HousingType);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * ======================================================================================================
	 * Working
	 * ======================================================================================================
	 */
	async getWorkingInfo(
		request: GetWorkingInfoRequestDto,
	): Promise<ApiResponse<WorkingInfo>> {
		const raw = await this.workingUseCase.getWorkingInfo(request);
		const response = ApiResponse.fromResponse(raw);

		const model = response.isSuccess()
			? WorkingInfo.fromJson(response.data)
			: ({} as WorkingInfo);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * Update working information
	 * @param request The request containing transaction ID and working information
	 * @returns Promise of ApiResponse containing updated working information
	 */
	async updateWorkingInfo(
		request: UpdateWorkingInfoRequestDto,
	): Promise<ApiResponse<WorkingInfo>> {
		const raw = await this.workingUseCase.updateWorkingInfo(request);
		const response = ApiResponse.fromResponse(raw);

		const model = response.isSuccess()
			? WorkingInfo.fromJson(response.data)
			: ({} as WorkingInfo);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async getOccupationGroups(
		request: GetOccupationGroupRequestDto,
	): Promise<ApiResponse<OccupationGroup[]>> {
		const raw = await this.workingUseCase.getOccupationGroups(request);
		const response = ApiResponse.fromResponse(raw);

		const model = response.isSuccess()
			? response.data.map((item) => OccupationGroup.fromJson(item))
			: ([] as OccupationGroup[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async getOccupations(
		request: GetOccupationRequestDto,
	): Promise<ApiResponse<Occupation[]>> {
		const raw = await this.workingUseCase.getOccupations(request);
		const response = ApiResponse.fromResponse(raw);

		const model = response.isSuccess()
			? response.data.map((item) => Occupation.fromJson(item))
			: ([] as Occupation[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * ======================================================================================================
	 * Product
	 * ======================================================================================================
	 */
	async getProductList(
		request: GetProductListRequestDto,
	): Promise<ApiResponse<ProductList[]>> {
		const raw = await this.productUseCase.getProductList(request);
		const response = ApiResponse.fromResponse(raw);

		const model = response.isSuccess()
			? response.data.map((item) => ProductList.fromJson(item))
			: ([] as ProductList[]);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async setProduct(
		request: SetProductRequestDto,
	): Promise<ApiResponse<boolean>> {
		const raw = await this.productUseCase.setProduct(request);
		const response = ApiResponse.fromResponse(raw);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: response.data,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async getProduct(
		request: GetProductRequestDto,
	): Promise<ApiResponse<Product | null>> {
		const raw = await this.productUseCase.getProduct(request);
		const response = ApiResponse.fromResponse(raw);
		const model =
			response.isSuccess() && response.hasData()
				? Product.fromJson(response.data)
				: null;

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async confirmProduct(
		request: ConfirmProductRequestDto,
	): Promise<ApiResponse<any>> {
		const raw = await this.productUseCase.confirmProduct(request);
		const response = ApiResponse.fromResponse(raw);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: response.data,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * ======================================================================================================
	 * Document
	 * ======================================================================================================
	 */

	async getUploadDocuments(
		request: GetUploadDocsRequestDto,
	): Promise<ApiResponse<any>> {
		const raw = await this.documentUseCase.getUploadDocuments(request);
		const response = ApiResponse.fromResponse(raw);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: response.data,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async uploadDocument(
		request: UploadDocRequestDto,
	): Promise<ApiResponse<any>> {
		const raw = await this.documentUseCase.uploadDocument(request);
		const response = ApiResponse.fromResponse(raw);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: response.data,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * ======================================================================================================
	 * Terms
	 * ======================================================================================================
	 */

	async getTermAndConsent(
		request: GetTermRequestDto,
	): Promise<ApiResponse<Term | null>> {
		const raw = await this.termUseCase.getTerm(request);
		const response = ApiResponse.fromResponse(raw);
		const model = response.isSuccess() ? Term.fromJson(response.data) : null;

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async acceptTerm(request: AcceptTermsRequestDto): Promise<ApiResponse<any>> {
		const raw = await this.termUseCase.acceptTerm(request);
		const response = ApiResponse.fromResponse(raw);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: response.data,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	/**
	 * ======================================================================================================
	 * Contact
	 * ======================================================================================================
	 */

	async updateContact(
		request: UpdateContactRequestDto,
	): Promise<ApiResponse<boolean>> {
		const raw = await this.contactUseCase.updateContact(request);
		const response = ApiResponse.fromResponse(raw);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: response.data,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async getContact(
		request: GetContactRequestDto,
	): Promise<ApiResponse<Contact>> {
		const raw = await this.contactUseCase.getContact(request);
		const response = ApiResponse.fromResponse(raw);
		const model = response.isSuccess()
			? Contact.fromJson(response.data)
			: ({} as Contact);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async sendOtp(request: SendOtpRequestDto): Promise<ApiResponse<SendOtp>> {
		const raw = await this.contactUseCase.sendOtp(request);
		const response = ApiResponse.fromResponse(raw);
		const model = response.isSuccess()
			? SendOtp.fromJson(response.data)
			: ({} as SendOtp);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: model,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async verifyOtp(request: VerifyOtpRequestDto): Promise<ApiResponse<boolean>> {
		const raw = await this.contactUseCase.verifyOtp(request);
		const response = ApiResponse.fromResponse(raw);

		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: response.data,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}
}
