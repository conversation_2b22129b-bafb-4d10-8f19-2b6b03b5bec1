import { provideHttpClient } from "@angular/common/http";
import { ApplicationConfig } from "@angular/core";
import { provideRouter } from "@angular/router";
import { provideIonicAngular } from "@ionic/angular/standalone";

import { routes } from "./app.routes";
import { ComponentMappingService } from "./core/application/workflow/component-mapping.service";
import { ModalService } from "./core/application/workflow/modal.service";
import { WorkflowModalService } from "./core/application/workflow/workflow-modal.service";
import { WorkflowRegistryService } from "./core/application/workflow/workflow-registry.service";

export const appConfig: ApplicationConfig = {
	providers: [
		provideRouter(routes),
		provideIonicAngular(),
		provideHttpClient(),
		WorkflowModalService,
		ModalService,
		WorkflowRegistryService,
		ComponentMappingService,
	],
};
