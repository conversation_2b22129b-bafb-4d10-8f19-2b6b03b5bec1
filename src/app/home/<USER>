#container {
	text-align: center;
	padding: 20px;
	max-width: 1200px;
	margin: 0 auto;
}

#container h1 {
	font-size: 28px;
	margin-bottom: 10px;
	color: var(--ion-color-primary);
}

#container p {
	font-size: 16px;
	line-height: 1.5;
	color: var(--ion-color-medium);
	margin-bottom: 20px;
}

.demo-cards {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	gap: 20px;
	margin-top: 30px;
}

ion-card {
	margin: 0;
	height: 100%;
	display: flex;
	flex-direction: column;
}

ion-card-content {
	flex-grow: 1;
	display: flex;
	flex-direction: column;
}

ion-card-content p {
	margin-bottom: 20px;
	flex-grow: 1;
}

.mb-4 {
	margin-bottom: 1rem;
}

@media (max-width: 768px) {
	#container {
		padding: 10px;
	}

	#container h1 {
		font-size: 24px;
	}
}
