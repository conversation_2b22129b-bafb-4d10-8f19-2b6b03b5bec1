<ion-header [translucent]="true">
	<ion-toolbar color="primary">
		<ion-title>Hexagonal Architecture Demo</ion-title>
	</ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
	<ion-header collapse="condense">
		<ion-toolbar>
			<ion-title size="large">Hexagonal Architecture Demo</ion-title>
		</ion-toolbar>
	</ion-header>

	<div id="container">
		<h1 class="text-3xl font-bold">Welcome to Hexagonal Architecture Demo</h1>
		<p class="mb-4">
			This is a demo of a frontend application using hexagonal architecture with
			workflow management
		</p>

		<div class="demo-cards">
			<ion-card>
				<ion-card-header>
					<ion-card-title>User Management</ion-card-title>
				</ion-card-header>
				<ion-card-content>
					<p>Basic CRUD operations using hexagonal architecture</p>
					<ion-button expand="block" routerLink="/users">View Users</ion-button>
				</ion-card-content>
			</ion-card>

			<ion-card>
				<ion-card-header>
					<ion-card-title>Workflow Demo</ion-card-title>
				</ion-card-header>
				<ion-card-content>
					<p>Modal-based workflow with signals for loan application</p>
					<ion-button expand="block" routerLink="/loan" color="secondary">
						Start Workflow
					</ion-button>
				</ion-card-content>
			</ion-card>
		</div>
	</div>
</ion-content>
