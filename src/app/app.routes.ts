import { Routes } from "@angular/router";

export const routes: Routes = [
	{
		path: "device-check",
		loadComponent: () =>
			import("./adapters/primary/loan/device-check/device-check.component").then(
				(m) => m.<PERSON>ce<PERSON>heckComponent,
			),
	},

	{
		path: "login",
		loadComponent: () =>
			import("./adapters/primary/auth/login/login.component").then((m) => m.LoginComponent),
	},
	{
		path: "home",
		loadComponent: () => import("./home/<USER>").then((m) => m.HomePage),
	},
	{
		path: "loan",
		loadComponent: () =>
			import("./adapters/primary/dashboard/dashboard.component").then((m) => m.DashboardComponent),
	},
	{
		path: "dashboard",
		loadComponent: () =>
			import("./adapters/primary/dashboard/dashboard.component").then((m) => m.DashboardComponent),
	},
	{
		path: "",
		redirectTo: "device-check",
		pathMatch: "full",
	},
];
