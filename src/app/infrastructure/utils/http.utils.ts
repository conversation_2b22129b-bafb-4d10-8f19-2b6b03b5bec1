import { HttpErrorResponse } from "@angular/common/http";
import { Observable, throwError } from "rxjs";

/**
 * Handle HTTP errors
 * @param error The HTTP error response
 * @returns An Observable that errors with a user-friendly message
 */
export function handleHttpError(error: HttpErrorResponse): Observable<never> {
	let errorMessage = "An unknown error occurred";

	if (error.error instanceof ErrorEvent) {
		// Client-side error
		errorMessage = `Error: ${error.error.message}`;
	} else {
		// Server-side error
		errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
	}

	console.error(errorMessage);
	return throwError(() => new Error(errorMessage));
}
