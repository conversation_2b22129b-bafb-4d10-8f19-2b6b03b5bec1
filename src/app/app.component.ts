import { Component } from "@angular/core";
import { <PERSON><PERSON><PERSON>, IonRouterOutlet } from "@ionic/angular/standalone";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { LoadingScreenComponent } from "src/app/adapters/primary/shared/components/loading-screen/loading-screen.component";
import { ToastScreenComponent } from "./adapters/primary/shared/components/toast-screen/toast-screen.component";

@Component({
	selector: "app-root",
	templateUrl: "app.component.html",
	imports: [
		IonApp,
		IonRouterOutlet,
		LoadingScreenComponent,
		ToastScreenComponent,
		ConfirmDialogModule,
	],
})
export class AppComponent {
	constructor() {}
}
