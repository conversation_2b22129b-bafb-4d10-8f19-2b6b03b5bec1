import { CommonModule } from "@angular/common";
import { Component, Input } from "@angular/core";
import { IonButton } from "@ionic/angular/standalone";

@Component({
	selector: "app-loading-modal",
	standalone: true,
	imports: [CommonModule, IonButton],
	template: `
		<div class="loading-modal-container">
			<div class="loading-modal-content">
				<h1 class="loading-title">{{ title }}</h1>

				<div class="loading-image-container">
					<img
						src="assets/images/id-card-reader.png"
						alt="ID Card Reader"
						class="id-card-reader-image" />
					<img
						src="assets/images/id-card.png"
						alt="ID Card"
						class="id-card-image" />
					<div class="arrow-up"></div>
				</div>

				<h2 class="loading-status">{{ status }}</h2>

				<p class="loading-message">{{ message }}</p>

				<div class="loading-actions">
					<ion-button fill="clear" class="back-button" (click)="onBackClick()">
						ย้อนกลับ
					</ion-button>

					<ion-button
						class="check-status-button"
						(click)="onCheckStatusClick()">
						ตรวจสอบสถานะ
					</ion-button>
				</div>
			</div>
		</div>
	`,
	styles: [
		`
			.loading-modal-container {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 100%;
				width: 100%;
				padding: 20px;
				text-align: center;
				background-color: white;
			}

			.loading-modal-content {
				max-width: 500px;
				width: 100%;
			}

			.loading-title {
				font-size: 24px;
				font-weight: bold;
				margin-bottom: 30px;
			}

			.loading-image-container {
				position: relative;
				height: 200px;
				margin-bottom: 30px;
			}

			.id-card-reader-image {
				width: 120px;
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
			}

			.id-card-image {
				width: 80px;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%) translateY(-50%);
			}

			.arrow-up {
				position: absolute;
				bottom: 60px;
				left: 50%;
				transform: translateX(-50%);
				width: 0;
				height: 0;
				border-left: 15px solid transparent;
				border-right: 15px solid transparent;
				border-bottom: 20px solid #1976d2;
			}

			.loading-status {
				font-size: 20px;
				font-weight: bold;
				margin-bottom: 15px;
			}

			.loading-message {
				font-size: 16px;
				color: #666;
				margin-bottom: 30px;
			}

			.loading-actions {
				display: flex;
				justify-content: space-between;
				width: 100%;
			}

			.back-button {
				--color: #1976d2;
			}

			.check-status-button {
				--background: #1976d2;
				--color: white;
			}
		`,
	],
})
export class LoadingModalComponent {
	@Input() title: string = "ตรวจสอบข้อมูลบัตรประชาชน";
	@Input() status: string = "อยู่ระหว่างขั้นตอนการตรวจสอบข้อมูล";
	@Input() message: string =
		'หากท่านอ่านบัตรประชาชนลูกค้าแล้ว\nกรุณากดปุ่ม "ตรวจสอบสถานะ" เพื่อดำเนินการต่อ\n\nหากระบบไม่เปิดแอปพลิเคชัน กรุณากดปุ่ม "ย้อนกลับ"\nแล้วกดปุ่ม "อ่านบัตรประชาชน" อีกครั้ง';

	onBackClick() {
		// Emit event to parent component
		const backEvent = new CustomEvent("back");
		window.dispatchEvent(backEvent);
	}

	onCheckStatusClick() {
		// Emit event to parent component
		const checkEvent = new CustomEvent("check");
		window.dispatchEvent(checkEvent);
	}
}
