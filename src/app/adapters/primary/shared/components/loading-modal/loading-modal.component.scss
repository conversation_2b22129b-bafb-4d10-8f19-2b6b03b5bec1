.loading-modal-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	width: 100%;
	padding: 20px;
	text-align: center;
	background-color: white;
}

.loading-modal-content {
	max-width: 500px;
	width: 100%;
}

.loading-title {
	font-size: 24px;
	font-weight: bold;
	margin-bottom: 30px;
}

.loading-image-container {
	position: relative;
	height: 200px;
	margin-bottom: 30px;
}

.id-card-reader-image {
	width: 120px;
	position: absolute;
	top: 0;
	left: 50%;
	transform: translateX(-50%);
}

.id-card-image {
	width: 80px;
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%) translateY(-50%);
}

.arrow-up {
	position: absolute;
	bottom: 60px;
	left: 50%;
	transform: translateX(-50%);
	width: 0;
	height: 0;
	border-left: 15px solid transparent;
	border-right: 15px solid transparent;
	border-bottom: 20px solid #1976d2;
}

.loading-status {
	font-size: 20px;
	font-weight: bold;
	margin-bottom: 15px;
}

.loading-message {
	font-size: 16px;
	color: #666;
	margin-bottom: 30px;
	white-space: pre-line;
}

.loading-actions {
	display: flex;
	justify-content: space-between;
	width: 100%;
}

.back-button {
	--color: #1976d2;
}

.check-status-button {
	--background: #1976d2;
	--color: white;
}
