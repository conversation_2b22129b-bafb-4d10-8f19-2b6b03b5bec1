import { CommonModule } from "@angular/common";
import { Component, ViewEncapsulation } from "@angular/core";
import { ToastModule } from "primeng/toast";

@Component({
	selector: "app-toast-screen",
	standalone: true,
	imports: [CommonModule, ToastModule],
	encapsulation: ViewEncapsulation.None,
	styleUrls: ["./toast-screen.component.scss"],
	template: `
		<p-toast position="top-right" [baseZIndex]="99999">
			<ng-template let-message pTemplate="message">
				<!-- API Response Template -->
				<ng-container
					*ngIf="isApiResponse(message.detail); else standardTemplate">
					<div class="toast-content">
						<!-- Header with Status Badge -->
						<div class="toast-header">
							<div
								[ngClass]="getIconContainerClass(message.severity)"
								class="toast-icon">
								<i [ngClass]="getIconClass(message.severity)"></i>
							</div>
							<div class="toast-title">
								<div class="flex items-center gap-1">
									<h4>{{ message.summary }}</h4>
									<span
										*ngIf="getDetails(message.detail).statusCode"
										[ngClass]="
											getStatusBadgeClass(getDetails(message.detail).statusCode)
										"
										class="status-badge">
										{{ getDetails(message.detail).statusCode }}
									</span>
									<span
										*ngIf="getDetails(message.detail).method"
										class="method-badge">
										{{ getDetails(message.detail).method }}
									</span>
								</div>
								<span class="toast-timestamp">
									{{ getDetails(message.detail).timestamp }}
								</span>
							</div>
						</div>

						<!-- Message -->
						<div class="toast-message-content">
							{{ getDetails(message.detail).message || "N/A" }}
						</div>

						<!-- URL (แสดงเฉพาะเมื่อเป็น error) -->
						<div *ngIf="getDetails(message.detail).url" class="toast-url">
							<span class="url-label">URL:</span>
							<span class="url-value">
								{{ getDetails(message.detail).url }}
							</span>
						</div>
					</div>
				</ng-container>

				<!-- Standard Template -->
				<ng-template #standardTemplate>
					<div class="toast-content standard">
						<div
							[ngClass]="getIconContainerClass(message.severity)"
							class="toast-icon">
							<i [ngClass]="getIconClass(message.severity)"></i>
						</div>
						<div class="toast-message">
							<h4>{{ message.summary }}</h4>
							<p>{{ message.detail }}</p>
						</div>
					</div>
				</ng-template>
			</ng-template>
		</p-toast>
	`,
})
export class ToastScreenComponent {
	isApiResponse(detail: string): boolean {
		if (!detail) return false;

		try {
			if (!detail.startsWith("{") && !detail.startsWith("[")) {
				return false;
			}

			const parsed = JSON.parse(detail);
			return (
				typeof parsed === "object" &&
				("statusCode" in parsed ||
					"method" in parsed ||
					"url" in parsed ||
					"timestamp" in parsed)
			);
		} catch (e) {
			return false;
		}
	}

	getDetails(detail: string) {
		try {
			// ตรวจสอบเบื้องต้นว่าเป็น JSON หรือไม่
			if (!detail.startsWith("{") && !detail.startsWith("[")) {
				return {
					timestamp: new Date().toLocaleTimeString(),
					statusCode: 0,
					message: detail,
					url: "",
				};
			}

			return JSON.parse(detail);
		} catch (e) {
			return {
				timestamp: new Date().toLocaleTimeString(),
				statusCode: 0,
				message: detail,
				url: "",
			};
		}
	}

	getIconClass(severity: string) {
		switch (severity) {
			case "success":
				return "pi pi-check";
			case "error":
				return "pi pi-times";
			case "info":
				return "pi pi-info-circle";
			case "warn":
				return "pi pi-exclamation-triangle";
			default:
				return "pi pi-info-circle";
		}
	}

	getIconContainerClass(severity: string) {
		switch (severity) {
			case "success":
				return "bg-green-200 text-green-700";
			case "error":
				return "bg-red-200 text-red-700";
			case "info":
				return "bg-blue-200 text-blue-700";
			case "warn":
				return "bg-yellow-200 text-yellow-700";
			default:
				return "bg-blue-200 text-blue-700";
		}
	}

	getStatusBadgeClass(status: number) {
		if (status >= 200 && status < 300) return "bg-emerald-600";
		if (status >= 400 && status < 500) return "bg-amber-600";
		if (status >= 500) return "bg-rose-600";
		return "bg-slate-600";
	}
}
