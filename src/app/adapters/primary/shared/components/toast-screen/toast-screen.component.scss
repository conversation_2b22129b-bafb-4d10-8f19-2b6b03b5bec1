/* Toast Container */
:host ::ng-deep .p-toast {
	z-index: 99999 !important;
}

:host ::ng-deep .p-toast .p-toast-message {
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	margin-bottom: 0.75rem;
	max-width: 320px;
}

:host ::ng-deep .p-toast .p-toast-message .p-toast-message-content {
	padding: 0.75rem;
	border-width: 0;
}

/* Toast Colors */
:host ::ng-deep .p-toast .p-toast-message-success {
	background-color: #ecfdf5;
	border-left: 4px solid #10b981;
}

:host ::ng-deep .p-toast .p-toast-message-info {
	background-color: #eff6ff;
	border-left: 4px solid #3b82f6;
}

:host ::ng-deep .p-toast .p-toast-message-warn {
	background-color: #fffbeb;
	border-left: 4px solid #f59e0b;
}

:host ::ng-deep .p-toast .p-toast-message-error {
	background-color: #fef2f2;
	border-left: 4px solid #ef4444;
}

/* Toast Content */
.toast-content {
	@apply flex flex-1 flex-col gap-2;
}

.toast-content.standard {
	@apply flex flex-row items-center gap-3;
}

/* Toast Header */
.toast-header {
	@apply flex items-center gap-2;
}

/* Toast Icon */
.toast-icon {
	@apply flex flex-shrink-0 items-center justify-center rounded-full p-1.5;
}

.toast-icon i {
	@apply text-sm;
}

/* Toast Title */
.toast-title {
	@apply flex-grow;
}

.toast-title h4 {
	@apply m-0 text-sm font-semibold text-gray-800;
}

.toast-timestamp {
	@apply text-xs text-gray-500;
}

/* Status and Method Badges */
.status-badge {
	@apply rounded-full px-1.5 py-0.5 text-[10px] font-medium text-white;
}

.method-badge {
	@apply rounded-full bg-gray-700 px-1.5 py-0.5 text-[10px] font-medium text-white;
}

/* Toast Message Content */
.toast-message-content {
	@apply mt-1.5 text-sm text-gray-700;
}

/* Toast Message for Standard Template */
.toast-message {
	@apply flex-grow;
}

.toast-message h4 {
	@apply m-0 text-sm font-semibold text-gray-800;
}

.toast-message p {
	@apply m-0 text-sm text-gray-700;
}

/* Toast Details */
.toast-details {
	@apply mt-1 border-t border-gray-200 pt-1 text-xs;
}

.toast-detail-row {
	@apply mb-0.5 flex items-center justify-between last:mb-0;
}

.toast-label {
	@apply font-medium text-gray-600;
}

.toast-value {
	@apply max-w-[180px] truncate text-gray-700;
}

/* URL Display for Error Messages */
.toast-url {
	@apply mt-1.5 border-t border-red-100 pt-1.5 text-xs;
	@apply flex flex-col;
}

.url-label {
	@apply mb-0.5 font-medium text-gray-700;
}

.url-value {
	@apply break-all text-[10px] text-gray-700;
	word-break: break-all;
}
