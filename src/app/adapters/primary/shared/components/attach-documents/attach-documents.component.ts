import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject, OnInit } from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { ModalController } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { SelectModule } from "primeng/select";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { FooterComponent } from "../footer/footer.component";
import { WorkflowStepBaseComponent } from "../../../workflow/workflow-step-base.component";

@Component({
	selector: "app-attach-documents",
	imports: [
		CommonModule,
		ReactiveFormsModule,
		FooterComponent,
		ButtonModule,
		SelectModule,
	],
	styleUrl: "./attach-documents.component.css",
	template: `
		<div class="app-layout-dialog">
			<!-- Content -->
			<div class="app-content">
				<div class="content-wrapper !max-w-full">
					<p class="page-title mb-6 !text-lg !font-medium">แนบเอกสาร</p>

					<form
						[formGroup]="form"
						class="custom-form flex h-full w-full flex-col gap-2 text-sm">
						<div class="form-field">
							<label class="form-label">
								ประเภทเอกสาร
								<span class="text-red-500">*</span>
							</label>
							<div class="card flex justify-center">
								<p-select
									placeholder="เลือกประเภทเอกสาร"
									formControlName="documentType"
									[options]="groupedDocumentType"
									[group]="true"
									appendTo="body"
									[ngClass]="{
										'ng-invalid ng-dirty': isError(form.get('documentType')!),
									}">
									<ng-template let-group #group>
										<div class="flex items-center">
											<span>{{ group.label }}</span>
										</div>
									</ng-template>
								</p-select>
							</div>
							<small
								*ngIf="isError(form.get('documentType')!)"
								class="text-red-500">
								กรุณาเลือกประเภทเอกสาร
							</small>
						</div>

						<p class="my-1 text-[#232323]">
							เลือกเอกสารที่ต้องการแนบ
							<span class="text-red-500">*</span>
						</p>
						<ul class="ml-5 list-disc text-sm text-[#858585]">
							<li>รองรับไฟล์ jpg, png และ pdf</li>
							<li>ขนาดไฟล์ไม่เกิน 5 MB</li>
						</ul>
						<div
							class="upload-file-box flex h-[200px] items-center justify-center"
							*ngIf="!form.get('file')?.value">
							<!-- Hidden file input -->
							<input
								type="file"
								#fileInput
								(change)="onFileSelected($event)"
								accept=".jpg, .png, .pdf"
								hidden />
							<p-button
								label="เลือกเอกสาร"
								class="bg-white"
								[outlined]="true"
								(click)="fileInput.click()" />
						</div>

						<div
							*ngIf="form.get('file')?.value"
							class="mt-2 h-14 min-h-fit rounded-lg border border-gray-200">
							<div class="flex items-center justify-between px-4 py-2">
								<div class="flex items-center">
									<img
										[src]="filePreviewUrl"
										class="mx-auto h-8 w-8 object-contain" />
								</div>
								<div class="ml-4 flex flex-1 flex-col items-start">
									<p class="mr-2 text-sm font-normal">{{ selectedFileName }}</p>
									<p class="text-xs font-light text-gray-500">
										{{ form.get("documentName")?.value.documentName }}
									</p>
								</div>
								<div class="flex items-center justify-end">
									<p-button
										label="ลบ"
										severity="danger"
										[outlined]="true"
										(onClick)="removeFile()"></p-button>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>

			<!-- Footer -->
			<app-footer>
				<div class="footer-wrapper !justify-between">
					<p-button
						label="ยกเลิก"
						styleClass="w-full"
						[outlined]="true"
						[loading]="isLoading()"
						(onClick)="cancel()" />
					<p-button
						label="บันทึก"
						styleClass="w-full"
						[loading]="isLoading()"
						[disabled]="!form.valid"
						(onClick)="submit()" />
				</div>
			</app-footer>
		</div>
	`,
})
export class AttachDocumentsComponent
	extends WorkflowStepBaseComponent
	implements OnInit
{
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);

	public form: FormGroup = new FormGroup({});
	public selectedFileName: string = "";
	public filePreviewUrl: string | ArrayBuffer | null = "";

	public groupedDocumentType = [
		{
			label: "เอกสารที่ต้องแนบ",
			value: "group1",
			items: [
				{ label: "สลิปเงินเดือนล่าสุด", value: "สลิปเงินเดือนล่าสุด" },
				{
					label: "หนังสือรับรองเงินเดือนปัจจุบัน ",
					value: "หนังสือรับรองเงินเดือนปัจจุบัน ",
				},
				{
					label: "รายการเดินบัญชีย้อนหลัง 3 เดือน ",
					value: "รายการเดินบัญชีย้อนหลัง 3 เดือน ",
				},
			],
		},
		{
			label: "เอกสารที่ต้องแนบอย่างน้อย 1 อย่าง",
			value: "optional",
			items: [
				{ label: "สำเนาทะเบียนบ้านผู้กู้", value: "สำเนาทะเบียนบ้านผู้กู้" },
				{
					label: "บัตรประจำตัวประชาชนผู้ค้ำประกัน",
					value: "บัตรประจำตัวประชาชนผู้ค้ำประกัน",
				},
				{
					label: "สำเนาทะเบียนบ้านผู้ค้ำประกัน",
					value: "สำเนาทะเบียนบ้านผู้ค้ำประกัน",
				},
				{ label: "เอกสารอื่น ๆ (ถ้ามี)", value: "เอกสารอื่น ๆ (ถ้ามี)" },
			],
		},
		{
			label: "เอกสารประกอบการพิจารณาพิจารณาสินเชื่อ",
			value: "support",
			items: [
				{ label: "???", value: "Kyoto" },
				{ label: "???", value: "Osaka" },
			],
		},
	];

	isError(control: AbstractControl): boolean {
		return control.invalid && control.touched;
	}

	ngOnInit(): void {
		this.form = new FormGroup({
			documentType: new FormControl("", [Validators.required]),
			file: new FormControl("", [Validators.required]),
		});
	}

	isImage(file: File): boolean {
		return file && file.type.startsWith("image/");
	}

	async onFileSelected(event: Event) {
		try {
			const input = event.target as HTMLInputElement;
			if (input.files && input.files.length > 0) {
				const file = input.files[0];

				// Check file type and size (5MB limit)
				const allowedTypes = ["image/jpeg", "image/png", "application/pdf"];
				if (!allowedTypes.includes(file.type)) {
					throw new Error("รองรับเฉพาะไฟล์ JPG, PNG และ PDF เท่านั้น");
				}
				if (file.size > 15 * 1024 * 1024) {
					throw new Error("ขนาดไฟล์ต้องไม่เกิน 15MB");
				}
				if (this.isImage(file)) {
					this.filePreviewUrl = "assets/svgs/img-file.svg";
				} else {
					this.filePreviewUrl = "assets/svgs/pdf-file.svg";
				}

				this.selectedFileName = file.name;
				this.form.patchValue({ file });
			}
		} catch (error: any) {
			const errorMessage = error.message || error;
			this.appUtilsService.showAlertDialog({
				header: "ไม่สามารถอัพโหลดเอกสารได้",
				message: `${errorMessage}`,
				subMessage: "Code: xxxxxx",
				acceptLabel: "ยืนยัน",
				acceptCallback: () => {},
			});
		}
	}

	public removeFile() {
		this.form.get("file")?.setValue("");
		this.filePreviewUrl = null;
		this.selectedFileName = "";
	}

	private async uploadFile() {
		const txnId = this.getTxnIdOrError();
		const documentType = this.form.get("documentType")?.value;
		const file = this.form.get("file")?.value;
		if (!documentType || !file) {
			throw new Error("กรุณาเลือกประเภทเอกสารและเลือกไฟล์");
		}

		await this.starmoneyService.uploadDocument({
			txn_id: txnId,
			document_type: documentType,
			file: file,
		});
	}

	async submit(): Promise<void> {
		try {
			await this.uploadFile();
			await this.modalController.dismiss({ refresh: true });
		} catch (error) {
			await this.handleError(error);
		}
	}

	async cancel(): Promise<void> {
		await this.modalController.dismiss();
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
