import { CommonModule } from "@angular/common";
import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from "@angular/core";
import { IonicModule, ModalController } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { interval, Subscription } from "rxjs";

@Component({
	selector: "app-circular-progress-loading-modal",
	standalone: true,
	imports: [CommonModule, IonicModule, ButtonModule],
	template: `
		<div class="loading-modal-container">
			<div class="loading-modal-content">
				<!-- Header -->
				<h1 class="loading-title">{{ title }}</h1>

				<!-- Circular Progress -->
				<div class="circular-progress-container">
					<div class="circular-progress">
						<svg viewBox="0 0 100 100" class="progress-circle">
							<circle cx="50" cy="50" r="45" class="progress-circle-bg" />
							<circle
								cx="50"
								cy="50"
								r="45"
								[class]="
									indeterminateProgress
										? 'progress-circle-path indeterminate'
										: 'progress-circle-path'
								"
								[style.strokeDashoffset]="progressOffset" />
						</svg>
						<div class="progress-icon" *ngIf="showIcon">
							<ion-icon name="checkmark-outline" *ngIf="isComplete"></ion-icon>
						</div>
					</div>
				</div>

				<!-- Status Message -->
				<h2 class="loading-status">{{ statusMessage }}</h2>

				<!-- Description -->
				<p class="loading-description">{{ description }}</p>

				<!-- Action Buttons (if provided) -->
				<div class="loading-actions" *ngIf="showButtons">
					<p-button
						*ngIf="cancelButtonLabel"
						[label]="cancelButtonLabel"
						styleClass="w-full"
						[outlined]="true"
						(onClick)="onCancelClick()"></p-button>

					<p-button
						*ngIf="actionButtonLabel"
						[label]="actionButtonLabel"
						styleClass="w-full"
						(onClick)="onActionClick()"></p-button>
				</div>
			</div>
		</div>
	`,
	styleUrls: ["./circular-progress-loading-modal.component.scss"],
})
export class CircularProgressLoadingModalComponent implements OnInit, OnDestroy {
	@Input() title: string = "ตรวจสอบข้อมูล";
	@Input() statusMessage: string = "กำลังตรวจสอบข้อมูล";
	@Input() description: string = "อยู่ระหว่างการตรวจสอบข้อมูล\nกรุณารอจนกว่าระบบจะทำงานเสร็จสิ้น";
	@Input() progress: number = 0; // 0 to 100
	@Input() autoProgress: boolean = true;
	@Input() progressDuration: number = 10000; // 10 seconds
	@Input() progressStep: number = 1;
	@Input() showIcon: boolean = false;
	@Input() isComplete: boolean = false;
	@Input() showButtons: boolean = false;
	@Input() cancelButtonLabel: string = "";
	@Input() actionButtonLabel: string = "";
	@Input() onCancel: (() => void) | null = null;
	@Input() onAction: (() => void) | null = null;
	@Input() autoClose: boolean = false;
	@Input() autoCloseDelay: number = 1000; // 1 second
	@Input() indeterminateProgress: boolean = false; // For operations with unknown completion time

	private progressSubscription: Subscription | null = null;
	private circumference: number = 2 * Math.PI * 45; // 2πr where r=45

	constructor(private modalController: ModalController) {}

	ngOnInit() {
		// Don't start auto progress if indeterminate mode is enabled
		if (this.autoProgress && !this.indeterminateProgress) {
			this.startAutoProgress();
		}
	}

	ngOnDestroy() {
		this.stopAutoProgress();
	}

	get progressOffset(): number {
		const offset = this.circumference - (this.progress / 100) * this.circumference;
		return offset;
	}

	startAutoProgress() {
		this.stopAutoProgress();

		const steps = this.progressDuration / (this.progressStep * 100);

		this.progressSubscription = interval(steps).subscribe(() => {
			if (this.progress < 100) {
				this.progress += this.progressStep;
			} else {
				this.progress = 100;
				this.isComplete = true;
				this.stopAutoProgress();

				if (this.autoClose) {
					setTimeout(() => {
						this.dismiss();
					}, this.autoCloseDelay);
				}
			}
		});
	}

	stopAutoProgress() {
		if (this.progressSubscription) {
			this.progressSubscription.unsubscribe();
			this.progressSubscription = null;
		}
	}

	setProgress(value: number) {
		this.progress = Math.min(Math.max(value, 0), 100);

		if (this.progress === 100) {
			this.isComplete = true;
		}
	}

	onCancelClick() {
		if (this.onCancel) {
			this.onCancel();
		} else {
			this.dismiss("cancel");
		}
	}

	onActionClick() {
		if (this.onAction) {
			this.onAction();
		} else {
			this.dismiss("action");
		}
	}

	dismiss(role?: string) {
		this.modalController.dismiss(null, role);
	}
}
