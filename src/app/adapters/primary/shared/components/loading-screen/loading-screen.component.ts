import { CommonModule } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Ng<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@angular/core";
import { Subscription } from "rxjs";
import { AppUtilsService } from "src/app/core/application/app-utils.service";

@Component({
	standalone: true,
	imports: [CommonModule],
	selector: "app-loading-screen",
	styleUrl: "./loading-screen.component.scss",
	template: `
		<div class="loading-screen" [class.show]="isLoading">
			<div class="loading-content">
				<div class="loader"></div>
				<p class="loading-text">{{ message }}</p>
			</div>
		</div>
	`,
})
export class LoadingScreenComponent implements AfterViewInit, OnDestroy {
	isLoading = false;
	message = "กำลังโหลด...";

	private loadingSubscription: Subscription | null = null;
	private messageSubscription: Subscription | null = null;

	constructor(
		private appUtils: AppUtilsService,
		private cdr: ChangeDetectorRef,
		private ngZone: NgZone,
	) {}

	ngAfterViewInit(): void {
		// ใช้ setTimeout เพื่อหลีกเลี่ยง ExpressionChangedAfterItHasBeenCheckedError
		setTimeout(() => {
			// สมัครสมาชิกเพื่อรับการเปลี่ยนแปลงสถานะ loading
			this.loadingSubscription = this.appUtils.isLoading$.subscribe(
				(isLoading) => {
					this.ngZone.run(() => {
						this.isLoading = isLoading;
						this.cdr.detectChanges();
					});
				},
			);

			// สมัครสมาชิกเพื่อรับการเปลี่ยนแปลงข้อความ loading
			this.messageSubscription = this.appUtils.loadingMessage$.subscribe(
				(message) => {
					this.ngZone.run(() => {
						this.message = message;
						this.cdr.detectChanges();
					});
				},
			);
		});
	}

	ngOnDestroy(): void {
		// ยกเลิกการสมัครสมาชิกเมื่อ component ถูกทำลาย
		if (this.loadingSubscription) {
			this.loadingSubscription.unsubscribe();
		}

		if (this.messageSubscription) {
			this.messageSubscription.unsubscribe();
		}
	}
}
