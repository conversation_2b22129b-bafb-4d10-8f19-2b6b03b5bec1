.loading-screen {
	@apply invisible fixed inset-0 z-[15000] flex items-center justify-center bg-black/50 opacity-0 transition-all duration-300;

	&.show {
		@apply visible opacity-100;
	}
}

.loading-content {
	@apply flex flex-col items-center gap-4 rounded-lg bg-white p-8 shadow-lg;
}

.loading-text {
	@apply text-sm text-gray-600;
}

.loader {
	width: 48px;
	aspect-ratio: 1;
	border-radius: 50%;
	border: 4px solid lightblue;
	border-right-color: rgb(0, 132, 255);
	animation: l2 1s infinite linear;
}

@keyframes l2 {
	to {
		transform: rotate(1turn);
	}
}
