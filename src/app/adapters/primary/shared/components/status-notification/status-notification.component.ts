import { CommonModule } from "@angular/common";
import { Component, Input, OnInit } from "@angular/core";
import { IonicModule } from "@ionic/angular";
import { ModalController } from "@ionic/angular/standalone";
import { addIcons } from "ionicons";
import {
	alertCircleOutline,
	checkmarkCircleOutline,
	closeCircleOutline,
	informationCircleOutline,
} from "ionicons/icons";
import { ButtonModule } from "primeng/button";

export interface StatusNotificationOptions {
	statusType?: "success" | "warning" | "error" | "info";
	title?: string;
	message?: string;
	description?: string;
	errorCode?: string;
	errorLabel?: string;
	primaryButtonLabel?: string;
	secondaryButtonLabel?: string;
	onPrimaryAction?: () => void | Promise<void>;
	onSecondaryAction?: () => void | Promise<void>;
}

@Component({
	standalone: true,
	imports: [CommonModule, IonicModule, ButtonModule],
	selector: "app-status-notification",
	styleUrls: ["./status-notification.component.scss"],
	template: `
		<div class="status-notification-container">
			<div class="status-notification-content">
				<!-- Header -->
				<h1 class="status-title">{{ config.title || "" }}</h1>

				<!-- Status Icon -->
				<div class="status-icon-container" [ngClass]="statusClass">
					<div class="status-icon">
						<ng-container [ngSwitch]="config.statusType">
							<img *ngSwitchCase="'success'" src="assets/svgs/success.svg" class="h-40 w-40" />
							<img *ngSwitchCase="'warning'" src="assets/svgs/warning.svg" class="h-40 w-40" />
							<img *ngSwitchCase="'error'" src="assets/svgs/error.svg" class="h-40 w-40" />
							<ion-icon *ngSwitchCase="'info'" name="information-circle-outline"></ion-icon>
						</ng-container>
					</div>
				</div>

				<!-- Main Message -->
				<h2 class="status-message">{{ config.message || "" }}</h2>

				<!-- Description -->
				<div class="status-description" [innerHTML]="config.description || ''"></div>

				<!-- Error Code (if provided) -->
				<div *ngIf="config.errorCode" class="status-error-code">
					<span class="error-label">{{ config.errorLabel || "Code:" }}</span>
					<span class="error-code">{{ config.errorCode }}</span>
				</div>

				<!-- Action Buttons -->
				<div class="status-actions">
					<p-button
						*ngIf="config.secondaryButtonLabel"
						[label]="config.secondaryButtonLabel"
						styleClass="w-full"
						[outlined]="true"
						(onClick)="onSecondaryClick()"></p-button>

					<p-button
						[label]="config.primaryButtonLabel || 'ตกลง'"
						styleClass="w-full"
						(onClick)="onPrimaryClick()"></p-button>
				</div>
			</div>
		</div>
	`,
})
export class StatusNotificationComponent implements OnInit {
	@Input() config: StatusNotificationOptions = {};

	constructor(private modalController: ModalController) {
		addIcons({
			checkmarkCircleOutline,
			alertCircleOutline,
			closeCircleOutline,
			informationCircleOutline,
		});
	}

	ngOnInit() {
		console.log("Status notification config in ngOnInit:", this.config);
	}

	get statusClass(): string {
		return `status-${this.config.statusType || "success"}`;
	}

	async onPrimaryClick() {
		console.log("Primary button clicked");
		if (this.config.onPrimaryAction) {
			console.log("Executing primary action", this.config.onPrimaryAction);
			await this.config.onPrimaryAction();
		} else {
			console.log("Dismissing modal");
			await this.modalController.dismiss({ action: "primary" });
		}
	}

	async onSecondaryClick() {
		console.log("Secondary button clicked");
		if (this.config.onSecondaryAction) {
			console.log("Executing secondary action", this.config.onSecondaryAction);
			await this.config.onSecondaryAction();
		} else {
			console.log("Dismissing modal");
			await this.modalController.dismiss({ action: "secondary" });
		}
	}
}
