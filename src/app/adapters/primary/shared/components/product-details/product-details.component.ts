import { CommonModule } from "@angular/common";
import { Component, inject, Input } from "@angular/core";
import { Product, ProductList } from "@domain/models/product.model";
import { IonicModule, ModalController } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { FooterComponent } from "../footer/footer.component";

@Component({
	selector: "app-see-product-details",
	imports: [CommonModule, IonicModule, FooterComponent, ButtonModule],
	styleUrl: "./product-details.component.scss",
	template: `
		<div class="app-layout-dialog">
			<!-- Content -->
			<div class="app-content">
				<div class="content-wrapper !max-w-full">
					<p class="page-title-description-center">รายละเอียดสินค้า</p>
					<img
						[src]="getProductImage()"
						[alt]="getProductName()"
						class="product-image" />
					<p class="page-title-description-center">
						{{ getProductName() }}
					</p>
					<p class="product-price">ราคา {{ getProductPrice() }} บาท</p>
					<p class="product-details-title">รายละเอียดสินค้า</p>
					<p
						class="product-description"
						[innerHTML]="getProductDescription()"></p>
				</div>
			</div>

			<!-- Action Buttons -->
			<app-footer>
				<div class="action-buttons">
					<p-button
						label="ปิดหน้าต่าง"
						class="w-full"
						styleClass="w-full"
						[outlined]="true"
						[fluid]="true"
						(onClick)="cancel()" />
					@if (canChoose) {
						<p-button
							label="เลือกสินค้า"
							class="w-full"
							styleClass="w-full"
							[fluid]="true"
							(onClick)="onSubmit()" />
					}
				</div>
			</app-footer>
		</div>
	`,
})
export class ProductDetailsComponent {
	@Input() productList: ProductList | null = null;
	@Input() product: Product | null = null;
	@Input() canChoose: boolean = true;
	private modalController = inject(ModalController);

	/**
	 * Determines if we're displaying a Product or ProductList
	 */
	get isProductMode(): boolean {
		return !!this.product;
	}

	/**
	 * Gets the product image from either Product or ProductList
	 */
	getProductImage(): string {
		if (this.isProductMode) {
			return this.product?.image || "";
		}
		return this.productList?.productImage || "";
	}

	/**
	 * Gets the product name from either Product or ProductList
	 */
	getProductName(): string {
		if (this.isProductMode) {
			return this.product?.modelName || "-";
		}
		return this.productList?.productModelName || "-";
	}

	/**
	 * Gets the product price from either Product or ProductList
	 */
	getProductPrice(): string {
		if (this.isProductMode) {
			return this.product?.price?.toString() || "-";
		}
		return this.productList?.productPrice?.toString() || "-";
	}

	/**
	 * Gets the product description from either Product or ProductList
	 */
	getProductDescription(): string {
		const description = this.isProductMode
			? this.product?.description
			: this.productList?.productDescription;

		return `รายละเอียดเพิ่มเติมของสินค้าที่กำลังดูอยู่ ${description || "-"}`;
	}

	/**
	 * Handles the submit action and returns the appropriate product data
	 */
	async onSubmit(): Promise<void> {
		if (this.isProductMode) {
			await this.modalController.dismiss({
				isSelected: true,
				product: this.product,
			});
		} else {
			await this.modalController.dismiss({
				isSelected: true,
				productList: this.productList,
			});
		}
	}

	/**
	 * Handles the cancel action
	 */
	async cancel(): Promise<void> {
		await this.modalController.dismiss({
			isSelected: false,
			product: null,
			productList: null,
		});
	}
}
