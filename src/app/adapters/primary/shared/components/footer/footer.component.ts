import { CommonModule } from "@angular/common";
import { Component, Input } from "@angular/core";

export interface FooterAction {
	icon?: string;
	label: string;
	action: () => void;
	variant?: "primary" | "secondary" | "outline";
	disabled?: boolean;
}

@Component({
	selector: "app-footer",
	standalone: true,
	imports: [CommonModule],
	template: `
		<footer class="app-footer" [class]="styleClass">
			<ng-content></ng-content>
		</footer>
	`,
})
export class FooterComponent {
	@Input() version?: string;
	@Input() showVersion: boolean = false;
	@Input() actions?: FooterAction[] = [];
	@Input() styleClass?: string;
}
