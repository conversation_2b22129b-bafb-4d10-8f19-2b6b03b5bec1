.form-field {
  @apply w-full pb-2;
}
.form-label {
  @apply text-sm text-gray-500 mb-1 block;
}
/* Custom Scrollbar Styles */
.scrollbar-style {
  scrollbar-width: thin;
  scrollbar-color: #0192fb #f1f1f1;

  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #0192fb;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #0066cc;
  }
}

/* Container Style */
.relative {
  position: relative;
  padding-bottom: 6px;
  /* Space for scrollbar */
}

/* Scrollable container styles */
.scroll-container {
  @apply relative w-full overflow-x-auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  /* Smooth scrolling on iOS */

  /* Hide scrollbar for mobile */
  @media (hover: none) and (pointer: coarse) {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;

    /* IE and Edge */
    &::-webkit-scrollbar {
      display: none;
      /* Chrome, Safari and Opera */
    }
  }

  /* Show scrollbar for desktop */
  @media (hover: hover) {
    scrollbar-width: thin;
    scrollbar-color: #0192fb #f1f1f1;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #0192fb;
      border-radius: 3px;

      &:hover {
        background: #0066cc;
      }
    }
  }
}

.scroll-content {
  @apply flex gap-3 pb-2 w-max;
  padding-bottom: 10px;
}

/* Installment Plan Component Styles */
.installment-section {
  @apply w-full flex flex-row gap-8;
}

.installment-section-half {
  @apply w-1/2 flex flex-col gap-2;
}

.section-title {
  @apply text-lg font-medium mb-2;
}

.section-subtitle {
  @apply text-lg font-medium;
}

.detail-row {
  @apply flex justify-between gap-4;
}

.detail-label {
  @apply w-1/3 text-sm text-gray-500 text-left;
}

.detail-value {
  @apply flex-1 text-sm text-gray-500 text-right;
}

.detail-label-bold {
  @apply w-1/3 text-sm text-gray-900 font-bold text-left;
}

.detail-value-bold {
  @apply flex-1 text-sm text-gray-900 font-bold text-right;
}

/* Scroll container for options */
.scroll-container {
  @apply w-full overflow-hidden relative;
}

.scroll-content {
  @apply flex gap-2 py-2 overflow-x-auto;
}

.disclaimer-text {
  @apply text-xs text-gray-500 text-right flex flex-col mt-1;
}

/* Card items for selection */
.card-item {
  @apply flex flex-col justify-center items-center p-3 rounded-lg min-w-[100px] border border-primary cursor-pointer;
  @apply transition-all duration-200 ease-in-out;
  @apply w-[120px] h-[60px];
  @apply text-sm text-primary;

  .title {
    @apply font-medium;
  }
}

.card-item.selected {
  @apply bg-primary/10 border-2 border-primary;
}

.card-item.disabled {
  @apply opacity-50 cursor-not-allowed bg-gray-100 border-gray-500;

  p {
    @apply text-gray-500;
  }
}

/* Indicators for scrollable content */
.indicator-container {
  @apply flex justify-center gap-1;
}

.indicator {
  @apply w-6 h-2 rounded-full bg-gray-300;
}

.indicator.active {
  @apply bg-primary;
}

.indicator.inactive {
  @apply bg-gray-300;
}
