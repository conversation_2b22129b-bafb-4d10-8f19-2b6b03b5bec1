import { CommonModule } from "@angular/common";
import { Component, inject, Input, OnInit } from "@angular/core";
import { LoanType } from "@domain/models/loan.model";
import { PeriodRangePrice, Product } from "@domain/models/product.model";
import { IonicModule, ModalController } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { SelectModule } from "primeng/select";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { FooterComponent } from "../footer/footer.component";

export interface InstallmentPlanOption {
	period: number;
	price: number;
	disabled?: boolean;
}

export interface DownPaymentOption {
	percent: number;
	price: number;
	disabled?: boolean;
}

@Component({
	selector: "app-installment-plan-info",
	imports: [
		CommonModule,
		IonicModule,
		FooterComponent,
		ButtonModule,
		SelectModule,
	],
	styleUrl: "./installment-plan-info.component.scss",
	template: `
		<ion-content>
			<div class="app-layout-dialog">
				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper !max-w-full">
						<p class="page-title mb-6 !text-lg !font-medium">
							การผ่อนชำระสินค้า
						</p>

						<div class="installment-section">
							<div class="installment-section-half">
								<p class="section-title">นโยบายการขาย</p>
								<p-select
									placeholder="ขายสินค้าหมวด U ทุกชนิดทุกช่องทางขาย"
									class="!text-sm"
									[fluid]="true"
									[disabled]="true" />

								<div class="my-2 flex flex-col gap-2">
									<p class="block text-sm font-bold text-gray-900">คำอธิบาย</p>
									<p class="line-clamp-2 text-sm text-gray-500">
										Lorem ipsum, dolor sit amet consectetur adipisicing elit.
										Ratione dolor optio officiis consequuntur quisquam? Modi
										reiciendis dolore nihil dolorem maiores?
									</p>
								</div>

								<div class="my-2 flex flex-col gap-2">
									<p class="section-subtitle">เลือกเงินดาวน์ที่ลูกค้าต้องการ</p>
									<div class="scroll-container">
										<div class="scroll-content">
											<div
												*ngFor="let item of downPaymentOptions"
												class="card-item"
												[class.selected]="
													selectedDownPayment?.percent === item.percent
												"
												[class.disabled]="item.disabled"
												(click)="!item.disabled && setDownPayment(item)">
												<p class="title">{{ item.percent || 0 | number }}%</p>
												<p>{{ item.price || 0 | number }} บาท</p>
											</div>
										</div>
									</div>

									<div class="indicator-container">
										<div
											*ngFor="let item of downPaymentOptions"
											class="indicator"
											[class.active]="
												selectedDownPayment?.percent === item.percent
											"
											[class.inactive]="
												selectedDownPayment?.percent !== item.percent
											"></div>
									</div>
								</div>

								<div class="my-2 flex flex-col gap-2">
									<p class="section-subtitle">เลือกจำนวนงวดที่ลูกค้าต้องการ</p>
									<div class="scroll-container">
										<div class="scroll-content">
											<div
												*ngFor="let item of installmentPlanOptions"
												class="card-item"
												[class.selected]="
													selectedInstallmentPlan?.period === item.period
												"
												[class.disabled]="item.disabled"
												(click)="!item.disabled && setInstallment(item)">
												<p class="title">{{ item.period || 0 | number }} งวด</p>
												<p>{{ item.price || 0 | number }} บาท</p>
											</div>
										</div>
									</div>

									<div class="indicator-container">
										<div
											*ngFor="let item of installmentPlanOptions"
											class="indicator"
											[class.active]="
												selectedInstallmentPlan?.period === item.period
											"
											[class.inactive]="
												selectedInstallmentPlan?.period !== item.period
											"></div>
									</div>
								</div>
							</div>

							<div class="installment-section-half">
								<h2 class="section-title">รายละเอียดสินค้า</h2>

								<p class="detail-row">
									<span class="detail-label">สินค้าที่เลือก</span>
									<span class="detail-value">
										{{ productInfo?.modelCode || "" }}
									</span>
								</p>

								<p class="detail-row">
									<span class="detail-label">ราคาสินค้า</span>
									<span class="detail-value">
										{{ productInfo?.price || 0 | number }} บาท
									</span>
								</p>

								<p class="detail-row">
									<span class="detail-label">ส่วนลด:</span>
									<span class="detail-value">
										{{ productInfo?.discount || 0 | number }} บาท
									</span>
								</p>

								<p class="detail-row">
									<span class="detail-label-bold">ราคาสุทธิ:</span>
									<span class="detail-value-bold">
										{{ productInfo?.netPrice || 0 | number }} บาท
									</span>
								</p>

								<hr class="my-4" />

								<h2 class="section-title">เงื่อนไขการผ่อนชำระ</h2>

								<p class="detail-row">
									<span class="detail-label-bold">ดาวน์:</span>
									<span class="detail-value-bold">
										({{ selectedDownPayment?.percent || "-" }}%)
										{{ selectedDownPayment?.price || 0 | number }} บาท
									</span>
								</p>

								<p class="detail-row">
									<span class="detail-label-bold">ดอกเบี้ย:</span>
									<span class="detail-value-bold">
										{{ productInfo?.interestRate || 0 | number }}% ต่อเดือน
									</span>
								</p>

								<p class="detail-row">
									<span class="detail-label-bold">ผ่อนต่องวด:</span>
									<span class="detail-value-bold">
										{{ selectedInstallmentPlan?.price || 0 | number }} บาท
									</span>
								</p>

								<p class="detail-row">
									<span class="detail-label-bold">จำนวนงวด:</span>
									<span class="detail-value-bold">
										{{ selectedInstallmentPlan?.period || 0 | number }} งวด
									</span>
								</p>

								<p class="disclaimer-text">
									<span>
										* กรุณาแจ้งให้ลูกค้าทราบ
										ข้อมูลดังกล่าวเป็นการประเมินเบื้องต้นจากระบบ
									</span>
									<span>อาจมีการเปลี่ยนแปลงตามผลการพิจารณาสินเชื่อ</span>
								</p>
							</div>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper !justify-between">
						<p-button
							label="ย้อนกลับ"
							styleClass="w-full"
							[outlined]="true"
							(onClick)="cancel()" />
						<p-button
							label="ต่อไป"
							styleClass="w-full"
							[disabled]="!selectedDownPayment || !selectedInstallmentPlan"
							(onClick)="submit()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class InstallmentPlanInfoComponent implements OnInit {
	private appUtils = inject(AppUtilsService);
	private modalController = inject(ModalController);

	@Input() loanInfo: LoanType | null = null;
	@Input() productInfo: Product | null = null;
	personalInfo: any | null = null;

	public downPaymentOptions: DownPaymentOption[] = [];
	public installmentPlanOptions: InstallmentPlanOption[] = [];

	public selectedDownPayment: DownPaymentOption | null = null;
	public selectedInstallmentPlan: InstallmentPlanOption | null = null;

	ngOnInit(): void {
		if (this.loanInfo?.isHirePurchase()) {
			this.initializeInstallmentData();
		}
		if (this.loanInfo?.isPersonalLoan()) {
			this.initializePersonalLoanData();
		}
	}

	private initializeInstallmentData(): void {
		if (!this.productInfo?.downRangePrice) {
			console.error("Product or down_range_price is missing");
			return;
		}

		this.downPaymentOptions = this.productInfo.downRangePrice;
		this.setProductDefaultValues();
	}

	private setProductDefaultValues(): void {
		if (this.downPaymentOptions.length === 0) {
			console.error("No down payment options available");
			return;
		}

		// หา down payment แรกที่ไม่ได้ disable
		const firstAvailableDownPayment = this.downPaymentOptions.find(
			(downPayment) => !downPayment.disabled,
		);
		if (!firstAvailableDownPayment) {
			console.error("All down payment options are disabled");
			return;
		}

		this.selectedDownPayment = firstAvailableDownPayment;
		const periods = this.getPeriodByPercentDown(
			this.selectedDownPayment.percent,
		);
		console.log("Periods", periods);

		if (periods && periods.length > 0) {
			this.installmentPlanOptions = periods;
			const firstAvailablePeriod = periods.find((period) => !period.disabled);
			console.log("First Available Period", firstAvailablePeriod);

			if (firstAvailablePeriod) {
				this.selectedInstallmentPlan = firstAvailablePeriod;
			} else {
				this.selectedInstallmentPlan = null;
			}
		}
	}

	private initializePersonalLoanData(): void {
		if (!this.personalInfo?.periodRangePrice) {
			console.error("Product or down_range_price is missing");
			return;
		}

		this.installmentPlanOptions = this.personalInfo.periodRangePrice;
		this.setPersonalLoanDefaultValues();
	}

	private setPersonalLoanDefaultValues(): void {
		if (
			!this.personalInfo?.periodRangePrice ||
			this.personalInfo.periodRangePrice.length === 0
		) {
			console.error("No period options available");
			return;
		}

		// หา period แรกที่ไม่ได้ disable
		const firstAvailablePeriod = this.personalInfo.periodRangePrice.find(
			(period: any) => !period.disabled,
		);

		if (firstAvailablePeriod) {
			this.selectedInstallmentPlan = firstAvailablePeriod;
		} else {
			this.selectedInstallmentPlan = null;
		}
	}

	public setDownPayment(value: DownPaymentOption): void {
		this.selectedDownPayment = value;
		const periods = this.getPeriodByPercentDown(value.percent);

		if (periods) {
			this.installmentPlanOptions = periods;
			const firstAvailablePeriod = periods.find((period) => !period.disabled);
			if (firstAvailablePeriod) {
				this.selectedInstallmentPlan = firstAvailablePeriod;
			} else {
				this.selectedInstallmentPlan = null;
			}
		}
	}

	private getPeriodByPercentDown(
		percent: number,
	): InstallmentPlanOption[] | null {
		if (!this.productInfo?.periodRangePrice) return null;
		const result = this.productInfo.periodRangePrice.find(
			(item: PeriodRangePrice) => item.percent === percent,
		);
		console.log("Result", result);
		return result?.period || null;
	}

	public setInstallment(value: InstallmentPlanOption): void {
		this.selectedInstallmentPlan = value;
	}

	async submit(): Promise<void> {
		if (this.loanInfo?.isHirePurchase()) {
			if (!this.selectedDownPayment) {
				await this.appUtils.showInfo("กรุณาเลือกเงินดาวน์");
				return;
			}

			if (!this.selectedInstallmentPlan) {
				await this.appUtils.showInfo("กรุณาเลือกจำนวนงวด");
				return;
			}

			this.modalController.dismiss({
				downPayment: this.selectedDownPayment,
				installmentPlan: this.selectedInstallmentPlan,
			});
		} else if (this.loanInfo?.isPersonalLoan()) {
			if (!this.selectedInstallmentPlan) {
				await this.appUtils.showInfo("กรุณาเลือกจำนวนงวด");
				return;
			}

			this.modalController.dismiss({
				installmentPlan: this.selectedInstallmentPlan,
			});
		} else {
			this.appUtils.showError("เกิดข้อผิดพลาด", "ไม่รู้จักประเภทสินเชื่อ");
			return;
		}
	}

	async cancel(): Promise<void> {
		await this.modalController.dismiss();
	}
}
