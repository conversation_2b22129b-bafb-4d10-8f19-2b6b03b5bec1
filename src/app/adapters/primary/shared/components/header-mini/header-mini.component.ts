import { WorkflowModalService } from "src/app/core/application/workflow/workflow-modal.service";
import { CommonModule } from "@angular/common";
import { Component, computed, inject, Input } from "@angular/core";
import { MenuItem } from "primeng/api";
import { AvatarModule } from "primeng/avatar";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { DashboardService } from "src/app/core/application/dashboard.service";

@Component({
	selector: "app-header-mini",
	standalone: true,
	imports: [CommonModule, AvatarModule, BreadcrumbModule],
	styleUrls: ["./header-mini.component.scss"],
	template: `
		<header class="header-mini">
			@if (customHeader) {
				<ng-content></ng-content>
			} @else {
				@if (mode === "root") {
					<div class="breadcrumb-container">
						<p-breadcrumb [model]="items()" [home]="home">
							<ng-template #item let-item>
								<span [class.text-primary]="isLastItem(item)">
									{{ item.label }}
								</span>
							</ng-template>
							<ng-template #separator>/</ng-template>
						</p-breadcrumb>
						<div class="user-info">
							<span class="user-info-text">
								คุณสตาร์ ทดสอบระบบ | ID: 0000001
							</span>
							<p-avatar styleClass="avatar-icon" label="สท" shape="circle" />
						</div>
					</div>
				} @else if (mode === "workflow") {
					<div class="header-mini-step">
						<div class="circle">{{ stepId() }}</div>
						<p class="step-title">{{ currentStepName() }}</p>
					</div>
				} @else {
					<div class="header-mini-step">
						<p class="step-title">Unknown Mode</p>
					</div>
				}
			}
		</header>
	`,
})
export class HeaderMiniComponent {
	@Input() customHeader: boolean = false;
	@Input() mode: "root" | "workflow" = "root";

	private dashboardService = inject(DashboardService);
	private workflowModalService = inject(WorkflowModalService);
	protected readonly currentStepId = this.workflowModalService.currentStepId;
	protected readonly currentStepName =
		this.workflowModalService.currentStepName;
	protected stepId = computed(() => {
		// use only 1 . like 1.1 1.2
		// if 1.3.1 use 1.3
		const stepId = this.currentStepId();
		if (!stepId) return "";
		if (stepId.includes(".")) {
			return stepId.split(".")[0] + "." + stepId.split(".")[1];
		}
		return stepId;
	});

	protected readonly activePrimaryMenu =
		this.dashboardService.activePrimaryMenu;
	protected readonly activeSecondaryMenu =
		this.dashboardService.activeSecondaryMenu;
	protected items = computed(() => {
		return [
			{ label: this.activePrimaryMenu()?.label },
			{ label: this.activeSecondaryMenu()?.label },
		];
	});

	home: MenuItem | undefined;

	constructor() {}

	isLastItem(item: any): boolean {
		const currentItems = this.items();
		if (!currentItems || currentItems.length === 0) return false;
		return currentItems.indexOf(item) === currentItems.length - 1;
	}
}
