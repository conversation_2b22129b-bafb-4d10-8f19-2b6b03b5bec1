/* Base Layout */
.header-mini {
	@apply flex items-center justify-start border-b border-gray-200;
	@apply h-[30px] shrink-0;
}

/* Root Mode - Breadcrumb Layout */
.breadcrumb-container {
	@apply flex flex-1 flex-row items-center justify-between px-4 text-xs;
}

/* User Info Section */
.user-info {
	@apply flex flex-row items-center gap-2;
}

.user-info-text {
	@apply hidden text-xs text-gray-700 sm:block;
}

/* Workflow Mode - Step Layout */
.header-mini-step {
	@apply flex flex-1 flex-row items-center gap-2 px-4;
}

.circle {
	@apply flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs text-blue-500;
}

.step-title {
	@apply text-xs text-gray-900;
}

/* Avatar Customization */
.p-avatar {
	@apply h-4 w-4 text-xs;
}
