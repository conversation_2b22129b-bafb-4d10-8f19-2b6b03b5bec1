.step-counter-container {
	@apply flex h-full w-20 flex-col border-r border-gray-200 bg-gray-100;
}

.step-item {
	@apply flex cursor-pointer flex-col items-center border-l-[3px] border-transparent px-2 py-3 transition-all duration-300;

	&:hover {
		@apply bg-gray-200;
	}

	&.active {
		@apply border-l-[3px] border-primary bg-white;
	}

	&.completed {
		.step-icon {
			@apply text-primary;
		}
	}

	&.active {
		.step-icon {
			@apply text-primary;
		}

		.step-title {
			@apply text-primary;
		}
	}
}

.step-icon {
	@apply flex h-10 w-10 items-center justify-center text-gray-500;
}

.step-text {
	@apply flex flex-col;
}

.step-title {
	@apply text-center text-xs font-normal text-gray-800;
}

.step-subtitle {
	@apply mt-0.5 text-xs text-gray-500;
}