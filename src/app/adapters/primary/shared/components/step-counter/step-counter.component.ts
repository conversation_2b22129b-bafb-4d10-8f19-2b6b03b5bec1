import { CommonModule } from "@angular/common";
import {
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChanges,
	inject,
} from "@angular/core";
import { StepCounterService } from "../../services/step-counter.service";

export interface StepItem {
	id: string;
	title: string;
	subtitle?: string;
	icon: {
		inactive: string;
		active: string;
	};
	completed?: boolean;
}

@Component({
	selector: "app-step-counter",
	standalone: true,
	imports: [CommonModule],
	styleUrls: ["./step-counter.component.scss"],
	template: `
		<div class="step-counter-container">
			<div
				*ngFor="let step of steps; let i = index; trackBy: trackByStepId"
				class="step-item"
				[class.active]="isActive(step.id)"
				[class.completed]="isCompleted(step.id)">
				<div class="step-icon">
					<img [src]="getStepIcon(step)" class="h-5 w-5" />
				</div>
				<div class="step-text">
					<div class="step-title">{{ step.title }}</div>
				</div>
			</div>
		</div>
	`,
})
export class StepCounterComponent implements OnInit, OnChanges {
	@Input() activeStep: string = "";

	// Inject the StepCounterService
	private stepCounterService = inject(StepCounterService);

	// Computed properties
	get steps(): StepItem[] {
		return this.stepCounterService.steps();
	}

	get activeStepIndex(): number {
		return this.stepCounterService.activeStepIndex();
	}

	constructor() {}

	ngOnInit(): void {
		this.initializeSteps();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes["activeStep"] && !changes["activeStep"].firstChange) {
			const newActiveStep = changes["activeStep"].currentValue;
			if (
				newActiveStep &&
				newActiveStep !== this.stepCounterService.activeStepId()
			) {
				// Set active step without updating completed status
				this.stepCounterService.setActiveStep(newActiveStep, false);
			}
		}
	}

	/**
	 * Initialize steps if needed or update active step
	 */
	private initializeSteps(): void {
		if (this.steps.length === 0) {
			// Initialize with default loan application steps
			// Clear completed steps when initializing a new session
			this.stepCounterService.initializeLoanApplicationSteps(
				this.activeStep,
				true,
			);
		} else if (
			this.activeStep &&
			this.activeStep !== this.stepCounterService.activeStepId()
		) {
			// Just update the active step without updating completed status
			this.stepCounterService.setActiveStep(this.activeStep, false);
		}
	}

	/**
	 * Check if a step is active
	 */
	isActive(stepId: string): boolean {
		return this.stepCounterService.isStepActive(stepId);
	}

	/**
	 * Check if a step is completed
	 * @param stepId The ID of the step to check
	 * @returns True if the step is completed, false otherwise
	 */
	isCompleted(stepId: string): boolean {
		// Only check if the step is explicitly marked as completed
		return this.stepCounterService.isStepCompleted(stepId);
	}

	/**
	 * Get the appropriate icon for a step
	 */
	getStepIcon(step: StepItem): string {
		if (this.isCompleted(step.id)) {
			return "assets/svgs/checkmark-circle.svg";
		}

		return this.isActive(step.id) ? step.icon.active : step.icon.inactive;
	}

	/**
	 * Track function for ngFor to improve performance
	 * @param _ Index of the item (unused)
	 * @param step Step item
	 * @returns Unique identifier for the step
	 */
	trackByStepId(_: number, step: StepItem): string {
		return step.id;
	}
}
