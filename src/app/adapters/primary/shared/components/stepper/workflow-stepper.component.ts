import { CommonModule } from "@angular/common";
import {
	ChangeDetectorRef,
	Component,
	Input,
	<PERSON><PERSON><PERSON>roy,
	OnInit,
	effect,
	inject,
	signal,
} from "@angular/core";
import { ButtonModule } from "primeng/button";
import { DrawerModule } from "primeng/drawer";
import { WorkflowModalService } from "../../../../../core/application/workflow/workflow-modal.service";
import { WorkflowStep } from "../../../../../core/domain/workflow/workflow.model";

export interface StepItem {
	id: string;
	number: string;
	title: string;
	description?: string;
	isActive: boolean;
	isCompleted: boolean;
	subSteps?: SubStepItem[];
}

export interface SubStepItem {
	id: string;
	number: string;
	title: string;
	description?: string;
	isActive: boolean;
	isCompleted: boolean;
}

@Component({
	selector: "app-workflow-stepper",
	standalone: true,
	imports: [CommonModule, ButtonModule, DrawerModule],
	styleUrls: ["./workflow-stepper.component.scss"],
	template: `
		<div class="workflow-container">
			<div class="nav-column">
				<div class="nav-header">
					<div class="nav-title">ขั้นตอน</div>
				</div>

				<div class="nav-content mt-4">
					<div *ngFor="let step of mainSteps">
						<div
							class="main-step"
							[class.active]="step.isActive"
							[class.completed]="step.isCompleted">
							<div class="step-indicator">
								@if (step.isCompleted) {
									<img class="completion-icon" src="assets/svgs/checkmark-circle.svg" />
								}
								<div class="step-number">{{ step.number }}</div>
							</div>
						</div>
						<p class="main-step-title">
							{{ step.title }}
						</p>
					</div>
				</div>

				<div class="nav-footer">
					<p-button
						[rounded]="true"
						[text]="true"
						aria-label="toggle drawer"
						(click)="visible.set(true)">
						<img src="assets/svgs/menu-show.svg" alt="Menu Show" />
					</p-button>
				</div>
			</div>

			<div class="details-column">
				<div class="details-header">
					<div class="details-title">
						<img
							src="assets/svgs/mini-starmoney-logo.svg"
							alt="SMART SALE v1.0.0"
							class="h-4 w-4" />
						<p>SMART SALE v1.0.0</p>
					</div>
					<div class="details-transaction">txn no.</div>
				</div>

				<div
					class="substeps-container"
					*ngIf="activeMainStep && activeMainStep.subSteps && activeMainStep.subSteps.length > 0">
					<div class="substeps-header">
						<div class="substeps-title">
							{{ activeMainStep.number }}. {{ activeMainStep.title }}
						</div>
					</div>

					<div class="substeps-content">
						<div
							*ngFor="let subStep of activeMainStep.subSteps"
							class="substep-item"
							[class.active]="subStep.isActive"
							[class.completed]="subStep.isCompleted">
							<div class="substep-indicator">
								@if (subStep.isCompleted) {
									<img src="assets/svgs/checkmark.svg" />
								} @else {
									<div class="substep-number">
										{{ subStep.number }}
									</div>
								}
							</div>

							<div class="substep-content">
								<div class="substep-title" [class.active]="subStep.isActive">
									{{ subStep.title }}
								</div>
								<div class="substep-description" *ngIf="subStep.description">
									{{ subStep.description }}
								</div>
							</div>
						</div>
					</div>
				</div>

				<div
					class="empty-state"
					*ngIf="
						!activeMainStep || !activeMainStep.subSteps || activeMainStep.subSteps.length === 0
					">
					<div class="empty-message">
						<p>กรุณาเลือกขั้นตอนเพื่อดำเนินการต่อ</p>
					</div>
				</div>

				<div class="details-footer">
					<div class="details-footer-content">
						<div class="details-footer-row">
							<div class="details-footer-col">
								<p class="message-title">ประเภทสินเชื่อ</p>
								<p class="message-value">สินเชื่อเช่าซื้อ</p>
							</div>
							<div class="details-footer-col">
								<p class="message-title">โค้ดการทำรายการ</p>
								<p class="message-value">SS0012345</p>
							</div>
						</div>
						<div class="details-footer-col">
							<p class="message-title">ชื่อลูกค้า</p>
							<p class="message-value">นายสมชาย สายเสมอ</p>
						</div>
					</div>
					<div class="details-footer-staff">
						<p class="message-title">เจ้าหน้าที่ผู้ทำการสมัคร</p>
						<p class="message-value">คุณสตาร์ ทดสอบระบบ | ID: 0000001</p>
					</div>
				</div>
			</div>
		</div>

		<p-drawer [(visible)]="visible" [closable]="false">
			<ng-template #header>
				<div class="workflow-header">
					<div class="workflow-header-title">ขั้นตอนที่</div>
				</div>
			</ng-template>

			<div class="workflow-drawer-content">
				<p class="workflow-drawer-message">
					<span class="workflow-drawer-title">สามารถเลือกไปยังขั้นตอนที่ต้องการได้</span>
					<span class="workflow-drawer-warning">
						* ท่านไม่สามารถแก้ไขรายละเอียดในบางรายการภายหลังได้
					</span>
				</p>

				<div
					*ngFor="let step of mainSteps"
					class="workflow-step-item"
					[class.active]="step.isActive">
					<div
						class="main-step"
						[class.active]="step.isActive"
						[class.completed]="step.isCompleted">
						<div class="step-indicator">
							@if (step.isCompleted) {
								<img class="completion-icon" src="assets/svgs/checkmark-circle.svg" />
							}
							<div class="step-number">{{ step.number }}</div>
						</div>
					</div>
					<div class="substep-title !text-sm !font-light">{{ step.title }}</div>
				</div>
			</div>

			<ng-template #footer>
				<div class="workflow-drawer-footer">
					<p-button
						label="ยกเลิกการสมัคร"
						severity="danger"
						[outlined]="true"
						class="w-full p-4"
						fluid="true"
						(click)="cancelTransaction()" />
					<div class="workflow-drawer-toggle">
						<p-button [rounded]="true" [text]="true" (click)="visible.set(false)">
							<img src="assets/svgs/menu-hide.svg" alt="Menu Hide" />
						</p-button>
						<span>ย่อขนาดเมนู</span>
					</div>
				</div>
			</ng-template>
		</p-drawer>
	`,
})
export class WorkflowStepperComponent implements OnInit, OnDestroy {
	@Input() currentStepId: string | undefined;
	mainSteps: StepItem[] = [];
	activeMainStep: StepItem | undefined | null;

	private stepEffect: ReturnType<typeof effect> | null = null;
	private stateEffect: ReturnType<typeof effect> | null = null;
	visible = signal(false);

	private cdr = inject(ChangeDetectorRef);

	constructor(private workflowService: WorkflowModalService) {
		this.subscribeToWorkflowChanges();
	}

	ngOnInit(): void {
		this.initializeSteps();
	}

	private subscribeToWorkflowChanges(): void {
		this.stepEffect = effect(() => {
			const stepId = this.workflowService.currentStepId();
			console.log("Stepper: currentStepId changed to:", stepId, "previous:", this.currentStepId);

			// Always update if stepId exists, even if it's the same
			if (stepId) {
				this.currentStepId = stepId;
				console.log("Stepper: updating active main step to:", stepId);
				this.updateActiveMainStep();
				this.cdr.detectChanges(); // Force change detection
			}
		});

		this.stateEffect = effect(() => {
			this.workflowService.completedSteps();
			this.updateCompletionStatus();
			this.cdr.detectChanges(); // Force change detection
		});
	}

	ngOnDestroy(): void {
		this.cleanupEffects();
	}

	private cleanupEffects(): void {
		if (this.stepEffect) {
			this.stepEffect.destroy();
			this.stepEffect = null;
		}

		if (this.stateEffect) {
			this.stateEffect.destroy();
			this.stateEffect = null;
		}
	}

	private initializeSteps(): void {
		const workflow = this.workflowService.currentWorkflow();
		if (!workflow) {
			return;
		}

		const mainSteps = workflow.steps;
		this.mainSteps = mainSteps.map((step, index) => this.createMainStepItem(step, index));
		this.activateInitialStep();
		this.updateActiveMainStep();
	}

	private createMainStepItem(step: WorkflowStep, index: number): StepItem {
		const isCompleted = this.isStepCompleted(step);
		const stepItem: StepItem = {
			id: step.stepId,
			number: `${index + 1}`,
			title: step.stepName,
			description: step.stepDescription,
			isActive: this.isStepActive(step),
			isCompleted: isCompleted,
			subSteps: [],
		};

		if (step.children && step.children.length > 0) {
			stepItem.subSteps = this.createSubStepItems(step, index);
			this.checkAndMarkMainStepCompleted(step, stepItem);
		}

		if (this.workflowService.isStepCompleted(step.stepId)) {
			stepItem.isCompleted = true;
		}

		return stepItem;
	}

	private createSubStepItems(step: WorkflowStep, mainIndex: number): SubStepItem[] {
		return step.children!.map((subStep, subIndex) => {
			const isSubStepCompleted = this.isStepCompleted(subStep);

			return {
				id: subStep.stepId,
				number: `${mainIndex + 1}.${subIndex + 1}`,
				title: subStep.stepName,
				description: subStep.stepDescription,
				isActive: this.isStepActive(subStep),
				isCompleted: isSubStepCompleted,
			};
		});
	}

	private checkAndMarkMainStepCompleted(step: WorkflowStep, stepItem: StepItem): void {
		if (!stepItem.subSteps || stepItem.subSteps.length === 0) return;

		const allSubStepsCompleted = stepItem.subSteps.every(
			(subStep: SubStepItem) => subStep.isCompleted,
		);
		if (allSubStepsCompleted && !stepItem.isCompleted) {
			stepItem.isCompleted = true;
			this.workflowService.markStepCompleted(step.stepId);
		}
	}

	private activateInitialStep(): void {
		if (!this.currentStepId) return;

		if (this.currentStepId.includes(".")) {
			const parts = this.currentStepId.split(".");
			const topLevelId = parts[0];

			const topLevelStep = this.mainSteps.find((step) => step.id === topLevelId);
			if (topLevelStep) {
				topLevelStep.isActive = true;
			}
		}
	}

	private updateCompletionStatus(): void {
		this.mainSteps.forEach((step) => {
			const workflowStep = this.workflowService.findStepById(step.id);
			if (workflowStep) {
				step.isCompleted = this.isStepCompleted(workflowStep);
			}

			if (step.subSteps) {
				step.subSteps.forEach((subStep: SubStepItem) => {
					const workflowSubStep = this.workflowService.findStepById(subStep.id);
					if (workflowSubStep) {
						subStep.isCompleted = this.isStepCompleted(workflowSubStep);
					}
				});

				this.checkAndMarkMainStepCompleted({ stepId: step.id } as WorkflowStep, step);
			}
		});
	}

	private updateActiveMainStep(): void {
		console.log("Stepper: updateActiveMainStep called with currentStepId:", this.currentStepId);
		console.log(
			"Stepper: available main steps:",
			this.mainSteps.map((s) => s.id),
		);

		// Reset all steps to inactive first
		this.mainSteps.forEach((step) => {
			step.isActive = false;
		});

		// Find and set the active step based on current step ID
		if (this.currentStepId) {
			const currentStep = this.mainSteps.find((step) => step.id === this.currentStepId);
			console.log("Stepper: found current step:", currentStep?.id);

			if (currentStep) {
				currentStep.isActive = true;
				this.activeMainStep = currentStep;
				console.log("Stepper: set activeMainStep to:", this.activeMainStep?.id);
			} else if (this.currentStepId.includes(".")) {
				// Handle child steps
				const parts = this.currentStepId.split(".");
				const topLevelId = parts[0];
				const topLevelStep = this.mainSteps.find((step) => step.id === topLevelId);
				if (topLevelStep) {
					topLevelStep.isActive = true;
					this.activeMainStep = topLevelStep;
					console.log("Stepper: set activeMainStep to parent:", this.activeMainStep?.id);
				}
			}
		}

		this.updateCompletionStatus();
	}

	private isStepActive(step: WorkflowStep): boolean {
		if (!this.currentStepId) return false;

		const state = this.workflowService.currentState();

		if (state.completedSteps && state.completedSteps.includes(step.stepId)) {
			return false;
		}

		if (step.stepId === this.currentStepId) {
			return true;
		}

		return this.isParentOfActiveStep(step);
	}

	private isParentOfActiveStep(step: WorkflowStep): boolean {
		if (step.children && step.children.length > 0) {
			const isDirectChildActive = step.children.some(
				(child) => child.stepId === this.currentStepId,
			);
			if (isDirectChildActive) {
				return true;
			}

			return step.children.some((child) => this.isStepActive(child));
		}

		return false;
	}

	private isStepCompleted(step: WorkflowStep): boolean {
		if (!this.currentStepId) return false;

		const state = this.workflowService.currentState();
		return state.completedSteps && state.completedSteps.includes(step.stepId);
	}

	/**
	 * Public method to force update the stepper
	 */
	public forceUpdateStepper(): void {
		console.log("Stepper: forceUpdateStepper called");
		const stepId = this.workflowService.currentStepId();
		console.log("Stepper: current step from service:", stepId);
		console.log("Stepper: local currentStepId:", this.currentStepId);

		if (stepId) {
			// Force update local step ID
			this.currentStepId = stepId;
			console.log("Stepper: updated local currentStepId to:", this.currentStepId);

			// Force update active step
			this.updateActiveMainStep();

			// Force change detection
			this.cdr.detectChanges();

			console.log("Stepper: activeMainStep after update:", this.activeMainStep?.id);
		}
	}

	cancelTransaction(): void {
		this.visible.set(false);

		// Delay the dispatch to allow the drawer to close first
		setTimeout(() => {
			this.workflowService.dispatch({
				command: "close",
				fromStepId: this.currentStepId,
			});
		}, 500);
	}
}
