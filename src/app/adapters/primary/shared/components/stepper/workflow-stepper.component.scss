:host {
	@apply block h-full w-full;
}

// Main layout
.workflow-container {
	@apply flex h-full w-[360px] flex-row bg-white;
}

// Left column - navigation
.nav-column {
	@apply flex w-[60px] flex-col border-r;
}

.nav-header {
	@apply flex h-[30px] items-center justify-center border-b;
}

.nav-title {
	@apply text-xs text-gray-500;
}

.nav-content {
	@apply flex flex-1 flex-col items-center space-y-4;
}

.nav-footer {
	@apply flex h-[60px] w-full items-center justify-center border-t border-gray-200;
}

// Main steps
.main-step {
	@apply flex transition-all duration-300;
}

.main-step-title {
	@apply mt-1 w-14 truncate pl-2 pr-1 text-center text-xs font-light text-gray-900;
}

.step-indicator {
	@apply relative mx-auto flex h-8 w-8 items-center justify-center rounded-sm bg-gray-200 text-xs text-gray-500;
}

.main-step.active .step-indicator {
	@apply bg-primary/10 text-primary;
}

.main-step.completed .step-indicator {
	@apply bg-success/20 text-white;
}

.main-step.completed .step-number {
	@apply text-success;
}

.completion-icon {
	@apply absolute right-[-6px] top-[-6px] z-10 h-4 w-4;
}

// Right column - details
.details-column {
	@apply flex w-[300px] flex-col overflow-hidden;
}

.details-header {
	@apply flex h-[30px] w-full flex-row items-center justify-between border-b px-4;
}

.details-title {
	@apply flex flex-row items-center gap-2 text-xs text-gray-500;
}

.details-transaction {
	@apply flex justify-end text-xs text-gray-500;
}

.details-footer {
	@apply flex flex-col items-center justify-center border-t border-gray-200;
}

.details-footer .message-title {
	@apply text-sm font-light text-gray-500;
}

.details-footer .message-value {
	@apply text-sm font-light text-gray-900;
}

.details-footer-content {
	@apply flex w-full flex-col gap-2 p-4;
}

.details-footer-row {
	@apply flex flex-row gap-1;
}

.details-footer-col {
	@apply flex-1;
}

.details-footer-staff {
	@apply flex h-[60px] w-full flex-col items-start justify-center border-t border-gray-200 p-4;
}

// Sub-steps section
.substeps-container {
	@apply flex h-full flex-1 flex-col overflow-y-auto;
}

.substeps-header {
	@apply mt-2 px-4 py-2;
}

.substeps-title {
	@apply text-sm font-semibold text-gray-800;
}

.substeps-content {
	@apply overflow-y-auto px-4 pb-8;
}

// Sub-step items
.substep-item {
	@apply mb-2 flex rounded p-2 transition-colors duration-300;
}

.substep-item.active {
	@apply border border-primary bg-primary/5;
}

.substep-indicator {
	@apply mr-2 flex h-6 min-w-[24px] items-center justify-center rounded-full bg-gray-200 text-xs font-normal text-gray-600;
}

.substep-item.active .substep-indicator {
	@apply bg-primary/10 text-primary;
}

.substep-item.completed .substep-indicator {
	@apply bg-success text-white;
}

// Sub-step content
.substep-content {
	@apply flex-1;
}

.substep-title {
	@apply mb-0.5 text-sm font-light text-gray-800;
}

.substep-title.active {
	@apply font-light text-primary;
}

.substep-description {
	@apply text-xs font-normal text-gray-600;
}

.substep-status {
	@apply mt-1;
}

.active-status {
	@apply rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary;
}

// Empty state
.empty-state {
	@apply flex-1 p-4;
}

.empty-message {
	@apply rounded-md border border-gray-200 bg-gray-50 p-8 text-center;
}

.empty-message p {
	@apply italic text-gray-500;
}

// Drawer styles
.workflow-header {
	@apply flex w-full items-center justify-start border-b px-4 py-2;
}

.workflow-header-title {
	@apply text-xs text-gray-500;
}

.workflow-drawer-content {
	@apply mb-4 flex w-full flex-col overflow-hidden p-4;
}

.workflow-drawer-message {
	@apply mb-4 flex flex-col;
}

.workflow-drawer-title {
	@apply text-sm font-light text-gray-900;
}

.workflow-drawer-warning {
	@apply text-xs text-red-400;
}

.workflow-step-item {
	@apply flex w-full flex-row items-center gap-4 p-4;
}

.workflow-step-item.active {
	@apply bg-primary/5;
}

// Drawer footer
.workflow-drawer-footer {
	@apply flex w-full flex-col items-center justify-start bg-white;
}

.workflow-drawer-toggle {
	@apply flex w-full items-center justify-start border-t border-gray-200 p-2;

	span {
		@apply ml-2 text-base text-gray-900;
	}
}