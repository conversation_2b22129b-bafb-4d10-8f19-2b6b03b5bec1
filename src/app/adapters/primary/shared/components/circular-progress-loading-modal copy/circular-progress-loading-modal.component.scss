.loading-modal-container {
	@apply flex h-full w-full flex-col items-center justify-between bg-white p-6;
}

.loading-modal-content {
	@apply flex h-full w-full max-w-md flex-col items-center;
}

.loading-title {
	@apply mb-12 text-center text-lg font-medium;
}

.circular-progress-container {
	@apply mb-12 flex items-center justify-center;
}

.circular-progress {
	@apply relative flex h-56 w-56 items-center justify-center;
}

.progress-circle {
	@apply h-full w-full -rotate-90 transform;
}

.progress-circle-bg {
	@apply fill-none stroke-gray-200 stroke-[6];
}

.progress-circle-path {
	@apply fill-none stroke-primary stroke-[6];
	stroke-linecap: round;
	stroke-dasharray: 283;
	/* Circumference of circle with r=45 (2πr) */
	transition: stroke-dashoffset 0.3s ease;

	&.indeterminate {
		animation: progress-spinner-dash 1.5s ease-in-out infinite;
		stroke-dasharray: 283, 283;
		stroke-dashoffset: 0;
	}
}

@keyframes progress-spinner-dash {
	0% {
		stroke-dasharray: 1, 283;
		stroke-dashoffset: 0;
	}

	50% {
		stroke-dasharray: 141.5, 283;
		stroke-dashoffset: -141.5;
	}

	100% {
		stroke-dasharray: 283, 283;
		stroke-dashoffset: -283;
	}
}

.progress-icon {
	@apply absolute inset-0 flex items-center justify-center text-primary;

	ion-icon {
		@apply text-5xl;
	}
}

.loading-status {
	@apply mb-4 text-center text-lg font-medium;
}

.loading-description {
	@apply mb-8 whitespace-pre-line text-center text-base font-normal text-gray-500;
}

.loading-actions {
	@apply flex w-full gap-4;
}

/* Custom button styles */
:host ::ng-deep {
	.p-button {
		@apply rounded-md;
	}

	.p-button.p-button-outlined {
		@apply border-gray-300 text-gray-700;

		&:hover {
			@apply border-gray-400 bg-gray-100;
		}
	}

	.p-button:not(.p-button-outlined) {
		@apply border-blue-500 bg-blue-500;

		&:hover {
			@apply border-blue-600 bg-blue-600;
		}
	}
}
