import { CommonModule } from "@angular/common";
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RippleModule } from "primeng/ripple";
import { NumpadService } from "../../services/numpad.service";

@Component({
	selector: "app-numpad-phone",
	templateUrl: "./my-numpad-phone.component.html",
	styleUrls: ["./my-numpad-phone.component.css"],
	providers: [NumpadService],
	standalone: true,
	imports: [CommonModule, FormsModule, ReactiveFormsModule, RippleModule],
})
export class MyNumpadPhoneComponent implements OnInit {
	@Output() numpadChangeEvent = new EventEmitter<string>();
	@Input() message: string = "";
	formGroupLength: number = 0;

	constructor(private numpad: NumpadService) {}

	ngOnInit(): void {
		this.formGroupLength = Object.keys(
			this.numpad.phoneNumberFormGroup.controls,
		).length;
		// console.log("Length of the form group:", this.formGroupLength);
	}

	onButtonClick(digit: string) {
		const emptyDigitControl = this.findFirstEmptyDigitControl();
		if (emptyDigitControl != null) {
			emptyDigitControl.setValue(digit);
			this.emitNumpadChangeEvent();
		}
	}

	onRemoveClick() {
		const lastNonEmptyDigitControl = this.findLastNonEmptyDigitControl();
		if (lastNonEmptyDigitControl != null) {
			lastNonEmptyDigitControl.setValue("");
			this.emitNumpadChangeEvent();
		}
	}

	onClearClick() {
		for (let i = 0; i < this.formGroupLength; i++) {
			this.numpad.phoneNumberFormGroup.get(i.toString())?.setValue("");
		}
		// Emit an empty value
		this.emitNumpadChangeEvent();
	}

	emitNumpadChangeEvent() {
		const valueInArray = Array.from(
			{ length: this.formGroupLength },
			(_, i) => this.numpad.phoneNumberFormGroup.get(i.toString())?.value || "",
		);
		const phoneNumber = valueInArray.join("");
		this.numpadChangeEvent.emit(phoneNumber);
	}

	private findLastNonEmptyDigitControl(): any {
		for (let i = this.formGroupLength - 1; i >= 0; i--) {
			const controlName = i.toString();
			const control = this.numpad.phoneNumberFormGroup.get(controlName);
			if (control?.value !== "") {
				return control;
			}
		}
		return null;
	}

	private findFirstEmptyDigitControl(): any {
		for (let i = 0; i <= this.formGroupLength - 1; i++) {
			const controlName = i.toString();
			const control = this.numpad.phoneNumberFormGroup.get(controlName);
			if (control?.value === "") {
				return control;
			}
		}
		return null;
	}
}
