import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject, signal } from "@angular/core";
import { SetProductRequestDto } from "@domain/dto/products/set-product.dto";
import { ProductList } from "@domain/models/product.model";
import { ModalController } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { SkeletonModule } from "primeng/skeleton";
import { AuthService } from "src/app/core/application/auth.service";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { HeaderMiniComponent } from "../header-mini/header-mini.component";
import { HeaderComponent } from "../header/header.component";
import { ProductDetailsComponent } from "../product-details/product-details.component";

interface CategoryFilter {
	name: string;
	selected: boolean;
}

@Component({
	selector: "app-select-installment-product",
	imports: [
		CommonModule,
		HeaderMiniComponent,
		HeaderComponent,
		ButtonModule,
		IconFieldModule,
		InputIconModule,
		InputTextModule,
		SkeletonModule,
	],
	styleUrl: "./select-installment-product.component.scss",
	template: `
		<div class="flex h-full bg-white">
			<!-- Secondary Sidebar -->
			<div class="sidebar-container hidden lg:block">
				<ng-container>
					<div class="flex flex-1 flex-col overflow-y-auto">
						<!-- Header -->
						<app-header-mini [customHeader]="true">
							<div class="nav-header">
								<img
									class="nav-logo"
									src="assets/svgs/smart-sale-logo.svg"
									alt="SMART SALE" />
								<span class="nav-title">SMART SALE</span>
								<span class="nav-subtitle">v.1.0.0</span>
							</div>
						</app-header-mini>
						<div class="flex-1 overflow-y-auto p-4">
							<p class="page-title-description">ตัวกรองข้อมูล</p>
							<p class="page-title-description !text-base !font-normal">
								เลือกหมวดสินค้าที่ลูกค้าสนใจ
							</p>
							<div class="my-6 flex flex-wrap gap-2">
								@for (category of categoryFilters; track category.name) {
									<div
										class="collection-item"
										(click)="openFilterProduct(category)">
										{{ category.name }}
									</div>
								}
							</div>
							<p class="page-title-description !text-base !font-normal">
								ยี่ห้อที่ลูกค้าสนใจ ({{ selectedBrands.length }} รายการ)
							</p>
							<p
								class="page-title-description !text-sm !font-light !text-gray-500">
								ยังไม่ได้เลือกหมวดสินค้า
							</p>
						</div>
						<div class="flex justify-end gap-2 p-4">
							<p-button
								label="ล้างค่า"
								[outlined]="true"
								class="w-full"
								styleClass="w-full"
								[loading]="isLoading()" />
						</div>
					</div>
				</ng-container>
			</div>

			<div class="main-container">
				<!-- Header -->
				<app-header>
					<div class="flex w-full flex-row items-center justify-between">
						<div class="w-full">
							<p class="page-title !m-0 !text-start">
								เลือกสินค้าที่ท่านต้องการ
							</p>
						</div>
						<div class="flex w-full justify-start">
							<p-iconfield>
								<input
									placeholder="ค้นหาสินค้า"
									type="text"
									class="w-[300px]"
									pInputText />
								<p-inputicon>
									<svg
										width="16"
										height="16"
										viewBox="0 0 16 16"
										fill="none"
										xmlns="http://www.w3.org/2000/svg">
										<path
											d="M6.80006 11.6C5.8507 11.6 4.92266 11.3185 4.13329 10.7911C3.34392 10.2636 2.72869 9.51396 2.36538 8.63688C2.00208 7.75979 1.90702 6.79467 2.09223 5.86357C2.27745 4.93246 2.73461 4.07718 3.40591 3.40589C4.07721 2.7346 4.93249 2.27744 5.86361 2.09223C6.79473 1.90702 7.75986 2.00208 8.63696 2.36538C9.51406 2.72868 10.2637 3.34391 10.7912 4.13326C11.3186 4.92262 11.6001 5.85065 11.6001 6.8C11.5987 8.0726 11.0925 9.29267 10.1926 10.1925C9.29276 11.0924 8.07267 11.5986 6.80006 11.6ZM6.80006 3.2C6.08804 3.2 5.39201 3.41114 4.79998 3.80671C4.20796 4.20228 3.74653 4.76453 3.47405 5.42234C3.20157 6.08015 3.13028 6.80399 3.26919 7.50232C3.4081 8.20066 3.75097 8.84211 4.25444 9.34558C4.75792 9.84905 5.39939 10.1919 6.09772 10.3308C6.79606 10.4697 7.51991 10.3984 8.17773 10.126C8.83556 9.85349 9.39781 9.39207 9.79338 8.80005C10.189 8.20803 10.4001 7.51201 10.4001 6.8C10.3991 5.84551 10.0196 4.93039 9.34462 4.25547C8.66969 3.58055 7.75456 3.20095 6.80006 3.2Z"
											fill="#637D92" />
										<path
											d="M13.4001 14C13.241 14 13.0884 13.9367 12.9759 13.8242L10.5759 11.4242C10.4666 11.311 10.4061 11.1595 10.4075 11.0022C10.4088 10.8448 10.472 10.6944 10.5832 10.5831C10.6944 10.4719 10.8449 10.4088 11.0023 10.4074C11.1596 10.406 11.3111 10.4665 11.4243 10.5758L13.8243 12.9758C13.9082 13.0597 13.9653 13.1666 13.9885 13.283C14.0116 13.3994 13.9997 13.52 13.9543 13.6296C13.9089 13.7392 13.832 13.8329 13.7334 13.8988C13.6348 13.9648 13.5188 14 13.4001 14Z"
											fill="#637D92" />
									</svg>
								</p-inputicon>
							</p-iconfield>
						</div>
						<div class="flex w-full justify-end">
							<p-button [rounded]="true" [text]="true" (click)="cancel()">
								<svg
									width="32"
									height="32"
									viewBox="0 0 32 32"
									fill="none"
									xmlns="http://www.w3.org/2000/svg">
									<path
										d="M17.9018 16.0157L24.9591 8.9584C25.0865 8.8354 25.1881 8.68828 25.2579 8.52561C25.3278 8.36293 25.3646 8.18797 25.3661 8.01094C25.3677 7.8339 25.3339 7.65832 25.2669 7.49446C25.1999 7.3306 25.1008 7.18173 24.9757 7.05654C24.8505 6.93135 24.7016 6.83235 24.5377 6.7653C24.3739 6.69826 24.1983 6.66453 24.0213 6.66607C23.8442 6.6676 23.6693 6.70439 23.5066 6.77426C23.3439 6.84414 23.1968 6.94572 23.0738 7.07307L16.0165 14.1304L8.95913 7.07307C8.70766 6.83019 8.37086 6.6958 8.02126 6.69883C7.67167 6.70187 7.33725 6.8421 7.09004 7.08931C6.84283 7.33652 6.7026 7.67094 6.69957 8.02053C6.69653 8.37013 6.83092 8.70693 7.0738 8.9584L14.1311 16.0157L7.0738 23.0731C6.94645 23.1961 6.84488 23.3432 6.775 23.5059C6.70512 23.6685 6.66834 23.8435 6.6668 24.0205C6.66526 24.1976 6.699 24.3731 6.76604 24.537C6.83308 24.7009 6.93208 24.8497 7.05727 24.9749C7.18246 25.1001 7.33133 25.1991 7.49519 25.2662C7.65906 25.3332 7.83463 25.3669 8.01167 25.3654C8.18871 25.3639 8.36367 25.3271 8.52634 25.2572C8.68901 25.1873 8.83614 25.0857 8.95913 24.9584L16.0165 17.9011L23.0738 24.9584C23.3253 25.2013 23.6621 25.3357 24.0117 25.3326C24.3613 25.3296 24.6957 25.1894 24.9429 24.9422C25.1901 24.6949 25.3303 24.3605 25.3334 24.0109C25.3364 23.6613 25.202 23.3245 24.9591 23.0731L17.9018 16.0157Z"
										fill="#858585" />
								</svg>
							</p-button>
						</div>
					</div>
				</app-header>

				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper !max-w-full">
						<!-- Product Grid -->
						@if (!isLoading()) {
							@if (filteredProducts.length > 0) {
								<!-- Consent Title -->
								<div class="section-header mb-4">
									<h2 class="section-title-text">โทรศัพท์มือถือแท็บเล็ต</h2>
								</div>

								<div class="product-list">
									@for (
										product of filteredProducts;
										track product.productModelCode
									) {
										<div class="product-item">
											<div class="product-item-details">
												<img
													[src]="product.productImage"
													[alt]="product.productModelName"
													class="product-item-image-container" />
												<p
													class="product-item-name"
													[title]="product.productModelName">
													{{ product.productModelName }}
												</p>
											</div>
											<div class="product-item-actions">
												<p class="summary-price">
													{{
														product.productPrice
															| currency: "ราคา " : "symbol" : "1.0-0"
													}}
													บาท
												</p>
												<div class="product-item-actions-buttons">
													<p-button
														label="ดูรายละเอียด"
														[outlined]="true"
														[fluid]="true"
														[loading]="isLoading()"
														(click)="openProductDetails(product)" />
													<p-button
														label="เลือกสินค้า"
														[fluid]="true"
														[loading]="isLoading()"
														(click)="selectProduct(product)" />
												</div>
											</div>
										</div>
									}
								</div>
							} @else {
								<div class="py-10 text-center text-gray-500">
									<p>ไม่พบสินค้าที่ตรงกับเงื่อนไขการค้นหา</p>
								</div>
							}
						} @else {
							<div class="py-10 text-center">
								<p class="mt-2 text-gray-500">กำลังโหลดข้อมูลสินค้า...</p>
							</div>
						}
					</div>
				</div>
			</div>
		</div>
	`,
})
export class SelectInstallmentProductComponent {
	private starmoneyService = inject(StarmoneyService);
	private authService = inject(AuthService);
	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);

	isLoading = signal(false);

	products: ProductList[] = [];
	filteredProducts: ProductList[] = [];
	brandFilters: string[] = [];
	selectedBrands: string[] = [];
	productType: string = "U1";
	categoryFilters: CategoryFilter[] = [
		{ name: "โทรศัพท์มือถือ / แท็บเล็ต", selected: false },
		{ name: "คอมพิวเตอร์ / โน้ตบุ๊ก", selected: false },
		{ name: "เครื่องใช้ไฟฟ้าขนาดเล็ก", selected: false },
	];

	ngOnInit(): void {
		this.loadProducts();
		this.applyFilters();
	}

	async loadProducts(): Promise<void> {
		try {
			const txnId = this.authService.getTxnId();
			if (!txnId) {
				throw new Error("Transaction ID is undefined");
			}

			const response = await this.starmoneyService.getProductList({
				txn_id: txnId,
				product_type: this.productType,
			});
			if (response.isSuccess()) {
				this.products = response.data;
				this.filteredProducts = response.data;
			}
		} catch (error) {
			await this.handleError(error);
		}
	}

	applyFilters(): void {
		this.filteredProducts = this.products.filter((product) => {
			// Filter by Category
			// const selectedCategories = this.categoryFilters.filter(cat => cat.selected).map(cat => cat.name);
			// const categoryMatch = selectedCategories.length === 0 || selectedCategories.includes(product.category);
			// // Filter by Brand
			// const brandMatch = this.selectedBrands.length === 0 || (product.brand && this.selectedBrands.includes(product.brand));
			// // Filter by Search Term (case-insensitive)
			// const searchMatch = !this.searchTerm || product.name.toLowerCase().includes(this.searchTerm.toLowerCase());
			// return categoryMatch && brandMatch && searchMatch;
		});
	}

	openFilterProduct(categoryFilters: any) {
		// const filterProductDialogRef = this.dialogService.open(
		// 	FilterProductComponent,
		// 	{
		// 		width: DIALOG_CONSTANTS.DIMENSIONS.GLOBAL.WIDTH,
		// 		height: DIALOG_CONSTANTS.DIMENSIONS.GLOBAL.HEIGHT,
		// 		baseZIndex: DIALOG_CONSTANTS.Z_INDEX.SUB,
		// 		styleClass: DIALOG_CONSTANTS.STYLE_CLASSES.GLOBAL,
		// 		contentStyle: {
		// 			overflow: "hidden",
		// 			padding: "0px",
		// 		},
		// 		focusOnShow: false,
		// 		showHeader: false,
		// 		modal: true,
		// 		closable: false,
		// 		draggable: false,
		// 		appendTo: "body",
		// 		data: { categoryFilters },
		// 	},
		// );
		// filterProductDialogRef.onClose.subscribe(async (data) => {
		// 	this.appUtils.showInfo("รีเฟรชตัวกรอง");
		// });
	}

	async openProductDetails(product: ProductList) {
		const modal = await this.modalController.create({
			component: ProductDetailsComponent,
			cssClass: "global-dialog-modal modal-blur-backdrop",
			componentProps: {
				productList: product,
				canChoose: true,
			},
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		if (data && data.isSelected) {
			await this.selectProduct(data.productList);
		} else {
			console.warn("ไม่ได้เลือกสินค้า");
		}
	}

	async selectProduct(product: ProductList): Promise<void> {
		try {
			const txnId = this.authService.getTxnId();
			if (!txnId) {
				throw new Error("Transaction ID is undefined");
			}

			const request: SetProductRequestDto = {
				txn_id: txnId,
				brand_code: product.productBrandCode,
				color_code: product.productColorCode,
				model_code: product.productModelCode,
				product_type: product.productType,
			};

			const apiResponse = await this.starmoneyService.setProduct(request);
			if (apiResponse.isSuccess()) {
				await this.appUtilsService.showSuccess("บันทึกสินค้าเรียบร้อย");
				await this.modalController.dismiss({ refresh: true });
			}
		} catch (error) {
			await this.handleError(error);
		}
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}

	async cancel(): Promise<void> {
		await this.modalController.dismiss({ refresh: false });
	}
}
