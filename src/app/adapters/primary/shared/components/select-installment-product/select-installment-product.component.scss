/* Header Section */

.sidebar-container {
  @apply min-h-screen w-[300px] border-r border-gray-300 bg-white;
  @apply flex flex-col;
}

.nav-header {
  @apply flex h-[30px] flex-row items-center justify-start gap-2 border-b border-gray-100 px-4;
}

.nav-logo {
  @apply h-4 w-4;
}

.nav-title {
  @apply text-xs text-gray-900;
}

.nav-subtitle {
  @apply text-xs text-gray-500;
}

.collection-item {
  @apply flex items-center justify-center p-2;
  @apply rounded-md border border-primary text-primary;
  @apply cursor-pointer text-xs;

  .active {
    @apply bg-primary/10 text-white;
  }
}

/* Menu Container */
.menu-item-wrapper {
  @apply flex flex-col gap-2 overflow-y-auto p-4;
}

.main-container {
  @apply flex flex-1 flex-col overflow-hidden;
}

.section-header {
  @apply bg-primary/10 p-3;
}

.section-title-text {
  @apply text-primary_dark text-lg font-medium;
}

.product-list {
  @apply grid grid-cols-2 gap-4 lg:grid-cols-3;
}

.product-item {
  @apply flex min-h-[300px] w-full flex-col items-center justify-between p-4;
  @apply rounded-lg border border-gray-200;

  .product-item-details {
    @apply flex w-full flex-col items-center justify-center gap-4;

    .product-item-image-container {
      @apply max-h-[140px] max-w-max object-contain;
    }

    .product-item-name {
      @apply w-full text-pretty text-start text-base font-normal text-gray-900;
    }
  }
}

.product-item-actions {
  @apply mt-8 flex w-full flex-col items-center justify-center gap-2;

  .summary-price {
    @apply mb-2 w-full text-start text-lg font-medium text-gray-900;
  }

  .product-item-actions-buttons {
    @apply flex w-full flex-row gap-2;

    p-button {
      @apply w-full;
    }
  }
}