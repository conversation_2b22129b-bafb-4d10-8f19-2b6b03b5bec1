import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject, OnInit } from "@angular/core";
import { Contact, SendOtp } from "@domain/models/contact.model";
import { <PERSON>dal<PERSON>ontroller } from "@ionic/angular/standalone";
import { ButtonModule } from "primeng/button";
import { InputOtpModule } from "primeng/inputotp";
import { map, Subscription, takeWhile, timer } from "rxjs";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { WorkflowStepBaseComponent } from "../../../workflow/workflow-step-base.component";

@Component({
	standalone: true,
	imports: [CommonModule, ButtonModule, InputOtpModule],
	selector: "app-otp-phone-verify",
	styleUrls: ["./otp-phone-verify.component.scss"],
	template: `
		<div class="app-layout-dialog">
			<!-- Content -->
			<div class="app-content">
				<div class="content-wrapper">
					<!-- Header Section -->
					<div class="text-center">
						<p class="page-title-description-center mb-6">ยืนยันรหัส OTP เบอร์โทรศัพท์</p>
						<p class="page-description-center">
							<span>กรุณายืนยันรหัส OTP</span>
							<span>ที่ส่งไปยังหมายเลข {{ contactInfo?.phone }}</span>
							<span>
								รหัสอ้างอิง:
								<span class="text-primary">{{ sendOtpInfo?.refCode }}</span>
							</span>
						</p>
					</div>

					<div class="my-8 flex w-full flex-col items-start justify-center px-8">
						<p class="mb-1 text-start text-sm">ระบุรหัส OTP</p>
						<p-inputotp
							[length]="6"
							size="large"
							[integerOnly]="true"
							(onChange)="handleChange($event)" />
					</div>

					<p class="page-description-center">
						<span>
							ขอรหัสใหม่ หรือเปลี่ยนเบอร์ได้ในอีก:
							<span class="text-primary">{{ countdown }}</span>
						</span>
					</p>

					<div class="mt-8 flex flex-1 items-center justify-center gap-4">
						<p-button
							label="ขอรหัสใหม่"
							[fluid]="true"
							[outlined]="true"
							[disabled]="isTimeout()"
							(click)="resendOTP()" />
						<p-button
							label="เปลี่ยนเบอร์โทรศัพท์"
							[fluid]="true"
							[outlined]="true"
							[disabled]="isTimeout()"
							(click)="closeModal()" />
					</div>
				</div>
			</div>
		</div>
	`,
})
export class OtpPhoneVerifyComponent extends WorkflowStepBaseComponent implements OnInit {
	private starmoneyService = inject(StarmoneyService);

	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);
	afterInfoViaInternet: boolean = false;
	contactInfo: Contact | null = null;
	sendOtpInfo: SendOtp | null = null;
	otp: string = "";

	async ngOnInit(): Promise<void> {
		await this.fetchContact();
	}

	/**
	 * Fetches the contact information
	 */
	async fetchContact() {
		await this.runWithLoading(async () => {
			try {
				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.getContact({
					txn_id: txnId,
				});

				if (apiResponse.isSuccess() && apiResponse.hasData()) {
					this.contactInfo = apiResponse.data;
					// then send otp
					await this.sendOTP();
				} else {
					throw new Error(apiResponse.message || "ไม่สามารถดึงข้อมูลติดต่อได้");
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	/**
	 * Sends the OTP
	 */
	async sendOTP() {
		await this.runWithLoading(async () => {
			try {
				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.sendOtp({
					txn_id: txnId,
					recipient_type: "phone",
					type: "confirm_otp",
				});

				if (apiResponse.isSuccess() && apiResponse.hasData()) {
					this.sendOtpInfo = apiResponse.data;
					const timestamp = new Date(this.sendOtpInfo.otpExpiresAt || 0).getTime();
					this.startCountdown(timestamp);
				} else {
					throw new Error(apiResponse.message || "ไม่สามารถส่ง OTP ได้");
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	async resendOTP() {
		await this.sendOTP();
	}

	handleChange(event: any) {
		console.log(event);
		if (event.value.length === 6) {
			this.otp = event.value;
			setTimeout(async () => {
				await this.appUtilsService.showSuccess("ยืนยันรหัส OTP สำเร็จ");
				if (this.afterInfoViaInternet) {
					this.handleAfterInfoViaInternet();
				} else {
					await this.verifyOTP();
				}
			}, 1000);
		}
	}

	/**
	 * Verifies the OTP
	 */
	async verifyOTP() {
		await this.runWithLoading(async () => {
			try {
				if (!this.sendOtpInfo?.refCode || this.otp.length !== 6) {
					throw new Error("ไม่สามารถยืนยัน OTP ได้ refCode ไม่ถูกต้อง หรือ หรือ OTP ไม่ถูกต้อง");
				}

				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.verifyOtp({
					txn_id: txnId,
					recipient_type: "phone",
					type: "confirm_otp",
					otp: this.otp,
					ref_code: this.sendOtpInfo?.refCode,
				});

				if (apiResponse.isSuccess() && apiResponse.isTrue()) {
					await this.modalController.dismiss({ success: true });
				} else {
					throw new Error(apiResponse.message || "ไม่สามารถยืนยัน OTP ได้");
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	async handleAfterInfoViaInternet() {
		await this.runWithLoading(async () => {
			try {
				if (!this.sendOtpInfo?.refCode || this.otp.length !== 6) {
					throw new Error("ไม่สามารถยืนยัน OTP ได้ refCode ไม่ถูกต้อง หรือ หรือ OTP ไม่ถูกต้อง");
				}

				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.verifyOtp({
					txn_id: txnId,
					recipient_type: "phone",
					type: "confirm_otp",
					otp: this.otp,
					ref_code: this.sendOtpInfo?.refCode,
				});

				if (apiResponse.isSuccess() && apiResponse.isTrue()) {
					await this.modalController.dismiss({ success: true });
				} else {
					throw new Error(apiResponse.message || "ไม่สามารถยืนยัน OTP ได้");
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	async closeModal() {
		await this.modalController.dismiss();
	}

	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;
		let subMessage = "";

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiMessage = error.error?.message;
			const apiError = error.error?.error;

			errorMessage = apiError || error.statusText || errorMessage;
			subMessage = apiMessage || "";

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			subMessage: subMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => {},
		});

		return false;
	}

	/**
	 * ======================================================
	 *  Countdown
	 * ======================================================
	 */

	public countdown: string = "00:00";
	private minutes: number = 0;
	private seconds: number = 0;
	private subscription: Subscription | null = null;

	public startCountdown(timestamp: number): void {
		const now = new Date().getTime();
		const diff = timestamp - now;

		if (isNaN(diff) || diff <= 0) {
			this.countdown = "00:00";
			console.warn("⏳ OTP หมดอายุหรือ timestamp ผิดพลาด");
			return;
		}

		this.minutes = Math.floor(diff / 60000);
		this.seconds = Math.floor((diff % 60000) / 1000);

		this.subscription = timer(0, 1000)
			.pipe(
				takeWhile(() => this.minutes > 0 || this.seconds > 0),
				map(() => {
					if (this.seconds === 0) {
						if (this.minutes === 0) {
							this.clearInterval();
						} else {
							this.minutes--;
							this.seconds = 59;
						}
					} else {
						this.seconds--;
					}
					return this.formatTime(this.minutes, this.seconds);
				}),
			)
			.subscribe((time) => {
				this.countdown = time;
			});
	}

	clearInterval(): void {
		if (this.subscription) {
			this.subscription.unsubscribe();
			this.subscription = null;
			this.countdown = "00:00";
		}
	}

	private formatTime(minutes: number, seconds: number): string {
		return `${this.padZero(minutes)}:${this.padZero(seconds)}`;
	}

	private padZero(value: number): string {
		return value < 10 ? `0${value}` : `${value}`;
	}

	public isTimeout() {
		return this.minutes > 0 || this.seconds > 0;
	}
}
