import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject, Input } from "@angular/core";
import { LoanType } from "@domain/models/loan.model";
import { Product } from "@domain/models/product.model";
import { ModalController } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { SelectModule } from "primeng/select";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { WorkflowStepBaseComponent } from "../../../workflow/workflow-step-base.component";
import { FooterComponent } from "../footer/footer.component";
import {
	DownPaymentOption,
	InstallmentPlanOption,
} from "../installment-plan-info/installment-plan-info.component";

@Component({
	selector: "app-installment-summary",
	imports: [CommonModule, FooterComponent, ButtonModule, SelectModule],
	styleUrl: "./installment-summary.component.css",
	template: `
		<div class="app-layout-dialog">
			<!-- Content -->
			<div class="app-content">
				<div class="content-wrapper !max-w-full">
					<p class="page-title mb-6 !text-lg !font-medium">
						รายละเอียดการผ่อนชำระ
					</p>
					<div class="installment-section">
						<div class="installment-section-half">
							<p class="section-title">สินค้าที่ลูกค้าเลือกผ่อนชำระ</p>
							<div class="mx-auto mb-2 w-full max-w-[200px]">
								<img [src]="productInfo?.image" alt="person-with-product" />
							</div>
							<p class="section-title">รายละเอียดสินค้า</p>
							<p class="detail-row">
								<span class="detail-label">สินค้าที่เลือก</span>
								<span class="detail-value">
									{{ productInfo?.modelName || "" }}
								</span>
							</p>
							<p class="detail-row">
								<span class="detail-label">ราคาสินค้า</span>
								<span class="detail-value">
									{{ productInfo?.price || 0 | number }} บาท
								</span>
							</p>
							<p class="detail-row">
								<span class="detail-label">ส่วนลด</span>
								<span class="detail-value">
									{{ productInfo?.discount || 0 | number }} บาท
								</span>
							</p>
							<p class="detail-row">
								<span class="detail-label-bold">ราคาสุทธิ:</span>
								<span class="detail-value-bold">
									{{ productInfo?.netPrice || 0 | number }} บาท
								</span>
							</p>
						</div>

						<div class="installment-section-half">
							<h2 class="section-title">ข้อมูลสำหรับเจ้าหน้าที่</h2>

							<div class="detail-row">
								<p class="detail-label">เจ้าหน้าที่ผู้ทำรายการ</p>
								<p class="detail-value flex flex-col gap-2">
									<span>คุณสตาร์ ทดสอบระบบ</span>
									<span>ID: 0000001</span>
								</p>
							</div>

							<div class="detail-row">
								<p class="detail-label">วันที่ทำรายการ</p>
								<p class="detail-value flex flex-col gap-2">
									<span>1 เมษายน 2568</span>
									<span>12:34</span>
								</p>
							</div>

							<div class="detail-row">
								<p class="detail-label">นโยบายการขาย</p>
								<p class="detail-value flex flex-col gap-2">
									<span>ขายสินค้าหมวด U</span>
									<span>ทุกชนิดทุกช่องทางขาย</span>
								</p>
							</div>

							<h2 class="section-title mt-3">
								เงื่อนไขการผ่อนชำระที่ลูกค้าเลือก
							</h2>

							<p class="detail-row">
								<span class="detail-label-bold">ดาวน์:</span>
								<span class="detail-value-bold">
									({{ downPayment?.percent || "-" }}%)
									{{ downPayment?.price || 0 | number }} บาท
								</span>
							</p>

							<p class="detail-row">
								<span class="detail-label-bold">ดอกเบี้ย:</span>
								<span class="detail-value-bold">
									{{ productInfo?.interestRate || 0 | number }}% ต่อเดือน
								</span>
							</p>

							<p class="detail-row">
								<span class="detail-label-bold">ผ่อนต่องวด:</span>
								<span class="detail-value-bold">
									{{ installmentPlan?.price || 0 | number }} บาท
								</span>
							</p>

							<p class="detail-row">
								<span class="detail-label-bold">จำนวนงวด:</span>
								<span class="detail-value-bold">
									{{ installmentPlan?.period || 0 | number }} งวด
								</span>
							</p>

							<p class="disclaimer-text">
								<span>
									* กรุณาแจ้งให้ลูกค้าทราบ
									ข้อมูลดังกล่าวเป็นการประเมินเบื้องต้นจากระบบ
								</span>
								<span>อาจมีการเปลี่ยนแปลงตามผลการพิจารณาสินเชื่อ</span>
							</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Footer -->
			<app-footer>
				<div class="footer-wrapper !justify-between">
					<p-button
						label="ย้อนกลับ"
						styleClass="w-full"
						[outlined]="true"
						(onClick)="cancel()" />
					<p-button
						label="ยืนยันข้อมูล"
						styleClass="w-full"
						[loading]="isLoading()"
						(onClick)="submit()" />
				</div>
			</app-footer>
		</div>
	`,
})
export class InstallmentSummaryComponent extends WorkflowStepBaseComponent {
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);

	@Input() loanInfo: LoanType | null = null;
	@Input() productInfo: Product | null = null;
	@Input() downPayment: DownPaymentOption | null = null;
	@Input() installmentPlan: InstallmentPlanOption | null = null;

	async submit(): Promise<void> {
		if (this.loanInfo?.isHirePurchase()) {
			return this.confirmProduct();
		} else if (this.loanInfo?.isPersonalLoan()) {
			// return this.confirmPersonalLoan();
		} else {
			throw new Error("ประเภทสินเชื่อไม่ถูกต้อง");
		}
	}

	async confirmProduct(): Promise<void> {
		try {
			if (!this.downPayment || !this.installmentPlan) {
				throw new Error("กรุณาเลือกดาวน์และจำนวนงวด");
			}

			const txnId = this.getTxnIdOrError();
			const response = await this.starmoneyService.confirmProduct({
				txn_id: txnId,
				down: this.downPayment?.percent || 0,
				period: this.installmentPlan?.period || 0,
				// ...(this.productInfo?.promotion_code && {
				//   promotion_code: this.product.promotion_code,
				// }),
			});

			if (response.isSuccess()) {
				await this.modalController.dismiss({
					downPayment: this.downPayment,
					installmentPlan: this.installmentPlan,
					productInfo: this.productInfo,
					loanInfo: this.loanInfo,
					refresh: true,
				});
			}
		} catch (error) {
			await this.handleError(error);
		}
	}

	async cancel(): Promise<void> {
		await this.modalController.dismiss({
			downPayment: this.downPayment,
			installmentPlan: this.installmentPlan,
			refresh: false,
		});
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
