import { CommonModule } from "@angular/common";
import { Component, Input } from "@angular/core";
import { IonicModule } from "@ionic/angular";
import { Modal<PERSON>ontroller } from "@ionic/angular/standalone";
import { addIcons } from "ionicons";
import {
	alertCircleOutline,
	checkmarkCircleOutline,
	closeCircleOutline,
	informationCircleOutline,
} from "ionicons/icons";
import { ButtonModule } from "primeng/button";

export enum StatusType {
	SUCCESS = "success",
	WARNING = "warning",
	ERROR = "error",
	INFO = "info",
}

@Component({
	selector: "app-status-notification-modal",
	standalone: true,
	imports: [CommonModule, IonicModule, ButtonModule],
	template: `
		<div class="status-notification-container">
			<div class="status-notification-content">
				<!-- Header -->
				<h1 class="status-title">{{ title }}</h1>

				<!-- Status Icon -->
				<div class="status-icon-container" [ngClass]="statusClass">
					<div class="status-icon">
						@if (statusType === "success") {
							<img src="assets/svgs/success.svg" class="h-40 w-40" />
						}
						@if (statusType === "warning") {
							<img src="assets/svgs/warning.svg" class="h-40 w-40" />
						}
						@if (statusType === "error") {
							<img src="assets/svgs/error.svg" class="h-40 w-40" />
						}
						<ion-icon *ngIf="statusType === 'info'" name="information-circle-outline"></ion-icon>
					</div>
				</div>

				<!-- Main Message -->
				<h2 class="status-message">{{ message }}</h2>

				<!-- Description -->
				<div class="status-description" [innerHTML]="description"></div>

				<!-- Error Code (if provided) -->
				<div *ngIf="errorCode" class="status-error-code">
					<span class="error-label">{{ errorLabel }}</span>
					<span class="error-code">{{ errorCode }}</span>
				</div>

				<!-- Action Buttons -->
				<div class="status-actions">
					<p-button
						*ngIf="secondaryButtonLabel"
						[label]="secondaryButtonLabel"
						styleClass="w-full"
						[outlined]="true"
						(onClick)="onSecondaryClick()"></p-button>

					<p-button
						[label]="primaryButtonLabel"
						styleClass="w-full"
						(onClick)="onPrimaryClick()"></p-button>
				</div>
			</div>
		</div>
	`,
	styleUrls: ["./status-notification-modal.component.scss"],
})
export class StatusNotificationModalComponent {
	@Input() title: string = "";
	@Input() statusType: StatusType = StatusType.SUCCESS;
	@Input() message: string = "";
	@Input() description: string = "";
	@Input() errorCode: string = "";
	@Input() errorLabel: string = "Code:";
	@Input() primaryButtonLabel: string = "ตกลง";
	@Input() secondaryButtonLabel: string = "";
	@Input() onPrimaryAction: (() => void) | null = null;
	@Input() onSecondaryAction: (() => void) | null = null;

	constructor(private modalController: ModalController) {
		addIcons({
			checkmarkCircleOutline,
			alertCircleOutline,
			closeCircleOutline,
			informationCircleOutline,
		});
	}

	get statusClass(): string {
		return `status-${this.statusType}`;
	}

	onPrimaryClick() {
		if (this.onPrimaryAction) {
			this.onPrimaryAction();
		} else {
			this.modalController.dismiss({ action: "primary" });
		}
	}

	onSecondaryClick() {
		if (this.onSecondaryAction) {
			this.onSecondaryAction();
		} else {
			this.modalController.dismiss({ action: "secondary" });
		}
	}
}
