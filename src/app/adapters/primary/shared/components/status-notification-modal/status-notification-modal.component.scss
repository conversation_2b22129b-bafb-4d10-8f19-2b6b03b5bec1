.status-notification-container {
	@apply flex h-full w-full flex-col items-center justify-center bg-white p-6;
}

.status-notification-content {
	@apply flex h-full w-full max-w-md flex-col items-center justify-between;
}

.status-title {
	@apply mb-8 text-center text-lg font-medium;
}

.status-icon-container {
	@apply mb-8 flex h-32 w-32 items-center justify-center rounded-full;

	&.status-success {
		@apply bg-green-500;
	}

	&.status-warning {
		@apply bg-yellow-400;
	}

	&.status-error {
		@apply bg-red-500;
	}

	&.status-info {
		@apply bg-blue-500;
	}
}

.status-icon {
	@apply text-white;

	ion-icon {
		@apply text-7xl;
	}
}

.status-message {
	@apply mb-4 text-center text-lg font-medium;
}

.status-description {
	@apply mb-8 whitespace-pre-line text-center text-base font-normal text-gray-500;
}

.status-error-code {
	@apply mb-8 flex flex-col items-center;

	.error-label {
		@apply text-base text-gray-500;
	}

	.error-code {
		@apply font-medium text-gray-500;
	}
}

.status-actions {
	@apply flex w-full justify-center gap-4;

	p-button {
		@apply w-full;
	}
}

/* Custom button styles */
:host ::ng-deep {
	.p-button {
		@apply rounded-md;
	}

	.p-button.p-button-outlined {
		@apply border-gray-200 text-gray-700;

		&:hover {
			@apply border-gray-400 bg-gray-100;
		}
	}

	.p-button:not(.p-button-outlined) {
		@apply border-blue-500 bg-blue-500;

		&:hover {
			@apply border-blue-600 bg-blue-600;
		}
	}
}