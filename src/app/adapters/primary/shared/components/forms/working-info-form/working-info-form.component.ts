import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, Input, OnInit, inject } from "@angular/core";
import {
	FormBuilder,
	FormGroup,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { UpdateWorkingInfoRequestDto } from "@domain/dto/working/update-working-info.dto";
import {
	Occupation,
	OccupationGroup,
	WorkingInfo,
} from "@domain/models/working.model";
import { IonicModule, ModalController } from "@ionic/angular";
import dayjs from "dayjs";
import { ButtonModule } from "primeng/button";
import { CalendarModule } from "primeng/calendar";
import { InputTextModule } from "primeng/inputtext";
import { RadioButtonModule } from "primeng/radiobutton";
import { SelectModule } from "primeng/select";
import {
	Subject,
	distinctUntilChanged,
	merge,
	startWith,
	takeUntil,
} from "rxjs";
import { WorkflowStepBaseComponent } from "src/app/adapters/primary/workflow/workflow-step-base.component";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { DateUtils } from "src/app/core/utils/date-utils";
import { MessageErrorUtils } from "src/app/core/utils/message-error-utils";
import { FooterComponent } from "../../footer/footer.component";

@Component({
	selector: "app-working-info-form",
	standalone: true,
	imports: [
		CommonModule,
		ReactiveFormsModule,
		IonicModule,
		ButtonModule,
		InputTextModule,
		SelectModule,
		CalendarModule,
		RadioButtonModule,
		FooterComponent,
	],
	templateUrl: "./working-info-form.component.html",
	styleUrls: ["./working-info-form.component.scss"],
})
export class WorkingInfoFormComponent
	extends WorkflowStepBaseComponent
	implements OnInit
{
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);
	private fb = inject(FormBuilder);
	private destroy$ = new Subject<void>();

	public FORM_TITLE_WORK_INFORMATION = "ข้อมูลการทำงาน";
	public FORM_TITLE_INCOME_EXPENSES = "ข้อมูลรายรับรายจ่าย";

	public form: FormGroup = new FormGroup({});
	public occupations: Occupation[] = [];
	public occupationGroups: OccupationGroup[] = [];
	public dateOptions: any;
	public monthOptions: any;
	public yearOptions: any;

	@Input() workingInfo: WorkingInfo | null = null;
	@Input() item: any;

	get formTitle(): string {
		return this.item.title;
	}

	get isFormValid(): boolean {
		return this.form && this.form.valid;
	}

	// Validation helpers
	isFieldInvalid(fieldName: string): boolean {
		const field = this.form.get(fieldName);
		return field ? !field.valid && (field.dirty || field.touched) : false;
	}

	shouldShowError(fieldName: string, errorType: string): boolean {
		const field = this.form.get(fieldName);
		return field
			? field.errors?.[errorType] && (field.dirty || field.touched)
			: false;
	}

	async ngOnInit() {
		// Initialize form
		console.log("Working Info Form Item:", this.item);
		console.log("Working Info Form Working Info:", this.workingInfo);
		if (this.formTitle === this.FORM_TITLE_WORK_INFORMATION) {
			this.initWorkForm();
			await this.getOccupationGroup();
			this._subscribeFormChanges();
			await this.setWorkingFormValue();
		} else if (this.formTitle === this.FORM_TITLE_INCOME_EXPENSES) {
			this.initIncomeForm();
			this._subscribeFormChanges();
		}
	}

	ngOnDestroy(): void {
		this.destroy$.next();
		this.destroy$.complete();
	}

	async initWorkForm() {
		this.form = this.fb.group({
			occupationGroup: ["", [Validators.required]],
			occupation: ["", [Validators.required]],
			position: [this.workingInfo?.position, [Validators.required]],
			startDate: ["", [Validators.required]],
			startMonth: ["", [Validators.required]],
			startYear: ["", [Validators.required]],
			workExperience: [{ value: "", disabled: true }, [Validators.required]],
		});

		this.yearOptions = DateUtils.getYearOptions();
		this.monthOptions = DateUtils.getMonthOptions();
		this.updateDateOptions("startMonth", "startYear");
	}

	private initIncomeForm() {
		this.form = this.fb.group({
			monthlyIncome: [
				this.workingInfo?.monthlyIncome || null,
				[Validators.required],
			],
			additionalIncome: [
				this.workingInfo?.additionalIncome || null,
				[Validators.required],
			],
			monthlyExpense: [
				this.workingInfo?.monthlyExpense || null,
				[Validators.required],
			],
			incomeSlip: [
				typeof this.workingInfo?.incomeSlip === "boolean"
					? this.workingInfo?.incomeSlip
					: null,
				[Validators.required],
			],
		});
	}

	async getOccupationGroup() {
		try {
			const txnId = this.getTxnIdOrError();
			const apiResponse = await this.starmoneyService.getOccupationGroups({
				txn_id: txnId,
			});
			if (apiResponse.isSuccess()) {
				this.occupationGroups = apiResponse.data;
				console.log("Occupation Groups:", this.occupationGroups);
			}
		} catch (error) {
			await this.handleError(error);
		}
	}

	async getOccupation() {
		if (this.form.get("occupationGroup")?.dirty) {
			this.form.get("occupation")?.reset("", { emitEvent: false });
		}
		if (this.form.get("occupationGroup")?.value) {
			try {
				const txnId = this.getTxnIdOrError();
				const response = await this.starmoneyService.getOccupations({
					txn_id: txnId,
					occupationGroupCode: this.form.get("occupationGroup")?.value.code,
				});
				if (response.isSuccess()) {
					this.occupations = response.data;
					console.log("Occupations:", this.occupations);
				}
			} catch (error) {
				await this.handleError(error);
			}
		}
	}

	private _subscribeFormChanges(): void {
		if (this.formTitle === this.FORM_TITLE_WORK_INFORMATION) {
			this.form
				.get("occupationGroup")
				?.valueChanges.pipe(
					startWith(this.form.get("occupationGroup")?.value),
					takeUntil(this.destroy$),
				)
				.subscribe((value) => {
					this.checkOccupationStatus(value);
					if (value) {
						this.getOccupation();
					} else {
						this.occupations = [];
						this.form.get("occupation")?.reset("", { emitEvent: false });
					}
				});

			this.form
				.get("startYear")
				?.valueChanges.pipe(
					startWith(this.form.get("startYear")?.value),
					takeUntil(this.destroy$),
				)
				.subscribe((value) => {
					this.checkStartDateStatus(value);
					this.updateDateOptions("startMonth", "startYear");
				});

			this.form
				.get("startMonth")
				?.valueChanges.pipe(takeUntil(this.destroy$))
				.subscribe(() => {
					this.updateDateOptions("startMonth", "startYear");
				});

			merge(
				this.form.get("startDate")?.valueChanges ?? [],
				this.form.get("startMonth")?.valueChanges ?? [],
				this.form.get("startYear")?.valueChanges ?? [],
			)
				.pipe(distinctUntilChanged(), takeUntil(this.destroy$))
				.subscribe(() => {
					this.calculateWorkExperience();
				});
		} else if (this.formTitle === this.FORM_TITLE_INCOME_EXPENSES) {
		}
	}

	private checkOccupationStatus(groupValue: any): void {
		const occupationControl = this.form.get("occupation");
		if (!groupValue) {
			occupationControl?.disable({ emitEvent: false });
		} else {
			occupationControl?.enable({ emitEvent: false });
		}
	}

	private checkStartDateStatus(yearValue: any): void {
		const monthControl = this.form.get("startMonth");
		const dateControl = this.form.get("startDate");
		if (yearValue) {
			monthControl?.enable({ emitEvent: false });
			dateControl?.enable({ emitEvent: false });
		} else {
			monthControl?.disable({ emitEvent: false });
			monthControl?.reset("", { emitEvent: false });
			dateControl?.disable({ emitEvent: false });
			dateControl?.reset("", { emitEvent: false });
			this.dateOptions = [];
		}
	}

	async setWorkingFormValue() {
		const startDate = this.workingInfo?.startDate;
		if (startDate) {
			const dateValue = DateUtils.getDateParts(startDate);
			this.form
				.get("startYear")
				?.setValue(dateValue?.year, { emitEvent: false });
			this.form
				.get("startMonth")
				?.setValue(dateValue?.month, { emitEvent: false });
			this.updateDateOptions("startMonth", "startYear");
			this.form
				.get("startDate")
				?.setValue(dateValue?.day, { emitEvent: false });
		}

		const occupationGroupCode = this.workingInfo?.occupationGroupCode;
		if (occupationGroupCode) {
			const group = this.occupationGroups.find(
				(group: OccupationGroup) => group.code === occupationGroupCode,
			);
			this.form.get("occupationGroup")?.setValue(group, { emitEvent: false });
			await this.getOccupation();
		}

		const occupationCode = this.workingInfo?.occupationCode;
		if (occupationCode) {
			if (!this.occupations || this.occupations.length === 0) {
				await this.getOccupation();
			}
			const occupation = this.occupations.find(
				(occupation: Occupation) => occupation.code === occupationCode,
			);
			this.form.get("occupation")?.setValue(occupation, { emitEvent: false });
		}

		this.checkOccupationStatus(this.form.get("occupationGroup")?.value);
		this.checkStartDateStatus(this.form.get("startYear")?.value);
		this.calculateWorkExperience();
	}

	calculateWorkExperience() {
		const yearVal = this.form.get("startYear")?.value;
		const monthVal = this.form.get("startMonth")?.value;
		const dateVal = this.form.get("startDate")?.value;

		if (!monthVal) return;
		const date = DateUtils.generateDate(dateVal, monthVal, yearVal);
		const today = dayjs();
		const startDate = dayjs(date, "YYYY-MM-DD");
		const years = today.diff(startDate, "years");
		const startDateAdded = startDate.add(years, "years");
		const months = today.diff(startDateAdded, "months");

		const workExperience = `${years > 0 ? `${years} ปี` : ""}  ${
			months > 0 ? `${months} เดือน` : ""
		}`;

		this.form.get("workExperience")?.setValue(workExperience);
	}

	updateDateOptions(
		keyMonth: string,
		keyYear: string,
		include_0: boolean = false,
	) {
		const month = this.form.get(keyMonth)?.value;
		const year = this.form.get(keyYear)?.value;
		if (month && year) {
			this.dateOptions = DateUtils.getDateOptions(include_0, month, year);
			const currentDay = this.form.get("startDate")?.value;
			if (
				currentDay &&
				!this.dateOptions.some(
					(option: { value: number }) => option.value === currentDay,
				)
			) {
				this.form.get("startDate")?.reset("", { emitEvent: false });
			}
		} else {
			this.dateOptions = [];
			this.form.get("startDate")?.reset("", { emitEvent: false });
		}
	}

	async cancel(): Promise<void> {
		await this.modalController.dismiss({ item: this.workingInfo });
	}

	async onSubmit(): Promise<void> {
		if (this.form.valid) {
			this.showConfirmationDialog();
		} else {
			this.form.markAllAsTouched();
		}
	}

	/**
	 * Shows confirmation dialog for loan type selection
	 */
	public showConfirmationDialog(): void {
		this.appUtilsService.showAlertDialog({
			header: `แก้ไขข้อมูล${this.formTitle === this.FORM_TITLE_WORK_INFORMATION ? this.FORM_TITLE_WORK_INFORMATION : this.FORM_TITLE_INCOME_EXPENSES}`,
			message: `หากท่านทำการแก้ไขข้อมูล${this.formTitle === this.FORM_TITLE_WORK_INFORMATION ? this.FORM_TITLE_WORK_INFORMATION : this.FORM_TITLE_INCOME_EXPENSES} ท่านจะต้องกรอกข้อมูลสินค้าและการผ่อนชำระใหม่อีกครั้ง`,
			subMessage: "หากต้องการดำเนินการต่อ กรุณากดปุ่ม “ยืนยัน”",
			acceptLabel: "ยืนยัน",
			rejectLabel: "ย้อนกลับ",
			acceptCallback: () => this.runWithLoading(this.updateWorkInfo.bind(this)),
			rejectCallback: () => console.log("ย้อนกลับแล้ว"),
		});
	}

	async updateWorkInfo(): Promise<void> {
		await this.runWithLoading(async () => {
			try {
				let success = false;
				if (this.formTitle === this.FORM_TITLE_WORK_INFORMATION) {
					success = await this.updateWorkInformation();
				} else if (this.formTitle === this.FORM_TITLE_INCOME_EXPENSES) {
					success = await this.updateIncomeExpenses();
				} else {
					throw new Error("Invalid item type for submission.");
				}

				if (success) {
					if (
						this.formTitle === this.FORM_TITLE_INCOME_EXPENSES &&
						this.form.get("incomeSlip")?.value === false
					) {
						await this.modalController.dismiss({ value: null, refresh: true });
					} else {
						await this.modalController.dismiss({
							value: this.form.value,
							refresh: true,
						});
					}
				} else {
					await this.appUtilsService.showError(
						"Error",
						"Failed to update information",
					);
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	async updateWorkInformation(): Promise<boolean> {
		const txnId = this.getTxnIdOrError();
		const startDate = DateUtils.generateDate(
			this.form.get("startDate")?.value,
			this.form.get("startMonth")?.value,
			this.form.get("startYear")?.value,
		);

		const request: UpdateWorkingInfoRequestDto = {
			txn_id: txnId,
			occupation_group_code: this.form.get("occupationGroup")?.value.code,
			occupation_group_name: this.form.get("occupationGroup")?.value.name,
			occupation_code: this.form.get("occupation")?.value.code,
			occupation_name: this.form.get("occupation")?.value.name,
			position: this.form.get("position")?.value,
			start_date: startDate,
		};

		console.log("Update Work Information Request:", request);
		return await this.updateWorkingInfo(request);
	}

	async updateIncomeExpenses(): Promise<boolean> {
		const txnId = this.getTxnIdOrError();
		const request: UpdateWorkingInfoRequestDto = {
			txn_id: txnId,
			monthly_income: Number(this.form.get("monthlyIncome")?.value || 0),
			additional_income: Number(this.form.get("additionalIncome")?.value || 0),
			monthly_expense: Number(this.form.get("monthlyExpense")?.value || 0),
			income_slip: this.form.get("incomeSlip")?.value,
		};

		return await this.updateWorkingInfo(request);
	}

	async updateWorkingInfo(request: UpdateWorkingInfoRequestDto) {
		try {
			const response = await this.starmoneyService.updateWorkingInfo(request);
			if (response.isSuccess()) {
				return true;
			}

			return false;
		} catch (error) {
			return await this.handleError(error);
		}
	}

	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;
		let subMessage = "";

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiMessage = error.error?.message;
			const apiError = error.error?.error;

			errorMessage = apiError || error.statusText || errorMessage;
			subMessage = apiMessage || "";

			if (error.error.error === "ARG.VALIDATION_ERROR") {
				const errors = error.error.errors;
				Object.keys(errors).forEach((field) => {
					const mapField: Record<string, string> = {
						occupation_group_name: "occupationGroup",
						occupation: "occupation",
						position: "position",
					};

					const formField = mapField[field as keyof typeof mapField];
					console.log(formField);

					if (this.form.controls[formField]) {
						console.log("Form Field", this.form.controls[formField]);
						const message: string =
							MessageErrorUtils.argumentValidationErrorMessage(
								errors[field][0],
							);
						this.form.controls[formField].setErrors({ serverError: message });
					}
				});
			}

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			subMessage: subMessage || "กรุณาลองใหม่อีกครั้ง",
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
