.header-container {
	@apply sticky top-0 z-10;
}

.content-container {
	@apply flex-1 overflow-auto;
}

.form-group {
	@apply mb-4;
}

.form-label {
	@apply mb-1 block text-sm font-medium text-gray-700;
}

// Override PrimeNG styles
:host ::ng-deep {
	.p-dropdown {
		@apply w-full;

		&.ng-invalid.ng-touched {
			@apply border-red-500;
		}
	}

	.p-inputtext {
		&.ng-invalid.ng-touched {
			@apply border-red-500;
		}
	}

	.p-button {
		&.p-button-outlined {
			@apply border-gray-400 text-gray-700;
		}

		&:not(.p-button-outlined) {
			@apply border-primary bg-primary;
		}

		&:disabled {
			@apply cursor-not-allowed opacity-50;
		}
	}

	.p-radiobutton {
		.p-radiobutton-box {
			&.p-highlight {
				@apply border-primary bg-primary;
			}
		}
	}
}