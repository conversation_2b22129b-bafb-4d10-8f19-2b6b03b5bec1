<ion-content>
	<div class="form-container">
		<!-- Header -->
		<div class="form-header">
			<h2 class="form-title">
				{{ formTitle === "work" ? "ข้อมูลการทำงาน" : "ข้อมูลรายรับรายจ่าย" }}
			</h2>
		</div>

		<!-- Work Information Form -->
		<form
			*ngIf="form && formTitle === FORM_TITLE_WORK_INFORMATION"
			[formGroup]="form"
			class="space-y-6">
			<!-- Occupation Group -->
			<div class="form-group">
				<label for="occupationGroup" class="form-label">
					กลุ่มอาชีพ
					<span class="text-red-500">*</span>
				</label>
				<p-select
					id="occupationGroup"
					formControlName="occupationGroup"
					[options]="occupationGroups"
					optionLabel="name"
					placeholder="เลือกกลุ่มอาชีพ"
					[filter]="true"
					styleClass="w-full"
					[class.ng-invalid]="
						form.get('occupationGroup')?.invalid &&
						form.get('occupationGroup')?.touched
					"></p-select>
				<small
					*ngIf="
						form.get('occupationGroup')?.invalid &&
						form.get('occupationGroup')?.touched
					"
					class="text-xs text-red-500">
					กรุณาเลือกกลุ่มอาชีพ
				</small>
			</div>

			<!-- Occupation -->
			<div class="form-group">
				<label for="occupation" class="form-label">
					อาชีพ
					<span class="text-red-500">*</span>
				</label>
				<p-select
					id="occupation"
					formControlName="occupation"
					[options]="occupations"
					optionLabel="name"
					placeholder="เลือกอาชีพ"
					[filter]="true"
					styleClass="w-full"
					[class.ng-invalid]="
						form.get('occupation')?.invalid && form.get('occupation')?.touched
					"></p-select>
				<small
					*ngIf="
						form.get('occupation')?.invalid && form.get('occupation')?.touched
					"
					class="text-xs text-red-500">
					กรุณาเลือกอาชีพ
				</small>
			</div>

			<!-- Position -->
			<div class="form-group">
				<label for="position" class="form-label">
					ตำแหน่งงาน
					<span class="text-red-500">*</span>
				</label>
				<input
					type="text"
					pInputText
					id="position"
					formControlName="position"
					placeholder="ระบุตำแหน่งงาน"
					class="w-full"
					[class.ng-invalid]="
						form.get('position')?.invalid && form.get('position')?.touched
					" />
				<small
					*ngIf="form.get('position')?.invalid && form.get('position')?.touched"
					class="text-xs text-red-500">
					กรุณาระบุตำแหน่งงาน
				</small>
			</div>

			<!-- Start Date -->
			<div class="form-group">
				<label class="form-label">
					แรกวง/ ตำบล
					<span class="text-red-500">*</span>
				</label>
				<div class="flex gap-2">
					<!-- Year -->
					<div class="w-1/3">
						<p-select
							formControlName="startYear"
							[options]="yearOptions"
							placeholder="ปี"
							styleClass="w-full"
							[class.ng-invalid]="
								form.get('startYear')?.invalid && form.get('startYear')?.touched
							"></p-select>
					</div>

					<!-- Month -->
					<div class="w-1/3">
						<p-select
							formControlName="startMonth"
							[options]="monthOptions"
							placeholder="เดือน"
							styleClass="w-full"
							[class.ng-invalid]="
								form.get('startMonth')?.invalid &&
								form.get('startMonth')?.touched
							"></p-select>
					</div>

					<!-- Day -->
					<div class="w-1/3">
						<p-select
							formControlName="startDate"
							[options]="dateOptions"
							placeholder="วัน"
							styleClass="w-full"
							[class.ng-invalid]="
								form.get('startDate')?.invalid && form.get('startDate')?.touched
							"></p-select>
					</div>
				</div>
				<small
					*ngIf="
						(form.get('startYear')?.invalid &&
							form.get('startYear')?.touched) ||
						(form.get('startMonth')?.invalid &&
							form.get('startMonth')?.touched) ||
						(form.get('startDate')?.invalid && form.get('startDate')?.touched)
					"
					class="text-xs text-red-500">
					กรุณาระบุวันที่เริ่มงาน
				</small>
			</div>

			<!-- workExperience -->
			<div class="form-group">
				<label for="workExperience" class="form-label">
					อายุงาน
					<span class="text-red-500">*</span>
				</label>
				<input
					type="text"
					pInputText
					id="workExperience"
					formControlName="workExperience"
					placeholder="ระบุตำแหน่งงาน"
					class="w-full"
					[class.ng-invalid]="
						form.get('workExperience')?.invalid &&
						form.get('workExperience')?.touched
					" />
				<div class="mt-4 text-sm text-gray-500">
					ระบบจะคำนวณให้เมื่อท่านระบุวันเริ่มงาน
				</div>
			</div>
		</form>

		<!-- Income Information Form -->
		<form
			*ngIf="form && formTitle === FORM_TITLE_INCOME_EXPENSES"
			[formGroup]="form"
			class="space-y-6">
			<!-- Income Slip -->
			<div class="form-group">
				<label class="form-label">
					ท่านมีสลิปเงินเดือนหรือไม่?
					<span class="text-red-500">*</span>
				</label>
				<div class="mt-2 flex gap-4">
					<div
						class="flex w-1/2 cursor-pointer items-start rounded-lg border border-primary p-2">
						<p-radiobutton
							name="incomeSlip"
							[value]="true"
							formControlName="incomeSlip"
							inputId="option1" />
						<label for="option1" class="ml-2 flex-1 text-primary">มี</label>
					</div>

					<div
						class="flex w-1/2 cursor-pointer items-start rounded-lg border border-primary p-2">
						<p-radiobutton
							name="incomeSlip"
							[value]="false"
							formControlName="incomeSlip"
							inputId="option2" />
						<label for="option2" class="ml-2 flex-1 text-primary">ไม่มี</label>
					</div>
				</div>
			</div>

			<!-- Monthly Income -->
			<div class="form-group">
				<label for="monthlyIncome" class="form-label">
					รายได้ประจำต่อเดือน
					<span class="text-red-500">*</span>
				</label>
				<input
					type="number"
					pInputText
					id="monthlyIncome"
					formControlName="monthlyIncome"
					placeholder="ระบุรายได้ประจำต่อเดือน"
					class="w-full"
					[class.ng-invalid]="
						form.get('monthlyIncome')?.invalid &&
						form.get('monthlyIncome')?.touched
					" />
				<small
					*ngIf="
						form.get('monthlyIncome')?.invalid &&
						form.get('monthlyIncome')?.touched
					"
					class="text-xs text-red-500">
					กรุณาระบุรายได้ประจำต่อเดือน
				</small>
			</div>

			<!-- Additional Income -->
			<div class="form-group">
				<label for="additionalIncome" class="form-label">
					รายได้อื่นๆ ต่อเดือน
					<span class="text-red-500">*</span>
				</label>
				<input
					type="number"
					pInputText
					id="additionalIncome"
					formControlName="additionalIncome"
					placeholder="ระบุรายได้อื่นๆ ต่อเดือน"
					class="w-full"
					[class.ng-invalid]="
						form.get('additionalIncome')?.invalid &&
						form.get('additionalIncome')?.touched
					" />
				<small
					*ngIf="
						form.get('additionalIncome')?.invalid &&
						form.get('additionalIncome')?.touched
					"
					class="text-xs text-red-500">
					กรุณาระบุรายได้อื่นๆ ต่อเดือน
				</small>
				<small
					*ngIf="!form.get('additionalIncome')?.invalid"
					class="text-xs text-gray-500">
					หากไม่มีรายได้อื่นๆ กรุณาระบุ 0
				</small>
			</div>

			<!-- Monthly Expense -->
			<div class="form-group">
				<label for="monthlyExpense" class="form-label">
					รายจ่ายต่อเดือน
					<span class="text-red-500">*</span>
				</label>
				<input
					type="number"
					pInputText
					id="monthlyExpense"
					formControlName="monthlyExpense"
					placeholder="ระบุรายจ่ายต่อเดือน"
					class="w-full"
					[class.ng-invalid]="
						form.get('monthlyExpense')?.invalid &&
						form.get('monthlyExpense')?.touched
					" />
				<small
					*ngIf="
						form.get('monthlyExpense')?.invalid &&
						form.get('monthlyExpense')?.touched
					"
					class="text-xs text-red-500">
					กรุณาระบุรายจ่ายต่อเดือน
				</small>
			</div>
		</form>
	</div>

	<!-- Footer -->
	<app-footer>
		<div class="footer-wrapper !justify-between">
			<p-button
				label="ยกเลิก"
				fluid="true"
				[outlined]="true"
				[disabled]="isLoading()"
				(click)="cancel()" />
			<p-button
				label="บันทึก"
				styleClass="w-full"
				[disabled]="!isFormValid || isLoading()"
				(click)="onSubmit()" />
		</div>
	</app-footer>
</ion-content>
