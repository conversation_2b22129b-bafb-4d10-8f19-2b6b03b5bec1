<ion-content>
	<div class="form-container">
		<form *ngIf="form" [formGroup]="form" (ngSubmit)="onSubmit()">
			<!-- Header -->
			<div class="form-header">
				<!-- Current Address Form Title -->
				<div *ngIf="formTitle === FORM_TITLE_CURRENT_ADDRESS">
					<h2 class="form-title">ที่อยู่ปัจจุบัน</h2>
					<div class="checkbox-container">
						<p-checkbox
							formControlName="isSameAsIdCard"
							[binary]="true"
							inputId="isSameAsIdCard"></p-checkbox>
						<label for="isSameAsIdCard" class="checkbox-label">
							ที่อยู่ปัจจุบันของฉัน เหมือนกับที่อยู่ตามหน้าบัตรประชาชน
						</label>
					</div>
				</div>

				<!-- Housing Type Form Title -->
				<div *ngIf="formTitle === FORM_TITLE_HOUSING_TYPE">
					<h2 class="form-title">ลักษณะที่อยู่อาศัย</h2>
				</div>
			</div>

			<!-- Current Address Form Fields -->
			<div class="form-fields" *ngIf="formTitle === FORM_TITLE_CURRENT_ADDRESS">
				<!-- เลขที่ -->
				<div class="form-group">
					<label for="houseNo" class="form-label">
						เลขที่
						<span class="required">*</span>
					</label>
					<input
						type="text"
						pInputText
						id="houseNo"
						formControlName="houseNo"
						placeholder="ระบุเลขที่"
						class="form-control" />
					<small
						*ngIf="form.get('houseNo')?.invalid && form.get('houseNo')?.touched"
						class="error-message">
						กรุณาระบุเลขที่
					</small>
				</div>

				<!-- หมู่ -->
				<div class="form-group">
					<label for="villageNo" class="form-label">หมู่</label>
					<input
						type="text"
						pInputText
						id="villageNo"
						formControlName="villageNo"
						placeholder="ระบุหมู่"
						class="form-control" />
				</div>

				<!-- ซอย -->
				<div class="form-group">
					<label for="alley" class="form-label">ซอย</label>
					<input
						type="text"
						pInputText
						id="alley"
						formControlName="alley"
						placeholder="ระบุซอย"
						class="form-control" />
				</div>

				<!-- ถนน -->
				<div class="form-group">
					<label for="street" class="form-label">ถนน</label>
					<input
						type="text"
						pInputText
						id="street"
						formControlName="street"
						placeholder="ระบุถนน"
						class="form-control" />
				</div>

				<!-- จังหวัด -->
				<div class="form-group">
					<label for="province" class="form-label">
						จังหวัด
						<span class="required">*</span>
					</label>
					<p-select
						id="province"
						formControlName="province"
						[options]="provinces"
						[filter]="true"
						placeholder="เลือกจังหวัด"
						styleClass="w-full"></p-select>
					<small
						*ngIf="
							form.get('province')?.invalid && form.get('province')?.touched
						"
						class="error-message">
						กรุณาเลือกจังหวัด
					</small>
				</div>

				<!-- เขต / อำเภอ -->
				<div class="form-group">
					<label for="district" class="form-label">
						เขต / อำเภอ
						<span class="required">*</span>
					</label>
					<p-select
						id="district"
						formControlName="district"
						[options]="districts"
						placeholder="เลือกเขต / อำเภอ"
						[filter]="true"
						[disabled]="!form.get('province')?.value"
						styleClass="w-full"></p-select>
					<small
						*ngIf="
							form.get('district')?.invalid && form.get('district')?.touched
						"
						class="error-message">
						กรุณาเลือกเขต / อำเภอ
					</small>
				</div>

				<!-- แขวง / ตำบล -->
				<div class="form-group">
					<label for="subDistrict" class="form-label">
						แขวง / ตำบล
						<span class="required">*</span>
					</label>
					<p-select
						id="subDistrict"
						formControlName="subDistrict"
						[options]="subDistricts"
						placeholder="เลือกแขวง / ตำบล"
						[filter]="true"
						[disabled]="!form.get('district')?.value"
						styleClass="w-full"></p-select>
					<small
						*ngIf="
							form.get('subDistrict')?.invalid &&
							form.get('subDistrict')?.touched
						"
						class="error-message">
						กรุณาเลือกแขวง / ตำบล
					</small>
				</div>

				<!-- รหัสไปรษณีย์ -->
				<div class="form-group">
					<label for="postalCode" class="form-label">
						รหัสไปรษณีย์
						<span class="required">*</span>
					</label>
					<p-select
						id="postalCode"
						formControlName="postalCode"
						[options]="postalCodes"
						placeholder="เลือกรหัสไปรษณีย์"
						[filter]="true"
						[disabled]="!form.get('subDistrict')?.value"
						styleClass="w-full"></p-select>
					<small
						*ngIf="
							form.get('postalCode')?.invalid && form.get('postalCode')?.touched
						"
						class="error-message">
						กรุณาเลือกรหัสไปรษณีย์
					</small>
				</div>
			</div>

			<!-- Housing Type Form Fields -->
			<div class="form-fields" *ngIf="formTitle === FORM_TITLE_HOUSING_TYPE">
				<!-- ลักษณะที่อยู่อาศัย -->
				<div class="form-group">
					<label for="addressType" class="form-label">
						ลักษณะที่อยู่อาศัย
						<span class="required">*</span>
					</label>
					<p-select
						id="addressType"
						formControlName="addressType"
						optionLabel="name"
						[options]="housingTypes"
						placeholder="เลือกลักษณะที่อยู่อาศัย"
						styleClass="w-full"></p-select>
					<small
						*ngIf="
							form.get('addressType')?.invalid &&
							form.get('addressType')?.touched
						"
						class="error-message">
						กรุณาเลือกลักษณะที่อยู่อาศัย
					</small>
				</div>
			</div>
		</form>
	</div>

	<!-- Footer -->
	<app-footer>
		<div class="footer-wrapper !justify-between">
			<p-button
				label="ยกเลิก"
				fluid="true"
				[outlined]="true"
				[disabled]="isLoading()"
				(click)="handleBack()" />
			<p-button
				label="บันทึก"
				styleClass="w-full"
				[disabled]="!isFormValid || isLoading()"
				(click)="onSubmit()" />
		</div>
	</app-footer>
</ion-content>
