import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import {
	ChangeDetectorRef,
	Component,
	inject,
	Input,
	OnDestroy,
	OnInit,
} from "@angular/core";
import {
	FormControl,
	FormGroup,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import {
	AddressInfo,
	District,
	HousingType,
	Province,
	SubDistrict,
} from "@domain/models/address.model";
import { IonicModule } from "@ionic/angular";
import { ModalController } from "@ionic/angular/standalone";
import { ButtonModule } from "primeng/button";
import { CheckboxModule } from "primeng/checkbox";
import { InputTextModule } from "primeng/inputtext";
import { SelectModule } from "primeng/select";
import { filter, Subject, takeUntil } from "rxjs";
import { AddressItem } from "src/app/adapters/primary/loan/address-contact/address-contact.component";
import { WorkflowStepBaseComponent } from "src/app/adapters/primary/workflow/workflow-step-base.component";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { MessageErrorUtils } from "src/app/core/utils/message-error-utils";
import { FooterComponent } from "../../footer/footer.component";

// Constants
const FORM_TITLE_CURRENT_ADDRESS = "ที่อยู่ปัจจุบัน";
const FORM_TITLE_HOUSING_TYPE = "ลักษณะที่อยู่อาศัย";
const ADDRESS_TYPE_CITIZEN = "CITIZEN";
const ADDRESS_TYPE_CATEGORY = "CATEGORY";
const POSTCODE_OPTION_OTHER = "อื่นๆ";

// Form data interface
interface AddressFormData {
	houseNo: string;
	villageNo: string;
	alley: string;
	street: string;
	province: string;
	district: string;
	subDistrict: string;
	postalCode: string;
	isSameAsIdCard: boolean;
	otherPostalCode: string;
	addressType?: HousingType | null;
}

@Component({
	selector: "app-address-info-form",
	standalone: true,
	imports: [
		CommonModule,
		ReactiveFormsModule,
		IonicModule,
		ButtonModule,
		InputTextModule,
		SelectModule,
		CheckboxModule,
		FooterComponent,
	],
	templateUrl: "./address-info-form.component.html",
	styleUrls: ["./address-info-form.component.scss"],
})
export class AddressInfoFormComponent
	extends WorkflowStepBaseComponent
	implements OnInit, OnDestroy
{
	private readonly starmoneyService = inject(StarmoneyService);
	private readonly appUtilsService = inject(AppUtilsService);
	private readonly modalController = inject(ModalController);
	private readonly cdr = inject(ChangeDetectorRef);

	public form!: FormGroup;
	@Input() item: AddressItem | null = null;
	@Input() addressInfo: AddressInfo[] = [];

	public provinces: string[] = [];
	public districts: string[] = [];
	public subDistricts: string[] = [];
	public postalCodes: string[] = [];
	public housingTypes: HousingType[] = [];

	private destroy$ = new Subject<void>();
	private citizenAddressSnapshot: Partial<AddressFormData> | null = null;

	// Constants moved inside the class for template access
	public readonly FORM_TITLE_CURRENT_ADDRESS = "ที่อยู่ปัจจุบัน";
	public readonly FORM_TITLE_HOUSING_TYPE = "ลักษณะที่อยู่อาศัย";
	public readonly ADDRESS_TYPE_CITIZEN = "CITIZEN";
	public readonly ADDRESS_TYPE_CATEGORY = "CATEGORY";
	public readonly POSTCODE_OPTION_OTHER = "อื่นๆ";

	get formTitle(): string {
		return this.item?.title || "";
	}

	get isFormValid(): boolean {
		return this.form && this.form.valid;
	}

	// Validation helpers
	isFieldInvalid(fieldName: string): boolean {
		const field = this.form.get(fieldName);
		return field ? field.invalid && (field.dirty || field.touched) : false;
	}

	shouldShowError(fieldName: string, errorType: string): boolean {
		const field = this.form.get(fieldName);
		return field
			? field.errors?.[errorType] && (field.dirty || field.touched)
			: false;
	}

	async ngOnInit(): Promise<void> {
		// get data from parent
		try {
			console.log("Address Info Form Item:", this.item);
			console.log("Address Info Form Address Info:", this.addressInfo);
			console.log(this.item?.title);
			console.log(FORM_TITLE_HOUSING_TYPE);
			console.log(FORM_TITLE_CURRENT_ADDRESS);
			if (this.item?.title === FORM_TITLE_HOUSING_TYPE) {
				this.initHousingTypeForm();
				await this.getHousingType();
				this.setupHousingTypeFormListeners();
			} else if (this.item?.title === FORM_TITLE_CURRENT_ADDRESS) {
				if (this.addressInfo && !Array.isArray(this.addressInfo)) {
					this.addressInfo = [this.addressInfo];
				}

				await this.initContactForm();
				this.setupContactFormListeners();

				const initialDataPromises: Promise<void>[] = [this.getProvince()];
				const initialProvince = this.form.get("province")?.value;
				const initialDistrict = this.form.get("district")?.value;
				const initialSubDistrict = this.form.get("subDistrict")?.value;

				if (initialProvince) initialDataPromises.push(this.getDistrict(false));
				if (initialDistrict)
					initialDataPromises.push(this.getSubDistrict(false));
				if (initialSubDistrict)
					initialDataPromises.push(this.getPostCode(false));

				await Promise.all(initialDataPromises);

				if (this.form.get("isSameAsIdCard")?.value === true) {
					await this.applyCitizenAddress();
				}

				this.updateDistrictState(initialProvince);
				this.updateSubDistrictState(initialDistrict);
				this.updatePostalCodeState(initialSubDistrict);
			} else {
				console.warn("Unknown form title:", this.item?.title);
			}
		} catch (error) {
			await this.handleError(error);
		} finally {
			this.cdr.markForCheck();
			await this.appUtilsService.hideLoading();
		}
	}

	ngOnDestroy(): void {
		this.destroy$.next();
		this.destroy$.complete();
	}

	private initHousingTypeForm(): void {
		if (!this.item) return;
		const housingInfo = this.addressInfo.find(
			(addr) => addr.addressType === this.item!.type,
		);
		const initialHousingType =
			this.housingTypes.find(
				(item: HousingType) => item.code === housingInfo?.housingCode,
			) || null;

		this.form = new FormGroup({
			addressType: new FormControl<HousingType | null>(initialHousingType, [
				Validators.required,
			]),
		});
	}

	private async initContactForm(): Promise<void> {
		if (!this.item) return;
		const addressInfo = await this.getAddressInfo(this.item!.type);
		const { houseNo, villageNo, formValues } =
			this.extractAddressValues(addressInfo);

		this.form = new FormGroup({
			houseNo: new FormControl(houseNo, [Validators.required]),
			villageNo: new FormControl(villageNo),
			alley: new FormControl(formValues.alley),
			street: new FormControl(formValues.street),
			province: new FormControl(formValues.province, [Validators.required]),
			district: new FormControl(
				{ value: formValues.district, disabled: !formValues.province },
				[Validators.required],
			),
			subDistrict: new FormControl(
				{ value: formValues.subDistrict, disabled: !formValues.district },
				[Validators.required],
			),
			postalCode: new FormControl(
				{ value: formValues.postalCode, disabled: !formValues.subDistrict },
				[Validators.required],
			),
			isSameAsIdCard: new FormControl(formValues.isSameAsIdCard || false),
			otherPostalCode: new FormControl(""),
		});
	}

	private setupHousingTypeFormListeners(): void {
		this.form
			.get("addressType")
			?.valueChanges.pipe(takeUntil(this.destroy$))
			.subscribe((value) => {
				// console.log('Housing Type changed:', value);
			});
	}

	private setupContactFormListeners(): void {
		// Guard against potential null form access, though unlikely given init order
		if (!this.form) return;

		// Province changes
		this.form
			.get("province")
			?.valueChanges.pipe(takeUntil(this.destroy$))
			.subscribe((value) => {
				this.resetDependentFields(["district", "subDistrict", "postalCode"]);
				if (value) {
					this.getDistrict();
					this.form.get("district")?.enable({ emitEvent: false });
				} else {
					this.form.get("district")?.disable({ emitEvent: false });
				}
				this.updateFormValidityAndCheck();
			});

		this.form
			.get("district")
			?.valueChanges.pipe(takeUntil(this.destroy$))
			.subscribe((value) => {
				this.resetDependentFields(["subDistrict", "postalCode"]);
				if (value) {
					this.getSubDistrict();
					this.form.get("subDistrict")?.enable({ emitEvent: false });
				} else {
					this.form.get("subDistrict")?.disable({ emitEvent: false });
				}
				this.updateFormValidityAndCheck();
			});

		this.form
			.get("subDistrict")
			?.valueChanges.pipe(takeUntil(this.destroy$))
			.subscribe((value) => {
				this.resetDependentFields(["postalCode"]);
				if (value) {
					this.getPostCode();
					this.form.get("postalCode")?.enable({ emitEvent: false });
				} else {
					this.form.get("postalCode")?.disable({ emitEvent: false });
				}
				this.updateFormValidityAndCheck();
			});

		this.form
			.get("postalCode")
			?.valueChanges.pipe(takeUntil(this.destroy$))
			.subscribe((code) => {
				const otherPostalCodeControl = this.form.get("otherPostalCode");
				if (code === POSTCODE_OPTION_OTHER) {
					otherPostalCodeControl?.setValidators([
						Validators.required,
						Validators.pattern("^[0-9]{5}$"),
					]);
				} else {
					otherPostalCodeControl?.clearValidators();
					otherPostalCodeControl?.setValue("", { emitEvent: false });
				}
				otherPostalCodeControl?.updateValueAndValidity({ emitEvent: false });
				this.updateFormValidityAndCheck();
			});

		this.form
			.get("isSameAsIdCard")
			?.valueChanges.pipe(takeUntil(this.destroy$))
			.subscribe(async (isSame) => {
				if (isSame) {
					await this.applyCitizenAddress();
				} else {
					this.citizenAddressSnapshot = null;
				}
				this.updateFormValidityAndCheck();
			});

		this.form.valueChanges
			.pipe(
				takeUntil(this.destroy$),
				filter(
					() =>
						this.form.get("isSameAsIdCard")?.value === true &&
						!!this.citizenAddressSnapshot,
				),
			)
			.subscribe((currentFormValues) => {
				if (this.hasAddressChangedFromSnapshot(currentFormValues)) {
					this.form
						.get("isSameAsIdCard")
						?.setValue(false, { emitEvent: false });
					this.citizenAddressSnapshot = null;
					this.updateFormValidityAndCheck();
				}
			});
	}

	private hasAddressChangedFromSnapshot(currentFormValues: any): boolean {
		if (!this.citizenAddressSnapshot) return false;

		const fieldsToCheck: (keyof AddressFormData)[] = [
			"houseNo",
			"villageNo",
			"alley",
			"street",
			"province",
			"district",
			"subDistrict",
			"postalCode",
		];

		for (const field of fieldsToCheck) {
			const snapshotValue = this.citizenAddressSnapshot[field] ?? "";
			const currentValue = currentFormValues[field] ?? "";
			if (snapshotValue !== currentValue) {
				return true;
			}
		}
		return false;
	}

	private resetDependentFields(fields: string[]): void {
		fields.forEach((fieldName) => {
			const control = this.form.get(fieldName);
			if (control) {
				control.setValue("", { emitEvent: false });
				control.disable({ emitEvent: false });
				switch (fieldName) {
					case "district":
						this.districts = [];
						break;
					case "subDistrict":
						this.subDistricts = [];
						break;
					case "postalCode":
						this.postalCodes = [];
						break;
				}
			}
		});
		this.cdr.markForCheck();
	}

	private updateDistrictState(provinceValue: string | null | undefined): void {
		const districtControl = this.form.get("district");
		if (provinceValue) {
			districtControl?.enable({ emitEvent: false });
		} else {
			districtControl?.disable({ emitEvent: false });
		}
	}

	private updateSubDistrictState(
		districtValue: string | null | undefined,
	): void {
		const subDistrictControl = this.form.get("subDistrict");
		if (districtValue) {
			subDistrictControl?.enable({ emitEvent: false });
		} else {
			subDistrictControl?.disable({ emitEvent: false });
		}
	}

	private updatePostalCodeState(
		subDistrictValue: string | null | undefined,
	): void {
		const postalCodeControl = this.form.get("postalCode");
		if (subDistrictValue) {
			postalCodeControl?.enable({ emitEvent: false });
		} else {
			postalCodeControl?.disable({ emitEvent: false });
		}
	}

	private async applyCitizenAddress(): Promise<void> {
		try {
			await this.appUtilsService.showLoading();
			const frontCardAddress = await this.getAddressInfo(ADDRESS_TYPE_CITIZEN);
			if (!frontCardAddress) {
				console.warn("Citizen address not found.");
				await this.appUtilsService.hideLoading();
				return;
			}

			const { houseNo, villageNo, formValues } =
				this.extractAddressValues(frontCardAddress);
			this.citizenAddressSnapshot = {
				houseNo,
				villageNo,
				alley: formValues.alley,
				street: formValues.street,
				province: formValues.province,
				district: formValues.district,
				subDistrict: formValues.subDistrict,
				postalCode: formValues.postalCode,
			};

			this.form.patchValue(
				{
					houseNo,
					villageNo,
					alley: formValues.alley || "",
					street: formValues.street || "",
					province: formValues.province,
					district: formValues.district,
					subDistrict: formValues.subDistrict,
					postalCode: formValues.postalCode,
				},
				{ emitEvent: false },
			);

			this.updateDistrictState(formValues.province);
			this.updateSubDistrictState(formValues.district);
			this.updatePostalCodeState(formValues.subDistrict);

			const fetchPromises: Promise<void>[] = [];
			if (formValues.province) fetchPromises.push(this.getDistrict(false));
			if (formValues.district) fetchPromises.push(this.getSubDistrict(false));
			if (formValues.subDistrict) fetchPromises.push(this.getPostCode(false));

			if (fetchPromises.length > 0) {
				await Promise.all(fetchPromises);
			}

			this.updateFormValidityAndCheck();
		} catch (error) {
			this.handleError(error);
		} finally {
			await this.appUtilsService.hideLoading();
			this.cdr.markForCheck();
		}
	}

	private async getAddressInfo(
		addressType: string | undefined = this.item?.type,
	): Promise<AddressInfo | null> {
		if (!addressType) {
			console.error("Cannot get address info: addressType is undefined.");
			return null;
		}
		if (!this.addressInfo || !Array.isArray(this.addressInfo)) {
			console.warn("Address info array is invalid or empty.");
			return null;
		}
		return (
			this.addressInfo.find((addr) => addr.addressType === addressType) || null
		);
	}

	private extractAddressValues(addressInfo: AddressInfo | null): {
		houseNo: string;
		villageNo: string;
		formValues: Partial<AddressFormData>;
	} {
		return {
			houseNo: addressInfo?.houseNo || "",
			villageNo: addressInfo?.villageNo || "",
			formValues: {
				alley: addressInfo?.alley || "",
				street: addressInfo?.street || "",
				province: addressInfo?.province || "",
				district: addressInfo?.district || "",
				subDistrict: addressInfo?.subDistrict || "",
				postalCode: addressInfo?.postalCode || "",
				isSameAsIdCard: addressInfo?.isSame ?? false,
				otherPostalCode: "",
			},
		};
	}

	private async getHousingType(): Promise<void> {
		await this.runWithLoading(async () => {
			try {
				const txnId = this.getTxnIdOrError();
				const res = await this.starmoneyService.getHousingTypes({
					txn_id: txnId,
				});
				if (res.statusCode === 200 && res.data) {
					this.housingTypes = res.data;
					const initialHousingType = this.housingTypes.find(
						(item: HousingType) => item.name === this.item?.value,
					);
					if (initialHousingType) {
						this.form
							.get("addressType")
							?.setValue(initialHousingType, { emitEvent: false });
					}
					this.cdr.markForCheck();
				} else {
					throw new Error(res.message || "Failed to fetch housing types");
				}
			} catch (error) {
				this.handleError(error);
			}
		});
	}

	private async getProvince(): Promise<void> {
		await this.runWithLoading(async () => {
			try {
				const txnId = this.getTxnIdOrError();
				const res = await this.starmoneyService.getProvinces({ txn_id: txnId });
				if (res.statusCode === 200 && res.data) {
					this.provinces = res.data.map((p: Province) => p.name).sort();
					this.cdr.markForCheck();
				} else {
					throw new Error(res.message || "Failed to fetch provinces");
				}
			} catch (error) {
				this.handleError(error);
			}
		});
	}

	private async getDistrict(showLoading: boolean = true): Promise<void> {
		const action = async () => {
			try {
				const provinceValue = this.form.get("province")?.value;
				if (!provinceValue) {
					this.districts = [];
					this.cdr.markForCheck();
					return;
				}
				const txnId = this.getTxnIdOrError();
				const res = await this.starmoneyService.getDistricts({
					txn_id: txnId,
					province: provinceValue,
				});
				if (res.statusCode === 200 && res.data) {
					this.districts = res.data.map((d: District) => d.name).sort();
					this.cdr.markForCheck();
				} else {
					this.districts = [];
					throw new Error(
						res.message ||
							`Failed to fetch districts for province ${provinceValue}`,
					);
				}
			} catch (error) {
				this.districts = [];
				await this.handleError(error);
			} finally {
				this.cdr.markForCheck();
			}
		};

		if (showLoading) {
			await this.runWithLoading(action);
		} else {
			await action();
		}
	}

	private async getSubDistrict(showLoading: boolean = true): Promise<void> {
		const action = async () => {
			try {
				const provinceValue = this.form.get("province")?.value;
				const districtValue = this.form.get("district")?.value;

				if (!provinceValue || !districtValue) {
					this.subDistricts = [];
					this.cdr.markForCheck();
					return;
				}

				const txnId = this.getTxnIdOrError();
				const res = await this.starmoneyService.getSubDistricts({
					txn_id: txnId,
					province: provinceValue,
					district: districtValue,
				});
				if (res.isSuccess() && res.data) {
					this.subDistricts = res.data.map((s: SubDistrict) => s.name).sort();
					this.cdr.markForCheck();
				} else {
					this.subDistricts = [];
					throw new Error(
						res.message ||
							`Failed to fetch sub-districts for ${districtValue}, ${provinceValue}`,
					);
				}
			} catch (error) {
				this.subDistricts = [];
				await this.handleError(error);
			} finally {
				this.cdr.markForCheck();
			}
		};
		if (showLoading) {
			await this.runWithLoading(action);
		} else {
			await action();
		}
	}

	private async getPostCode(showLoading: boolean = true): Promise<void> {
		const action = async () => {
			try {
				const provinceValue = this.form.get("province")?.value;
				const districtValue = this.form.get("district")?.value;
				const subDistrictValue = this.form.get("subDistrict")?.value;

				if (!provinceValue || !districtValue || !subDistrictValue) {
					this.postalCodes = [];
					this.cdr.markForCheck();
					return;
				}

				const txnId = this.getTxnIdOrError();
				const res = await this.starmoneyService.getPostalCodes({
					txn_id: txnId,
					province: provinceValue,
					district: districtValue,
					subdistrict: subDistrictValue,
				});

				if (res.statusCode === 200 && res.data) {
					const uniqueCodes = [...new Set(res.data.map((p) => p.code))].sort();
					this.postalCodes = [...uniqueCodes, POSTCODE_OPTION_OTHER];
					this.cdr.markForCheck();
				} else {
					this.postalCodes = [POSTCODE_OPTION_OTHER];
					throw new Error(
						res.message || `Failed to fetch postcodes for ${subDistrictValue}`,
					);
				}
			} catch (error) {
				this.postalCodes = [POSTCODE_OPTION_OTHER];
				await this.handleError(error);
			} finally {
				this.cdr.markForCheck();
			}
		};
		if (showLoading) {
			await this.runWithLoading(action);
		} else {
			await action();
		}
	}

	private updateFormValidityAndCheck(): void {
		this.cdr.markForCheck();
	}

	async handleBack(): Promise<void> {
		this.modalController.dismiss();
	}

	async onSubmit(): Promise<void> {
		this.form.markAllAsTouched();

		if (!this.form.valid) {
			this.appUtilsService.showError(
				"Invalid Form",
				"Please fill in all required fields correctly.",
			);
			this.cdr.markForCheck();
			return;
		}

		if (!this.item) {
			this.handleError(new Error("Item data is missing during submit."));
			return;
		}

		await this.runWithLoading(async () => {
			try {
				let success = false;
				if (this.item?.type === ADDRESS_TYPE_CATEGORY) {
					success = await this.updateCategory();
				} else if (this.item?.type) {
					success = await this.updateAddress();
				} else {
					throw new Error("Invalid item type for submission.");
				}

				if (success) {
					const value =
						this.item.type === ADDRESS_TYPE_CATEGORY
							? (this.form.get("addressType")?.value as any)?.housingTypeName
							: this.getFormattedAddress();
					this.modalController.dismiss({ value, refresh: true });
				}
			} catch (error) {
				console.error("Error updating address", error);
				await this.handleError(error);
			}
		});
	}

	private async updateAddress(): Promise<boolean> {
		try {
			if (!this.item?.type) {
				throw new Error("Address type is missing.");
			}

			const txnId = this.getTxnIdOrError();
			const formValue = this.form.value as AddressFormData;
			const postalCodeToSend =
				formValue.postalCode === POSTCODE_OPTION_OTHER
					? formValue.otherPostalCode
					: formValue.postalCode;

			if (!postalCodeToSend) {
				throw new Error("Postal code is missing.");
			}

			await this.starmoneyService.updateAddressInfo({
				txn_id: txnId,
				addressType: this.item.type,
				house_no: formValue.houseNo,
				village_no: formValue.villageNo || "",
				alley: formValue.alley || "",
				street: formValue.street || "",
				sub_district: formValue.subDistrict,
				district: formValue.district,
				province: formValue.province,
				postal_code: postalCodeToSend,
				is_same: formValue.isSameAsIdCard || false,
				manual_postal_code: formValue.postalCode === POSTCODE_OPTION_OTHER,
			});
			return true;
		} catch (error) {
			console.error("Error updating address", error);
			await this.handleError(error);
			return false;
		}
	}

	private async updateCategory(): Promise<boolean> {
		try {
			const txnId = this.getTxnIdOrError();
			const housingType = this.form.get("addressType")?.value as HousingType;
			if (!housingType?.code || !housingType?.name) {
				throw new Error("Housing type selection is incomplete.");
			}

			await this.starmoneyService.updateHousingType({
				txn_id: txnId,
				housing_type: housingType.name,
				housing_code: housingType.code,
			});
			return true;
		} catch (error) {
			console.error("Error updating housing type", error);
			await this.handleError(error);
			return false;
		}
	}

	private getFormattedAddress(): string {
		if (this.formTitle !== FORM_TITLE_CURRENT_ADDRESS) return "";
		const formValue = this.form.value as AddressFormData;
		const postalCodeDisplay =
			formValue.postalCode === POSTCODE_OPTION_OTHER
				? formValue.otherPostalCode
				: formValue.postalCode;
		const addressParts = [
			formValue.houseNo,
			formValue.villageNo ? `หมู่ ${formValue.villageNo}` : "",
			formValue.alley ? `ซอย ${formValue.alley}` : "",
			formValue.street ? `ถนน ${formValue.street}` : "",
			formValue.subDistrict ? `ต.${formValue.subDistrict}` : "",
			formValue.district ? `อ.${formValue.district}` : "",
			formValue.province ? `จ.${formValue.province}` : "",
			postalCodeDisplay,
		];

		return addressParts.filter((part) => part && String(part).trim()).join(" ");
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			if (error.error.error === "ARG.VALIDATION_ERROR") {
				const errors = error.error.errors;
				Object.keys(errors).forEach((field) => {
					const mapField: Record<string, string> = {
						house_no: "houseNo",
						village_no: "villageNo",
						alley: "alley",
						street: "street",
					};

					const formField = mapField[field as keyof typeof mapField];
					if (this.form.controls[formField]) {
						const message: string =
							MessageErrorUtils.argumentValidationErrorMessage(
								errors[field][0],
							);
						this.form.controls[formField].setErrors({ serverError: message });
					}
				});
			}

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
