<div class="loading-modal-container">
	<app-status-notification
		*ngIf="isShowStatusNotification() && !isLoadingProcessing()"
		class="overlay-modal"
		[config]="statusNotificationOptions()!" />

	<!-- Content -->
	<div class="loading-modal-content">
		<!-- Header -->
		<h1 class="loading-title">{{ title }}</h1>

		<!-- Circular Progress -->
		<div class="circular-progress-container">
			<div class="circular-progress">
				<svg viewBox="0 0 100 100" class="progress-circle">
					<circle cx="50" cy="50" r="45" class="progress-circle-bg" />
					<circle
						cx="50"
						cy="50"
						r="45"
						[class]="
							indeterminateProgress ? 'progress-circle-path indeterminate' : 'progress-circle-path'
						"
						[style.strokeDashoffset]="progressOffset" />
				</svg>
				<div class="progress-icon" *ngIf="showIcon">
					<ion-icon name="checkmark-outline" *ngIf="isComplete"></ion-icon>
				</div>
			</div>
		</div>

		<!-- Status Message -->
		<h2 class="loading-status">{{ statusMessage }}</h2>

		<!-- Description -->
		<p class="loading-description">{{ description }}</p>

		<!-- Action Buttons (if provided) -->
		<div class="loading-actions" *ngIf="showButtons">
			<p-button
				*ngIf="cancelButtonLabel"
				[label]="cancelButtonLabel"
				styleClass="w-full"
				[outlined]="true"
				(onClick)="onCancelClick()"></p-button>

			<p-button
				*ngIf="actionButtonLabel"
				[label]="actionButtonLabel"
				styleClass="w-full"
				(onClick)="onActionClick()"></p-button>
		</div>
	</div>
</div>
