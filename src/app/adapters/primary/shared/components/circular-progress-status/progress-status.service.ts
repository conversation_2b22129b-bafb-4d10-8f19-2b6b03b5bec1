import { computed, Injectable, signal } from "@angular/core";
import { ModalService } from "src/app/core/application/workflow/modal.service";
import { CheckResultType } from "../../../loan/check-id-card/check-id-card.component";
import { StatusNotificationOptions } from "../status-notification/status-notification.component";
import { CircularProgressStatusComponent } from "./progress-status.component";

export enum StatusType {
	SUCCESS = "success",
	WARNING = "warning",
	ERROR = "error",
	INFO = "info",
}

export interface CircularProgressLoadingOptions {
	title?: string;
	statusMessage?: string;
	description?: string;
	progress?: number;
	autoProgress?: boolean;
	progressDuration?: number;
	progressStep?: number;
	showIcon?: boolean;
	isComplete?: boolean;
	showButtons?: boolean;
	cancelButtonLabel?: string;
	actionButtonLabel?: string;
	onCancel?: () => void;
	onAction?: () => void;
	autoClose?: boolean;
	autoCloseDelay?: number;
	backdropDismiss?: boolean;
	fullscreen?: boolean;
	// For async operations
	asyncOperation?: () => Promise<any>;
	indeterminateProgress?: boolean;
	onSuccess?: () => StatusNotificationOptions;
	onWarning?: () => StatusNotificationOptions;
	onError?: () => StatusNotificationOptions;
}

@Injectable({
	providedIn: "root",
})
export class ProgressStatusService {
	private activeModal: HTMLIonModalElement | null = null;
	private activeLoadingModal: HTMLIonModalElement | null = null;

	// Reactive state with signals
	private readonly _isShowStatusNotification = signal<boolean>(false);
	private readonly _statusNotificationOptions = signal<StatusNotificationOptions | null>(null);
	private readonly _isLoadingProcessing = signal<boolean>(false);

	// Computed properties for template binding
	public readonly isShowStatusNotification = computed(() => this._isShowStatusNotification());
	public readonly statusNotificationOptions = computed(() => this._statusNotificationOptions());
	public readonly isLoadingProcessing = computed(() => this._isLoadingProcessing());

	constructor(private modalService: ModalService) {}

	/**
	 * Close the current notification modal
	 * @param data Optional data to pass back when closing
	 * @returns Promise that resolves when the modal is closed
	 */
	async closeNotification(data?: any): Promise<boolean> {
		console.log("Closing notification modal");

		// Close the loading modal (which contains the status notification)
		if (this.activeLoadingModal) {
			console.log("Closing active loading modal");
			await this.activeLoadingModal.dismiss(data);
			this.activeLoadingModal = null;
			console.log("Notification modal closed");

			// Hide the status notification overlay
			this._isShowStatusNotification.set(false);
			this._statusNotificationOptions.set(null);

			return true;
		}

		// Fallback: close activeModal if it exists
		if (this.activeModal) {
			console.log("Closing active modal");
			await this.activeModal.dismiss(data);
			this.activeModal = null;
			console.log("Notification modal closed");

			// Hide the status notification overlay
			this._isShowStatusNotification.set(false);
			this._statusNotificationOptions.set(null);
			return true;
		}

		console.log("No active modal to close");
		return false;
	}

	/**
	 * Show the circular progress loading modal
	 * @param options Configuration options for the loading modal
	 * @returns Promise that resolves when the modal is shown
	 */
	async showLoading(options: CircularProgressLoadingOptions = {}): Promise<HTMLIonModalElement> {
		// Close any existing loading modal
		if (this.activeLoadingModal) {
			await this.hideLoading();
		}

		// Create and show the modal
		this.activeLoadingModal = await this.modalService.openGeneralModal({
			component: CircularProgressStatusComponent,
			componentProps: {
				title: options.title || "ตรวจสอบข้อมูล",
				statusMessage: options.statusMessage || "กำลังตรวจสอบข้อมูล",
				description:
					options.description || "อยู่ระหว่างการตรวจสอบข้อมูล\nกรุณารอจนกว่าระบบจะทำงานเสร็จสิ้น",
				progress: options.progress ?? 0,
				autoProgress: options.indeterminateProgress ? false : (options.autoProgress ?? true),
				progressDuration: options.progressDuration ?? 10000,
				progressStep: options.progressStep ?? 1,
				showIcon: options.showIcon ?? false,
				isComplete: options.isComplete ?? false,
				showButtons: options.showButtons ?? false,
				cancelButtonLabel: options.cancelButtonLabel || "",
				actionButtonLabel: options.actionButtonLabel || "",
				onCancel: options.onCancel || null,
				onAction: options.onAction || null,
				autoClose: options.autoClose ?? false,
				autoCloseDelay: options.autoCloseDelay ?? 1000,
				indeterminateProgress: options.indeterminateProgress ?? false,
			},
			backdropDismiss: options.backdropDismiss ?? false,
			cssClass: ["circular-progress-loading-modal", "modal-blur-backdrop"],
		});

		return this.activeLoadingModal;
	}

	/**
	 * Update the progress of the current loading modal
	 * @param progress New progress value (0-100)
	 */
	updateProgress(progress: number): void {
		if (this.activeLoadingModal) {
			// ไม่สามารถเรียกใช้เมธอดของ component ได้โดยตรง
			// เพราะ componentProps เป็นเพียง object ไม่ใช่ instance ของ component จริงๆ
			if (this.activeLoadingModal.componentProps) {
				// กำหนดค่า progress โดยตรง
				this.activeLoadingModal.componentProps["progress"] = Math.min(Math.max(progress, 0), 100);

				// ถ้า progress เป็น 100 ให้กำหนด isComplete เป็น true
				if (progress >= 100) {
					this.activeLoadingModal.componentProps["isComplete"] = true;
				}
			}
		}
	}

	/**
	 * Set the loading modal as complete
	 */
	setLoadingComplete(): void {
		if (this.activeLoadingModal) {
			// ไม่สามารถเรียกใช้เมธอดของ component ได้โดยตรง
			// เพราะ componentProps เป็นเพียง object ไม่ใช่ instance ของ component จริงๆ
			if (this.activeLoadingModal.componentProps) {
				// กำหนดค่า progress และ isComplete โดยตรง
				this._isLoadingProcessing.set(false);
				this.activeLoadingModal.componentProps["progress"] = 100;
				this.activeLoadingModal.componentProps["isComplete"] = true;
			}
		}
	}

	/**
	 * Hide the loading modal
	 * @param data Optional data to pass back to the caller
	 * @returns Promise that resolves when the modal is hidden
	 */
	async hideLoading(data?: any): Promise<boolean> {
		if (!this.activeLoadingModal) {
			return false;
		}

		await this.activeLoadingModal.dismiss(data);
		this.activeLoadingModal = null;
		return true;
	}

	/**
	 * Close all modals (both notification and loading)
	 */
	async closeAll(): Promise<void> {
		await this.closeNotification();
		await this.hideLoading();
	}

	/**
	 * Retry the current operation without closing the modal
	 * This will hide the status notification and show loading again
	 */
	async retryOperation(): Promise<void> {
		console.log("Retrying operation...");

		// Hide the status notification overlay
		this._isShowStatusNotification.set(false);
		this._statusNotificationOptions.set(null);

		// Show loading again
		this._isLoadingProcessing.set(true);

		// Reset loading modal to show progress again
		if (this.activeLoadingModal && this.activeLoadingModal.componentProps) {
			this.activeLoadingModal.componentProps["progress"] = 0;
			this.activeLoadingModal.componentProps["isComplete"] = false;
		}

		console.log("Loading state reset for retry");
	}

	/**
	 * Retry operation with async execution
	 * This will reset the loading state and execute the provided operation again
	 *
	 * @param options The same options used in the original showLoadingWithOperation
	 * @returns Promise that resolves with the result of the async operation
	 */
	async retryOperationWithExecution<T>(options: CircularProgressLoadingOptions): Promise<T> {
		if (!options.asyncOperation) {
			throw new Error("asyncOperation is required for retryOperationWithExecution");
		}

		console.log("Retrying operation with execution...");

		// Reset the UI state
		await this.retryOperation();

		try {
			// Execute the async operation again
			const result = await options.asyncOperation();
			console.log("Retry API operation completed with result:", result);

			// Set loading as complete
			this.setLoadingComplete();

			// Set status notification options based on result
			if (result === CheckResultType.Success) {
				this._statusNotificationOptions.set(options.onSuccess?.() || null);
			} else if (result === CheckResultType.Warning) {
				this._statusNotificationOptions.set(options.onWarning?.() || null);
			} else if (result === CheckResultType.Failure) {
				this._statusNotificationOptions.set(options.onError?.() || null);
			}

			// Show status notification if options are provided
			if (this._statusNotificationOptions()) {
				this._isShowStatusNotification.set(true);
			}

			console.log(
				"Retry completed - Status notification options:",
				this._statusNotificationOptions(),
			);
			console.log(
				"Retry completed - Is show status notification:",
				this._isShowStatusNotification(),
			);

			return result;
		} catch (error) {
			// Handle error
			console.error("Error in retry operation:", error);

			// Set loading processing to false
			this._isLoadingProcessing.set(false);

			throw error;
		}
	}

	/**
	 * Show loading modal with an async operation
	 * This method will show a loading modal, execute the provided async operation,
	 * and automatically handle the loading state and completion
	 *
	 * @param options Configuration options for the loading modal and operation
	 * @returns Promise that resolves with the result of the async operation
	 */
	async showLoadingWithOperation<T>(options: CircularProgressLoadingOptions): Promise<T> {
		if (!options.asyncOperation) {
			throw new Error("asyncOperation is required for showLoadingWithOperation");
		}

		// Show the loading modal
		const loadingOptions: CircularProgressLoadingOptions = {
			...options,
			// If indeterminateProgress is true, disable autoProgress
			autoProgress: options.indeterminateProgress ? false : (options.autoProgress ?? true),
		};

		this._isLoadingProcessing.set(true);
		await this.showLoading(loadingOptions);

		try {
			// Execute the async operation
			const result = await options.asyncOperation();
			console.log("API operation completed with result:", result);

			// Set loading as complete
			this.setLoadingComplete();

			// If autoClose is enabled, hide the loading modal after the specified delay
			if (options.autoClose) {
				setTimeout(() => {
					this.hideLoading();
				}, options.autoCloseDelay ?? 1000);
			}

			// Set status notification options
			console.log("Result:", result);
			if (result === CheckResultType.Success) {
				this._statusNotificationOptions.set(options.onSuccess?.() || null);
			} else if (result === CheckResultType.Warning) {
				this._statusNotificationOptions.set(options.onWarning?.() || null);
			} else if (result === CheckResultType.Failure) {
				this._statusNotificationOptions.set(options.onError?.() || null);
			}

			// Show status notification if options are provided
			if (this._statusNotificationOptions()) {
				this._isShowStatusNotification.set(true);
			}

			console.log("Status notification options:", this._statusNotificationOptions());
			console.log("Is show status notification:", this._isShowStatusNotification());
			console.log("Is loading processing:", this._isLoadingProcessing());

			return result;
		} catch (error) {
			// Handle error
			console.error("Error in async operation:", error);

			// Hide the loading modal
			await this.hideLoading();

			throw error;
		}
	}
}
