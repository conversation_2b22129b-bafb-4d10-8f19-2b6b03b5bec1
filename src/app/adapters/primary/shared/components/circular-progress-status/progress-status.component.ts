import { CommonModule } from "@angular/common";
import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { IonicModule, ModalController } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { interval, Subscription } from "rxjs";
import { StatusNotificationComponent, StatusNotificationOptions } from "../status-notification/status-notification.component";
import { ProgressStatusService } from "./progress-status.service";

@Component({
	selector: "app-circular-progress-status",
	standalone: true,
	imports: [CommonModule, IonicModule, ButtonModule, StatusNotificationComponent],
	templateUrl: "./progress-status.component.html",
	styleUrls: ["./progress-status.component.scss"],
})
export class CircularProgressStatusComponent implements OnInit, OnDestroy {
	@Input() title: string = "ตรวจสอบข้อมูล";
	@Input() statusMessage: string = "กำลังตรวจสอบข้อมูล";
	@Input() description: string = "อยู่ระหว่างการตรวจสอบข้อมูล\nกรุณารอจนกว่าระบบจะทำงานเสร็จสิ้น";
	@Input() progress: number = 0; // 0 to 100
	@Input() autoProgress: boolean = true;
	@Input() progressDuration: number = 10000; // 10 seconds
	@Input() progressStep: number = 1;
	@Input() showIcon: boolean = false;
	@Input() isComplete: boolean = false;
	@Input() showButtons: boolean = false;
	@Input() cancelButtonLabel: string = "";
	@Input() actionButtonLabel: string = "";
	@Input() onCancel: (() => void) | null = null;
	@Input() onAction: (() => void) | null = null;
	@Input() autoClose: boolean = false;
	@Input() autoCloseDelay: number = 1000; // 1 second
	@Input() indeterminateProgress: boolean = false; // For operations with unknown completion time

	private progressSubscription: Subscription | null = null;
	private circumference: number = 2 * Math.PI * 45; // 2πr where r=45

	constructor(
		private modalController: ModalController,
		public progressStatusService: ProgressStatusService,
	) {}

	ngOnInit() {
		// Don't start auto progress if indeterminate mode is enabled
		if (this.autoProgress && !this.indeterminateProgress) {
			this.startAutoProgress();
		}
	}

	ngOnDestroy() {
		this.stopAutoProgress();
	}

	get progressOffset(): number {
		const offset = this.circumference - (this.progress / 100) * this.circumference;
		return offset;
	}

	startAutoProgress() {
		this.stopAutoProgress();

		const steps = this.progressDuration / (this.progressStep * 100);
		this.progressSubscription = interval(steps).subscribe(() => {
			if (this.progress < 100) {
				this.progress += this.progressStep;
			} else {
				this.progress = 100;
				this.isComplete = true;
				this.stopAutoProgress();

				if (this.autoClose) {
					setTimeout(() => {
						this.dismiss();
					}, this.autoCloseDelay);
				}
			}
		});
	}

	stopAutoProgress() {
		if (this.progressSubscription) {
			this.progressSubscription.unsubscribe();
			this.progressSubscription = null;
		}
	}

	setProgress(value: number) {
		this.progress = Math.min(Math.max(value, 0), 100);
		if (this.progress === 100) {
			this.isComplete = true;
		}
	}

	onCancelClick() {
		if (this.onCancel) {
			this.onCancel();
		} else {
			this.dismiss("cancel");
		}
	}

	onActionClick() {
		if (this.onAction) {
			this.onAction();
		} else {
			this.dismiss("action");
		}
	}

	dismiss(role?: string) {
		this.modalController.dismiss(null, role);
	}

	/**
	 * Get config with properly bound callback functions
	 */
	getBoundConfig(): StatusNotificationOptions | null {
		const config = this.progressStatusService.statusNotificationOptions();
		if (!config) return null;

		return {
			...config,
			onPrimaryAction: config.onPrimaryAction ? config.onPrimaryAction.bind(this) : undefined,
			onSecondaryAction: config.onSecondaryAction ? config.onSecondaryAction.bind(this) : undefined,
		};
	}
}
