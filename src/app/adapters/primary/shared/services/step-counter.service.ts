import { Injectable, computed, effect, signal } from "@angular/core";
import { StepItem } from "../components/step-counter/step-counter.component";

/**
 * Interface for Step Counter State
 */
export interface StepCounterState {
	steps: StepItem[];
	activeStepId: string;
}

/**
 * Service for managing step counter state
 * Uses Angular Signals for reactive state management
 */
@Injectable({
	providedIn: "root",
})
export class StepCounterService {
	// Private state signal
	private state = signal<StepCounterState>({
		steps: [],
		activeStepId: "",
	});

	// Public computed signals for components to consume
	readonly steps = computed(() => this.state().steps);
	readonly activeStepId = computed(() => this.state().activeStepId);
	readonly activeStep = computed(() =>
		this.state().steps.find((step) => step.id === this.state().activeStepId),
	);
	readonly activeStepIndex = computed(() => {
		const activeId = this.state().activeStepId;
		if (!activeId) return -1;
		return this.state().steps.findIndex((step) => step.id === activeId);
	});

	constructor() {
		effect(() => {
			console.log("Step counter state changed:", this.state());
		});
	}

	/**
	 * Initialize steps
	 * @param steps Array of step items
	 * @param activeStepId Optional active step ID
	 */
	initializeSteps(steps: StepItem[], activeStepId?: string): void {
		// Load completed steps from localStorage
		const savedCompletedSteps = JSON.parse(
			localStorage.getItem("completed-steps") || "[]",
		);
		console.log(
			"Loaded completed steps from localStorage:",
			savedCompletedSteps,
		);

		// Mark steps as completed based on input data and localStorage
		// Priority: 1. Input completed status, 2. localStorage data
		const initializedSteps = steps.map((step) => ({
			...step,
			// Respect the input completed status first, then check localStorage
			completed:
				step.completed === true ? true : savedCompletedSteps.includes(step.id),
		}));

		this.state.set({
			steps: initializedSteps,
			activeStepId: activeStepId || (steps.length > 0 ? steps[0].id : ""),
		});
	}

	/**
	 * Initialize loan application steps with default values
	 * @param activeStepId Optional active step ID
	 * @param clearCompletedSteps Whether to clear completed steps from localStorage (default: true)
	 */
	initializeLoanApplicationSteps(
		activeStepId?: string,
		clearCompletedSteps: boolean = true,
	): void {
		// Clear completed steps from localStorage if requested
		// if (clearCompletedSteps) {
		// 	this.clearCompletedSteps();
		// }

		const defaultSteps: StepItem[] = [
			{
				id: "2.1",
				title: "ข้อมูลที่อยู่และการติดต่อ",
				icon: {
					inactive: "assets/svgs/inactive/mailbox.svg",
					active: "assets/svgs/active/mailbox.svg",
				},
				completed: false,
			},
			{
				id: "2.2",
				title: "ข้อมูลการทำงาน",
				icon: {
					inactive: "assets/svgs/inactive/briefcase.svg",
					active: "assets/svgs/active/briefcase.svg",
				},
				completed: false,
			},
			{
				id: "2.3",
				title: "เลือกสินค้าและการผ่อนชำระ",
				icon: {
					inactive: "assets/svgs/inactive/devices.svg",
					active: "assets/svgs/active/devices.svg",
				},
				completed: false,
			},
			{
				id: "2.4",
				title: "แนบเอกสารหลักฐาน",
				icon: {
					inactive: "assets/svgs/inactive/folder.svg",
					active: "assets/svgs/active/folder.svg",
				},
				completed: false,
			},
			{
				id: "2.5",
				title: "ตรวจสอบข้อมูล",
				icon: {
					inactive: "assets/svgs/inactive/search-document.svg",
					active: "assets/svgs/active/search-document.svg",
				},
				completed: false,
			},
		];

		this.initializeSteps(defaultSteps, activeStepId);
	}

	/**
	 * Clear completed steps from localStorage
	 */
	clearCompletedSteps(): void {
		localStorage.removeItem("completed-steps");
		console.log("Cleared completed steps from localStorage");
	}

	/**
	 * Set active step by ID
	 * @param stepId Step ID to set as active
	 * @param updateStatus Whether to update completed status (default: false)
	 */
	setActiveStep(stepId: string, updateStatus: boolean = false): void {
		if (!this.state().steps.some((step) => step.id === stepId)) {
			console.warn(`Step with ID ${stepId} not found`);
			return;
		}

		this.state.update((state) => ({
			...state,
			activeStepId: stepId,
		}));

		// Only update completed status if requested
		// This prevents automatically marking steps as completed when just setting the active step
		if (updateStatus) {
			this.updateCompletedStatus();
		}
	}

	/**
	 * Mark a step as completed
	 * @param stepId Step ID to mark as completed
	 */
	markStepCompleted(stepId: string): void {
		// Update the step in the state
		this.state.update((state) => ({
			...state,
			steps: state.steps.map((step) =>
				step.id === stepId ? { ...step, completed: true } : step,
			),
		}));

		// Also update localStorage to persist completed steps
		const savedCompletedSteps = JSON.parse(
			localStorage.getItem("completed-steps") || "[]",
		);
		if (!savedCompletedSteps.includes(stepId)) {
			savedCompletedSteps.push(stepId);
			localStorage.setItem(
				"completed-steps",
				JSON.stringify(savedCompletedSteps),
			);
		}
	}

	/**
	 * Mark a step as not completed
	 * @param stepId Step ID to mark as not completed
	 */
	markStepNotCompleted(stepId: string): void {
		// Update the step in the state
		this.state.update((state) => ({
			...state,
			steps: state.steps.map((step) =>
				step.id === stepId ? { ...step, completed: false } : step,
			),
		}));
	}

	/**
	 * Update step properties
	 * @param stepId Step ID to update
	 * @param properties Properties to update
	 */
	updateStep(stepId: string, properties: Partial<StepItem>): void {
		this.state.update((state) => ({
			...state,
			steps: state.steps.map((step) =>
				step.id === stepId ? { ...step, ...properties } : step,
			),
		}));
	}

	/**
	 * Update completed status based on active step
	 * This method now respects the explicit completed status of each step
	 * and doesn't automatically mark previous steps as completed
	 */
	updateCompletedStatus(): void {
		// Load completed steps from localStorage
		const savedCompletedSteps = JSON.parse(
			localStorage.getItem("completed-steps") || "[]",
		);

		// Update steps based on localStorage data
		this.state.update((state) => ({
			...state,
			steps: state.steps.map((step) => ({
				...step,
				// Only mark as completed if explicitly saved in localStorage
				completed: savedCompletedSteps.includes(step.id),
			})),
		}));
	}

	/**
	 * Get a step by ID
	 * @param stepId Step ID to get
	 */
	getStep(stepId: string): StepItem | undefined {
		return this.state().steps.find((step) => step.id === stepId);
	}

	/**
	 * Check if a step is completed
	 * @param stepId Step ID to check
	 */
	isStepCompleted(stepId: string): boolean {
		const step = this.getStep(stepId);
		return step?.completed || false;
	}

	/**
	 * Check if a step is active
	 * @param stepId Step ID to check
	 */
	isStepActive(stepId: string): boolean {
		return this.state().activeStepId === stepId;
	}

	/**
	 * Get next step ID
	 * @returns Next step ID or null if there is no next step
	 */
	getNextStepId(): string | null {
		const activeIndex = this.activeStepIndex();
		if (activeIndex < 0 || activeIndex >= this.state().steps.length - 1) {
			return null;
		}
		return this.state().steps[activeIndex + 1].id;
	}

	/**
	 * Get previous step ID
	 * @returns Previous step ID or null if there is no previous step
	 */
	getPreviousStepId(): string | null {
		const activeIndex = this.activeStepIndex();
		if (activeIndex <= 0) {
			return null;
		}
		return this.state().steps[activeIndex - 1].id;
	}

	/**
	 * Move to next step
	 * @param updateStatus Whether to update completed status (default: true)
	 * @returns True if moved to next step, false otherwise
	 */
	moveToNextStep(updateStatus: boolean = true): boolean {
		const nextStepId = this.getNextStepId();
		if (!nextStepId) {
			return false;
		}
		this.setActiveStep(nextStepId, updateStatus);
		return true;
	}

	/**
	 * Move to previous step
	 * @param updateStatus Whether to update completed status (default: true)
	 * @returns True if moved to previous step, false otherwise
	 */
	moveToPreviousStep(updateStatus: boolean = true): boolean {
		const prevStepId = this.getPreviousStepId();
		if (!prevStepId) {
			return false;
		}
		this.setActiveStep(prevStepId, updateStatus);
		return true;
	}
}
