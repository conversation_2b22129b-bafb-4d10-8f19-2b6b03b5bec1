import { Injectable, signal } from "@angular/core";
import { Platform } from "@ionic/angular";
import {
	BrowserInfo,
	BrowserName,
	DeviceInfo,
	OSInfo,
	OSName,
	UAInfo,
} from "ua-info";

export interface DeviceCheckResult {
	passed: boolean;
	details: CheckDetail[];
}

export type CheckType = "os" | "hardware" | "browser" | "targetBrowser";

export interface CheckDetail {
	type: CheckType;
	name: string;
	passed: boolean;
	current: number | string;
	required: number | string;
	data?: {
		targetBrowser?: string;
		targetBrowserUrl?: string;
	};
	message?: string;
}

export interface DeviceRequirement {
	osVersion: number;
	cpuCores: number;
	ramSize: number;
	browserVersions: Partial<Record<BrowserName, number>>;
}

export type DeviceRequirements = Partial<Record<OSName, DeviceRequirement>>;

export const DEVICE_REQUIREMENTS: DeviceRequirements = {
	iOS: {
		osVersion: 16,
		cpuCores: 4,
		ramSize: 0,
		browserVersions: {
			"Safari": 16,
			"Safari Mobile": 16,
			"Chrome": 112,
			"Chrome Mobile": 112,
		},
	},
	Android: {
		osVersion: 9,
		cpuCores: 8,
		ramSize: 2,
		browserVersions: {
			"Chrome": 112,
			"Chrome Mobile": 112,
			"Edge": 126,
			"Firefox": 126,
			"Samsung Internet": 20,
			"Opera": 80,
			"Huawei Browser": 14,
		},
	},
	MacOS: {
		osVersion: 10,
		cpuCores: 4,
		ramSize: 0,
		browserVersions: {
			"Safari": 16,
			"Chrome": 112,
			"Chrome Mobile": 112,
		},
	},
	Windows: {
		osVersion: 10,
		cpuCores: 4,
		ramSize: 4,
		browserVersions: {
			"Chrome": 112,
			"Chrome Mobile": 112,
			"Edge": 126,
			"Firefox": 126,
			"Samsung Internet": 20,
			"Opera": 80,
		},
	},
};

@Injectable({
	providedIn: "root",
})
export class UaInfoService {
	private readonly uaInfo = new UAInfo();
	private readonly _isMobileDesktopMode = signal<boolean>(false);
	readonly isMobileDesktopMode = this._isMobileDesktopMode.asReadonly();

	constructor(private readonly platform: Platform) {
		this.initUserAgent();
		this.checkDesktopMode();
		window.addEventListener("resize", () => this.checkDesktopMode());
	}

	private checkDesktopMode(): void {
		const isDesktopMode = this.detectMobileDesktopMode();
		this._isMobileDesktopMode.set(isDesktopMode);
	}

	private detectMobileDesktopMode(): boolean {
		const userAgent = navigator.userAgent || "";
		const isMobile = this.uaInfo.isMobile() || this.platform.is("mobile");
		const isTablet = this.uaInfo.isTablet() || this.platform.is("tablet");

		if (userAgent.includes("Linux x86_64")) {
			return true;
		}

		const screenWidth = window.innerWidth;
		const viewportWidth = document.documentElement.clientWidth;

		const viewportRatio = viewportWidth / screenWidth;
		const isPortrait = this.platform.isPortrait();

		if (isMobile && screenWidth > 850 && isPortrait) {
			return true;
		}

		if (isTablet && screenWidth > 900 && isPortrait) {
			return true;
		}

		if (isMobile && viewportRatio < 0.8) {
			return true;
		}

		try {
			const viewport = document.querySelector('meta[name="viewport"]');
			const viewportContent = viewport?.getAttribute("content") || "";
			const hasInitialScale = viewportContent.includes("initial-scale=1.0");
			if (!hasInitialScale) {
				return true;
			}
		} catch (error) {
			return true;
		}

		const aspectRatio = screenWidth / window.innerHeight;
		if (isMobile && aspectRatio > 1.8 && isPortrait) {
			return true;
		}

		return false;
	}

	public getCurrentStatus(): boolean {
		return this._isMobileDesktopMode();
	}

	private initUserAgent(): void {
		this.uaInfo.setUserAgent(navigator.userAgent);
	}

	public isMobile(): boolean {
		return this.uaInfo.isMobile();
	}

	public isTablet(): boolean {
		return this.uaInfo.isTablet();
	}

	public isDesktop(): boolean {
		return this.uaInfo.isDesktop();
	}

	public isOS(names: OSName | OSName[]): boolean {
		return this.uaInfo.isOS(names);
	}

	public isIPad(): boolean {
		return this.uaInfo.isIPad();
	}

	public isBrowser(names: BrowserName | BrowserName[]): boolean {
		return this.uaInfo.isBrowser(names);
	}

	public isInAppBrowser(): boolean {
		return this.uaInfo.isInAppBrowser();
	}

	public getBrowser(): BrowserInfo {
		return this.uaInfo.getBrowser();
	}

	public getOS(): OSInfo {
		return this.uaInfo.getOS();
	}

	public getDevice(): DeviceInfo {
		return this.uaInfo.getDevice();
	}

	public getCpuCoreCount(): number {
		return this.uaInfo.getCpuCoreCount();
	}

	public getMemory(): number {
		return this.uaInfo.getMemory();
	}

	public isOSVersionAtLeast(version: string): boolean {
		return this.uaInfo.isOSVersionAtLeast(version);
	}

	public isBrowserVersionAtLeast(version: string): boolean {
		return this.uaInfo.isBrowserVersionAtLeast(version);
	}

	/**
	 * ===============================================
	 * CHECKERS
	 * ===============================================
	 */

	private readonly requirements = DEVICE_REQUIREMENTS;

	/**
	 * Performs a complete device compatibility check
	 */
	async checkOSAndHardwareCompatibility(): Promise<DeviceCheckResult> {
		const os = this.getOS().name as OSName;
		const requirements = this.requirements[os];
		if (!requirements) {
			return this.createFailResult(`Unsupported OS: ${os}`);
		}

		const checks = await this.performChecks(os, requirements);
		return this.createResult(checks);
	}

	/**
	 * Performs a complete device compatibility check
	 */
	async checkBrowserCompatibility(): Promise<DeviceCheckResult> {
		const os = this.getOS().name as OSName;
		const requirements = this.requirements[os];

		if (!requirements) {
			return this.createFailResult(`Unsupported OS: ${os}`);
		}

		const checks = await this.performBrowserChecks(os, requirements);

		return this.createResult(checks);
	}

	/**
	 * Performs all required compatibility checks
	 */
	private async performBrowserChecks(
		os: OSName,
		requirements: DeviceRequirement,
	): Promise<CheckDetail[]> {
		const browserCheck = this.checkBrowser(os, requirements);
		const targetBrowser = this.checkTargetBrowserCompatibility();
		return [browserCheck, targetBrowser];
	}

	/**
	 * Performs all required compatibility checks
	 */
	private async performChecks(
		os: OSName,
		requirements: DeviceRequirement,
	): Promise<CheckDetail[]> {
		const osCheck = this.checkOS(requirements);
		const hardwareChecks = this.checkHardware(os, requirements);
		return [osCheck, ...hardwareChecks];
	}

	/**
	 * Checks OS version compatibility
	 */
	private checkOS(requirements: DeviceRequirement): CheckDetail {
		const currentVersion = this.getOS().version;
		const requiredVersion = requirements.osVersion;
		const passed = this.isOSVersionAtLeast(requiredVersion.toString());

		return {
			type: "os",
			name: "Operating System Version",
			passed,
			current: currentVersion,
			required: requiredVersion,
			message: passed
				? undefined
				: `Requires OS version ${requiredVersion} or higher`,
		};
	}

	/**
	 * Checks browser compatibility
	 */
	private checkBrowser(
		os: OSName,
		requirements: DeviceRequirement,
	): CheckDetail {
		const browser = this.getBrowser();
		const versions = requirements.browserVersions;
		const requiredVersion = versions[browser.name as BrowserName];
		const passed = this.isBrowserCompatible(os, versions);

		return {
			type: "browser",
			name: "Browser Version",
			passed,
			current: browser.version,
			required: requiredVersion || "N/A",
			message: passed ? undefined : `Unsupported browser or version`,
		};
	}

	/**
	 * Checks browser version compatibility
	 */
	private isBrowserCompatible(
		os: OSName,
		versions: Partial<Record<BrowserName, number>>,
	): boolean {
		const browser = this.getBrowser();

		if (
			["ios", "macos"].includes(os) &&
			this.isBrowser(["Safari", "Safari Mobile"])
		) {
			return this.checkBrowserVersion(versions, "Safari");
		}

		return browser.name
			? this.checkBrowserVersion(versions, browser.name)
			: false;
	}

	private checkBrowserVersion(
		versions: Partial<Record<BrowserName, number>>,
		browserName: BrowserName,
	): boolean {
		const requiredVersion = versions[browserName];
		return requiredVersion
			? this.isBrowserVersionAtLeast(requiredVersion.toString())
			: false;
	}

	/**
	 * Checks hardware requirements
	 */
	private checkHardware(
		os: OSName,
		requirements: DeviceRequirement,
	): CheckDetail[] {
		const hardwareChecks: CheckDetail[] = [];

		if (os === "iOS" || os === "MacOS") {
			hardwareChecks.push(
				this.createCheckDetail(
					"hardware",
					"CPU Cores",
					true,
					this.getCpuCoreCount() || 0,
					requirements.cpuCores,
					undefined,
				),
				this.createCheckDetail(
					"hardware",
					"RAM",
					true,
					this.getMemory() || 0,
					requirements.ramSize,
					undefined,
				),
			);
		} else {
			const cpuCores = this.getCpuCoreCount() || 0;
			const passed = cpuCores >= requirements.cpuCores;

			hardwareChecks.push(
				this.createCheckDetail(
					"hardware",
					"CPU Cores",
					passed,
					cpuCores,
					requirements.cpuCores,
					passed
						? undefined
						: `Requires ${requirements.cpuCores} CPU cores minimum`,
				),
			);

			if (requirements.ramSize !== null) {
				const memory = this.getMemory() || 0;
				const passed = memory >= requirements.ramSize;

				hardwareChecks.push(
					this.createCheckDetail(
						"hardware",
						"RAM",
						passed,
						memory,
						requirements.ramSize,
						passed
							? undefined
							: `Requires ${requirements.ramSize}GB RAM minimum`,
					),
				);
			}
		}

		return hardwareChecks;
	}

	/**
	 * ===============================================
	 * HELPERS FUNCTIONS
	 * ===============================================
	 */

	private createCheckDetail(
		type: CheckType,
		name: string,
		passed: boolean,
		current: number,
		required: number,
		message?: string,
	): CheckDetail {
		return {
			type,
			name,
			passed,
			current,
			required,
			message,
		};
	}

	private checkTargetBrowserCompatibility(): CheckDetail {
		const browser = this.getBrowser();
		const url = window.location.href.replace(/^https?:\/\//, "");

		const isAndroid = this.isOS("Android");
		const isIOS = this.isOS("iOS") || this.isOS("MacOS");
		const isChrome =
			this.isBrowser("Chrome") || this.isBrowser("Chrome Mobile");
		const isHuawei =
			this.isOS("HarmonyOS") || this.getDevice().vendor === "Huawei";
		const isHuaweiBrowser = this.isBrowser("Huawei Browser");
		const passed = (isAndroid && (isChrome || isHuaweiBrowser)) || isIOS;

		let targetBrowser = "";
		let targetBrowserUrl = "";

		if (isAndroid) {
			if (isHuawei) {
				targetBrowser = "Huawei Browser";
				targetBrowserUrl = `intent://${url}#Intent;scheme=https;package=com.huawei.browser;S.browser_fallback_url=${encodeURIComponent(
					window.location.href,
				)};end`;
			} else {
				targetBrowser = "Chrome";
				targetBrowserUrl = `intent://${url}#Intent;scheme=https;package=com.android.chrome;end`;
			}
		} else if (isIOS) {
			targetBrowser = "Safari";
			targetBrowserUrl = `x-safari-https://${url}`;
		}

		return {
			type: "targetBrowser",
			name: "Target Browser Compatibility",
			passed,
			current: browser.version,
			required: targetBrowser,
			data: {
				targetBrowser,
				targetBrowserUrl,
			},
			message: passed ? undefined : "Unsupported browser",
		};
	}

	private createResult(checks: CheckDetail[]): DeviceCheckResult {
		return {
			passed: checks.every((check) => check.passed),
			details: checks,
		};
	}

	private createFailResult(message: string): DeviceCheckResult {
		return {
			passed: false,
			details: [
				{
					type: "os",
					name: "System Check",
					passed: false,
					current: "N/A",
					required: "N/A",
					message,
				},
			],
		};
	}
}
