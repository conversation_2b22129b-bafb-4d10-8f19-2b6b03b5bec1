import { inject, Injectable } from "@angular/core";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { DeviceManagerUtils } from "src/app/core/utils/device-manager-utils";
import { Resolution, Webcam, WebcamConfiguration } from "ts-webcam";
import { UaInfoService } from "./ua-info.service";

// Types and Interfaces

export interface CameraHandlerOptions {
	onStart?: () => void;
	onError?: (error: CameraError) => void;
}

export interface CameraError {
	message: string;
	code?: string;
	originalError?: unknown;
}

// Constants
const CAMERA_PERMISSION_MESSAGES = {
	DENIED: {
		header: "ไม่สามารถเข้าถึงกล้องได้",
		message: "กรุณาอนุญาตการใช้งานกล้องเพื่อใช้ฟีเจอร์นี้",
		acceptLabel: "ตกลง",
	},
	NO_CAMERA: {
		type: "danger" as const,
		message: "ไม่พบกล้อง กรุณาตรวจสอบการเชื่อมต่อกล้อง",
	},
	NO_PREVIEW: {
		type: "danger" as const,
		message: "ไม่พบ preview element กรุณาตรวจสอบการเชื่อมต่อกล้อง",
	},
} as const;

export const DEFAULT_RESOLUTIONS = [
	{ name: "SQUARE-1920", width: 1920, height: 1920 },
	{ name: "SQUARE-1080", width: 1080, height: 1080 },
	{ name: "SQUARE-720", width: 720, height: 720 },
] as const;

@Injectable({
	providedIn: "root",
})
export class WebcamService {
	private readonly appUtilsService = inject(AppUtilsService);
	private readonly uaInfoService = inject(UaInfoService);
	private readonly deviceManagerUtils = new DeviceManagerUtils();

	public readonly webcam = new Webcam();
	private previewElement: HTMLVideoElement | null = null;

	/**
	 * Get webcam instance for direct access
	 */
	get instance() {
		return this.webcam;
	}

	/**
	 * Get current webcam status
	 */
	public getStatus(): string {
		return this.webcam.getStatus();
	}

	/**
	 * Attach video element for camera preview
	 */
	public attachPreviewElement(element: HTMLVideoElement): void {
		this.previewElement = element;
	}

	/**
	 * Check camera permission using ts-webcam
	 * @returns Permission state from ts-webcam library
	 */
	async checkCameraPermission(): Promise<string> {
		return await this.webcam.checkCameraPermission();
	}

	/**
	 * Check camera permission (legacy method for backward compatibility)
	 * @returns PermissionStatus object
	 */
	async checkCameraPermissionLegacy(): Promise<PermissionStatus> {
		const permission = await this.checkCameraPermission();
		return { state: permission } as PermissionStatus;
	}

	async requestCameraPermissions(type: "camera" | "microphone"): Promise<boolean> {
		const permissions = await this.webcam.requestPermissions();
		if (permissions[type] !== "granted") {
			throw new Error(
				`${type === "camera" ? "กล้องถ่ายรูป" : "ไมโครโฟน"}ไม่ได้รับอนุญาตให้เข้าถึง กรุณาอนุญาตการเข้าถึงในการตั้งค่าเบราว์เซอร์`,
			);
		}

		return true;
	}

	async checkCameraCapabilities(deviceId: string): Promise<boolean> {
		const devices = await this.webcam.getVideoDevices();
		const device = devices.find((device) => device.deviceId === deviceId);
		if (!device) {
			throw new Error("No camera device found");
		}

		const capabilities = await this.webcam.getDeviceCapabilities(device?.deviceId);

		// Check if specific resolutions are supported
		const desiredResolutions: Resolution[] = [
			this.webcam.createResolution("SQUARE-720", 720, 720),
			this.webcam.createResolution("SQUARE-1080", 1080, 1080),
			this.webcam.createResolution("SQUARE-1920", 1920, 1920),
		];

		const supportInfo = this.webcam.checkSupportedResolutions([capabilities], desiredResolutions);

		// some resolutions are supported
		const isSupportedResolutions = supportInfo.resolutions.some((resolution) => {
			return resolution.supported;
		});

		return isSupportedResolutions;
	}

	/**
	 * Stop camera and release resources
	 */
	public async stopCamera(): Promise<void> {
		try {
			this.webcam.stop();
		} catch (error) {
			console.warn("Error stopping camera:", error);
		}
	}

	/**
	 * Take photo with current camera
	 */
	public async takePhoto(options?: {
		imageType?: "image/jpeg" | "image/png";
		quality?: number;
		scale?: number;
	}): Promise<string | null> {
		try {
			return this.webcam.captureImage(options);
		} catch (error) {
			console.error("Error taking photo:", error);
			return null;
		}
	}

	/**
	 * Create camera configuration with custom resolutions
	 */
	public async createCameraConfiguration(
		cameraType: "environment" | "user" = "environment",
		customResolutions?: Array<{ name: string; width: number; height: number }>,
		_options: CameraHandlerOptions = {},
	): Promise<WebcamConfiguration | null> {
		const videoDevices = await this.webcam.getVideoDevices();
		const selectedCamera = await this.deviceManagerUtils.selectCamera(videoDevices, cameraType);

		if (!selectedCamera) {
			await this.appUtilsService.showError(
				CAMERA_PERMISSION_MESSAGES.NO_CAMERA.type,
				CAMERA_PERMISSION_MESSAGES.NO_CAMERA.message,
			);
			return null;
		}

		if (!this.previewElement) {
			await this.appUtilsService.showError(
				CAMERA_PERMISSION_MESSAGES.NO_PREVIEW.type,
				CAMERA_PERMISSION_MESSAGES.NO_PREVIEW.message,
			);
			return null;
		}

		const resolutions = customResolutions || DEFAULT_RESOLUTIONS;
		const resolutionPacks = resolutions.map((config) =>
			this.webcam.createResolution(config.name, config.width, config.height),
		);
		return {
			enableAudio: false,
			deviceInfo: selectedCamera,
			mirrorVideo: this.deviceManagerUtils.isFrontCamera(selectedCamera),
			videoElement: this.previewElement,
			allowFallbackResolution: false,
			allowAutoRotateResolution: this.shouldAllowAutoRotate(),
			preferredResolutions: resolutionPacks,
			debug: true,
		};
	}

	/**
	 * Initialize camera with configuration
	 */
	public async startCamera(
		configuration: WebcamConfiguration,
		options: CameraHandlerOptions = {},
	): Promise<void> {
		const webcamConfig = {
			...configuration,
			onStart: () => {
				options.onStart?.();
			},
			onError: (error: any) => {
				options.onError?.(error);
			},
		};

		this.webcam.setupConfiguration(webcamConfig);
		await this.webcam.start();
	}

	private shouldAllowAutoRotate(): boolean {
		return this.uaInfoService.isMobile() || this.uaInfoService.isTablet();
	}
}
