import { Injectable } from "@angular/core";
import {
	assertThaiId,
	PASSPORT_CARD_REGEX,
	THAI_DRIVING_LICENSE_REGEX,
	THAI_ID_APPLE_FRONT_REGEX,
	THAI_ID_CARD_REGEX,
} from "../constants/ocr.expression";

@Injectable({
	providedIn: "root",
})
export class CardRegExpService {
	constructor() {}

	private safeExecute<T>(fn: () => T, defaultValue: T): T {
		try {
			return fn();
		} catch (error) {
			console.error("An error occurred during OCR processing:", error);
			return defaultValue;
		}
	}

	// =============================================================================
	// Thai ID
	// =============================================================================

	public extractThaiIdNumber(text: string): string {
		return this.extractIdNumber(text, THAI_ID_CARD_REGEX.DIGIT);
	}

	public extractLaserIdNumber(text: string): string {
		const cleanedText = this.cleanLaserIdText(text);
		return this.extractIdNumber(
			cleanedText,
			THAI_ID_CARD_REGEX.DIGIT_BACK_CARD,
		);
	}

	public cleanLaserIdText(text: string): string {
		return text.replace(/[-—–―‒ ]/g, "");
	}

	public cleanFormatLaserCode(laserCode: string): string {
		return laserCode.replace(/-/g, "").replace(/^(.{2})[Oo]/, "$10");
	}

	private extractIdNumber(text: string, regex: RegExp): string {
		const cleanedText = text.replaceAll(/ /g, "");
		if (!cleanedText) return "";

		const match = cleanedText.match(regex);
		return match ? match[0] : "";
	}

	public validThaiIdNumber(text: string): boolean {
		return this.safeExecute(() => assertThaiId(text), false);
	}

	public checkAndValidateLaserId(laserId?: string): string | undefined {
		if (!laserId) return undefined;

		let cleanLaserId = laserId.replace(/-/g, "");
		if (cleanLaserId.charAt(2) === "O" || cleanLaserId.charAt(2) === "o") {
			cleanLaserId = cleanLaserId.slice(0, 2) + "0" + cleanLaserId.slice(3);
		}

		return cleanLaserId;
	}

	// =============================================================================
	// Detect Words For Thai ID
	// =============================================================================

	public detectThaiWords(text: string): string[] {
		return this.detectWords(text, THAI_ID_CARD_REGEX.WORDS_THAI);
	}

	public detectEnglishWords(text: string): string[] {
		return this.detectWords(text, THAI_ID_CARD_REGEX.WORDS_ENG);
	}

	public detectEngWordsApple(text: string): string[] {
		return this.detectWords(text, THAI_ID_APPLE_FRONT_REGEX.WORDS);
	}

	private detectWords(text: string, regex: RegExp): string[] {
		return text.match(regex) || [];
	}

	public findTextAfterID(text: string, idNumber: string): string {
		if (!idNumber || !text) {
			return "";
		}

		// หาตำแหน่งของเลขบัตรในข้อความ
		const cleanText = text.replace(/ /g, "");
		const idPosition = cleanText.indexOf(idNumber);

		if (idPosition === -1) {
			return "";
		}

		// ตัดเอาเฉพาะข้อความหลังเลขบัตร
		return text.substring(idPosition + idNumber.length);
	}

	// =============================================================================
	// Extract Passport
	// =============================================================================

	public extractPassportInfo = (text: string, type: string): string[] => {
		try {
			if (type === "ALL_MRZ") {
				const all_mrz = text?.match(PASSPORT_CARD_REGEX.ALL_MRZ);
				if (all_mrz) {
					let mrz = [];
					for (let i = 0; i < all_mrz.length; i++) {
						if (all_mrz[i]?.includes("<")) {
							mrz.push(all_mrz[i]);
						}
					}
					return mrz;
				}
			} else if (type === "MRZ1") {
				const mrz1_match = text?.match(PASSPORT_CARD_REGEX.MRZ_LINE_1);
				if (mrz1_match) {
					return mrz1_match;
				}
			} else if (type === "MRZ2") {
				const mrz2_match = text?.match(PASSPORT_CARD_REGEX.MRZ_LINE_2);
				if (mrz2_match) {
					return mrz2_match;
				}
			} else if (type === "Date") {
				const date_match = text?.match(PASSPORT_CARD_REGEX.Date);
				if (date_match) {
					return date_match;
				}
			} else if (type === "DATA") {
				let modifiedData = text?.match(/^(.*?)(P<[^<]*)/s); //use[1]
				const data_match = modifiedData?.[1]?.match(PASSPORT_CARD_REGEX.DATA);
				if (data_match) {
					return data_match;
				}
			} else if (type === "TITLE") {
				const title_match = text?.match(PASSPORT_CARD_REGEX.TITLE);
				if (title_match) {
					return title_match;
				}
			} else {
				return [];
			}
		} catch (error) {
			console.error(
				"An error occurred during passport information extraction:",
				error,
			);
			return [];
		}
		return []; // Add this line
	};

	private readonly MONTHS_MAP: { [key: string]: number } = {
		Jan: 0,
		Feb: 1,
		Mar: 2,
		Apr: 3,
		May: 4,
		Jun: 5,
		Jul: 6,
		Aug: 7,
		Sep: 8,
		Oct: 9,
		Nov: 10,
		Dec: 11,
	};

	public findDrivingLicenseWords(str: string): string[] {
		return this.safeExecute(() => {
			return str.match(THAI_DRIVING_LICENSE_REGEX.ENG_WORD) || [];
		}, []);
	}

	public findDrivingLicenseIDNumber(str: string): string {
		return this.safeExecute(() => {
			const cleanStr = str.replace(/\s+/g, "");
			const match = cleanStr.match(THAI_DRIVING_LICENSE_REGEX.DIGIT);
			return match?.[0] || "";
		}, "");
	}

	public findDrivingLicenseNumber(str: string): string {
		return this.safeExecute(() => {
			const cleanStr = str.replace(/\s+/g, "");
			const match = cleanStr.match(THAI_DRIVING_LICENSE_REGEX.NUMBER);
			return match?.[0] || "";
		}, "");
	}

	public findDateFormat(str: string): boolean {
		return this.safeExecute(() => {
			const cleanStr = str.replace(/\s+/g, "");
			const match = cleanStr.match(THAI_DRIVING_LICENSE_REGEX.DATE_FORMAT);
			if (match) {
				const [, dayStr, monthStr, yearStr] = match;
				const day = parseInt(dayStr, 10);
				const month =
					this.MONTHS_MAP[monthStr.slice(0, 3) as keyof typeof this.MONTHS_MAP];
				const year = parseInt(yearStr, 10);
				return this.isValidDate(day, month, year);
			}
			return false;
		}, false);
	}

	public findDrivingLicenseName(str: string): string {
		return this.safeExecute(() => {
			const clean = str.replace(/\s+/g, "");
			const matches = clean.match(THAI_DRIVING_LICENSE_REGEX.NAME) || [];
			const longWords = matches.filter((word: string) => word.length > 10);
			return longWords[0] || "";
		}, "");
	}

	private isValidDate(day: number, month: number, year: number): boolean {
		const date = new Date(year, month, day);
		return (
			date.getFullYear() === year &&
			date.getMonth() === month &&
			date.getDate() === day
		);
	}
}
