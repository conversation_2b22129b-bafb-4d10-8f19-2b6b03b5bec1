import { Injectable } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

@Injectable({
  providedIn: "root",
})
export class NumpadService {
  isError = false;

  constructor() { }

  phoneNumberFormGroup = new FormGroup({
    0: new FormControl({ value: "", disabled: true }, Validators.required),
    1: new FormControl({ value: "", disabled: true }, Validators.required),
    2: new FormControl({ value: "", disabled: true }, Validators.required),
    3: new FormControl({ value: "", disabled: true }, Validators.required),
    4: new FormControl({ value: "", disabled: true }, Validators.required),
    5: new FormControl({ value: "", disabled: true }, Validators.required),
    6: new FormControl({ value: "", disabled: true }, Validators.required),
    7: new FormControl({ value: "", disabled: true }, Validators.required),
    8: new FormControl({ value: "", disabled: true }, Validators.required),
    9: new FormControl({ value: "", disabled: true }, Validators.required),
  });

  RegisFormGroup = new FormGroup({
    0: new FormControl({ value: "", disabled: true }, Validators.required),
    1: new FormControl({ value: "", disabled: true }, Validators.required),
    2: new FormControl({ value: "", disabled: true }, Validators.required),
    3: new FormControl({ value: "", disabled: true }, Validators.required),
    4: new FormControl({ value: "", disabled: true }, Validators.required),
    5: new FormControl({ value: "", disabled: true }, Validators.required),
    6: new FormControl({ value: "", disabled: true }, Validators.required),
  });

  setPhoneNumber(value: any) {
    value = value.split("");
    value.forEach((element: any, index: any) => {
      this.phoneNumberFormGroup.get(index.toString())?.setValue(element);
    });
  }

  setRegis(value: any) {
    value = value.split("");
    value.forEach((element: any, index: any) => {
      this.RegisFormGroup.get(index.toString())?.setValue(element);
    });
  }

  clearPhoneNumber() {
    for (let i = 0; i <= 9; i++) {
      this.phoneNumberFormGroup.get(i.toString())?.setValue("");
    }
  }

  clearRegis() {
    for (let i = 0; i <= 6; i++) {
      this.RegisFormGroup.get(i.toString())?.setValue("");
    }
  }

  clearZero() {
    if (this.phoneNumberFormGroup.get("0")?.value === "0") {
      this.clearPhoneNumber();
    }
  }
}
