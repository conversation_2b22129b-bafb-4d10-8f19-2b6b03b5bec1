export const THAI_ID_CARD_REGEX = {
	WORDS_ENG: new RegExp(
		/(Thai|National|ID|Card|Identification|Number|N[e|a]me|Last[ |]n[e|a]me|Expiry|Issue|D(\w+|)[ ](\w+|)f[ ][B|h](\w+|)[ ](\d{1,2}[ |])([[J|j]an[.| |]|[F|f]eb[.| |]|[M|m]ar[.| |]|[A|a]pr[.| |]|[M|m]ay[.| |]|[J|j]un[.| |]|[J|j]ul[.| |]|[A|a]ug[.| |]|[S|s]ep[.| |]|[O|o]ct[.| |]|[N|n]ov[.| |]|[D|d]ec[.| |]])[ |][1|2]\d{3})/g,
	),
	WORDS_THAI: new RegExp(
		/(บัตรประจำตัวประชาชน|ประชาชน|นาย|นาง|และ|ศาสนา|วันบัตร|ออกบัตร|หมดอายุ|Mr.|Thai|N[e|a]tional|ID|Card|Identification|Number|N[e|a]me|Last[ |]n[e|a]me|Ex[p|g|q]iry|Issue|(เกิดวันที่ \d{1,2} [ก-ฮ].[ก-ฮ]. 2[4|5]\d{2})|(แขวง)|(เขต)|(ที่อยู่)|D(\w+|)[ ](\w+|)f[ ][B|h](\w+|)[ ](\d{1,2}[ |])([[J|j]an[.| |]|[F|f]eb[.| |]|[M|m]ar[.| |]|[A|a]pr[.| |]|[M|m]ay[.| |]|[J|j]un[.| |]|[J|j]ul[.| |]|[A|a]ug[.| |]|[S|s]ep[.| |]|[O|o]ct[.| |]|[N|n]ov[.| |]|[D|d]ec[.| |]])[ |][1|2]\d{3}|([0-9][ -]\d{4}[ -]\d{5}[ -]\d{2}[ -]\d)|(\d{4}[ -]\d{2}[ -]\d{8}))/g,
	),
	DIGIT: new RegExp(/[1-8]\d{12}/g),
	DIGIT_SIGN: new RegExp(/[1-8][ -]\d{4}[ -]\d{5}[ -]\d{2}[ -]\d/g),
	DIGIT_BACK_CARD: new RegExp(/[A-Z]{2}[O|o|0-9]\d{9}/g),
	THAI_ID: new RegExp(/(\d{1}( |)\d{4}( |)\d{5}( |)\d{2}( |)\d{1})/g),
	KEYWORD_AFTER_ID: new RegExp(
		/(N[e|a]me|Last[ |]n[e|a]me|Mr[.]|Mrs[.]|Miss|D[a|e]te[ ](\w+|)f[ ][B|h](\w+|)[ ](\d{1,2}[ |])([[J|j]an[.| |]|[F|f]eb[.| |]|[M|m]ar[.| |]|[A|a]pr[.| |]|[M|m]ay[.| |]|[J|j]un[.| |]|[J|j]ul[.| |]|[A|a]ug[.| |]|[S|s]ep[.| |]|[O|o]ct[.| |]|[N|n]ov[.| |]|[D|d]ec[.| |]])[ |][1|2]\d{3})/g,
	),
};

export const THAI_ID_MLKIT_FRONT_REGEX = {
	WORDS: new RegExp(
		/(Thai|National|ID|Card|Identification|Number|N[e|a]me|Last[ |]n[e|a]me|Expiry|Issue|D(\w+|)[ ](\w+|)f[ ][B|h](\w+|)[ ](\d{1,2}[ |])([[J|j]an[.| |]|[F|f]eb[.| |]|[M|m]ar[.| |]|[A|a]pr[.| |]|[M|m]ay[.| |]|[J|j]un[.| |]|[J|j]ul[.| |]|[A|a]ug[.| |]|[S|s]ep[.| |]|[O|o]ct[.| |]|[N|n]ov[.| |]|[D|d]ec[.| |]])[ |][1|2]\d{3})/g,
	),
	DIGIT: new RegExp(/[1-8]\d{12}/g),
	DIGIT_SIGN: new RegExp(/[1-8][ -]\d{4}[ -]\d{5}[ -]\d{2}[ -]\d/g),
	DIGIT_BACK_CARD: new RegExp(/[A-Z]{2}[O|o|0-9]\d{9}/g),
	THAI_ID: new RegExp(/(\d{1}( |)\d{4}( |)\d{5}( |)\d{2}( |)\d{1})/g),
};

export const THAI_ID_APPLE_FRONT_REGEX = {
	WORDS: new RegExp(
		/(บัตรประจำตัวประชาชน|ประชาชน|นาย|นาง|และ|ศาสนา|วันบัตร|ออกบัตร|หมดอายุ|Mr.|Thai|N[e|a]tional|ID|Card|Identification|Number|N[e|a]me|Last[ |]n[e|a]me|Ex[p|g|q]iry|Issue|(เกิดวันที่ \d{1,2} [ก-ฮ].[ก-ฮ]. 2[4|5]\d{2})|(แขวง)|(เขต)|(ที่อยู่)|D(\w+|)[ ](\w+|)f[ ][B|h](\w+|)[ ](\d{1,2}[ |])([[J|j]an[.| |]|[F|f]eb[.| |]|[M|m]ar[.| |]|[A|a]pr[.| |]|[M|m]ay[.| |]|[J|j]un[.| |]|[J|j]ul[.| |]|[A|a]ug[.| |]|[S|s]ep[.| |]|[O|o]ct[.| |]|[N|n]ov[.| |]|[D|d]ec[.| |]])[ |][1|2]\d{3}|([0-9][ -]\d{4}[ -]\d{5}[ -]\d{2}[ -]\d)|(\d{4}[ -]\d{2}[ -]\d{8}))/g,
	),
	DIGIT: new RegExp(/[1-8]\d{12}/g),
	DIGIT_SIGN: new RegExp(/[1-8][ -]\d{4}[ -]\d{5}[ -]\d{2}[ -]\d/g),
	DIGIT_BACK_CARD: new RegExp(/[A-Z]{2}[O|o|0-9]\d{9}/g),
	THAI_ID: new RegExp(/(\d{1}( |)\d{4}( |)\d{5}( |)\d{2}( |)\d{1})/g),
};

export const PASSPORT_CARD_REGEX = {
	Date: new RegExp(
		/\d{2}(JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)\d{4}/gi,
	),
	MRZ1: new RegExp(
		/([P][<A-Z0-9]{1})([A-Z]{3})(([A-Z]*<){2})(([A-Z]*<|[A-Z]*){3})(<*)/,
	),
	MRZ2: new RegExp(
		/([A-Z0-9<]{9})([0-9])([A-Z<]{3})([0-9]{6})([0-9])([MNF<])([0-9]{6})([0-9])([A-Z0-9<]{14})([0-9<])([0-9])/,
	),
	TITLE: new RegExp(
		/(Type|Title|Name|Nationality|Sex|DateofBirth|IdentificationNo|PassportNo|CountryCode|DateofIssue|DateofExpiry|Surname|IssuingAuthority|PlaceofBirth)/gi,
	),
	DATA: new RegExp(/[A-Z]+[A-Z0-9]*\b/g),
	MRZ_LINE_1: new RegExp(/[A-Z]{2}[A-Z0<«ec]{20,}</i),
	MRZ_LINE_2: new RegExp(
		/[A-Z0-9<]{9}[0-9][A-Z<]{3}[0-9]{6}[0-9][MF][0-9]{6}[0-9][A-Z0-9<]{14}[0-9][0-9]/,
	),
	ALL_MRZ: new RegExp(/[A-Z][A-Z0-9ecsk<]{20,}/g),
};

export const THAI_DRIVING_LICENSE_REGEX = {
	DIGIT: new RegExp(/[1-8]\d{8,12}/g),
	NUMBER: new RegExp(/(?<!\d)\d{8}(?!\d)/g),
	ENG_WORD: new RegExp(
		/(Kingdom|Of|Thailand|Thai|Car|Driving|License|Number|Private|Motorcycle|ID|No|N[e|a]me|Last[ |]n[e|a]me|Expir[ye]|Issue|D(\w+|)[ ](\w+|)f[ ][B|h](\w+|)[ ](\d{1,2}[ |])([[J|j]an[.| |]|[F|f]eb[.| |]|[M|m]ar[.| |]|[A|a]pr[.| |]|[M|m]ay[.| |]|[J|j]un[.| |]|[J|j]ul[.| |]|[A|a]ug[.| |]|[S|s]ep[.| |]|[O|o]ct[.| |]|[N|n]ov[.| |]|[D|d]ec[.| |]])[ |][1|2]\d{3})/g,
	),
	DATE_FORMAT: new RegExp(
		/(\d{1,2})(Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)(\d{4})/g,
	),
	NAME: new RegExp(/([A-Z]+)/g),
};

/**
 * Github
 * https://gist.github.com/peatiscoding/a16840caed0ba1b29e6b2fe171565ea0#file-assertthaiid-ts
 */

export const assertThaiId = (thaiId: string): boolean => {
	const m = thaiId.match(/(\d{12})(\d)/);
	if (!m) {
		// console.warn('Bad input from user, invalid thaiId=', thaiId);
		return false;
	}

	const digits = m[1].split("");
	const sum = digits.reduce((total: number, digit: string, i: number) => {
		return total + (13 - i) * +digit;
	}, 0);

	const lastDigit = `${(11 - (sum % 11)) % 10}`;
	const inputLastDigit = m[2];
	if (lastDigit !== inputLastDigit) {
		// console.warn('Bad input from user, invalid checksum thaiId=', thaiId);
		return false;
	}
	return true;
};

interface IdValidationResult {
	isValid: boolean;
	reason?: "empty" | "length" | "format" | "checksum" | "invalid-chars";
	details?: {
		expected?: string;
		received?: string;
		position?: number;
	};
}

export function validateIdNumber(idNumber: string): IdValidationResult {
	// Check empty
	if (!idNumber) {
		return {
			isValid: false,
			reason: "empty",
			details: {
				received: idNumber,
			},
		};
	}

	// Check length and format
	if (idNumber.length !== 13) {
		return {
			isValid: false,
			reason: "length",
			details: {
				expected: "13",
				received: idNumber.length.toString(),
			},
		};
	}

	// Check if all characters are digits
	if (!/^\d+$/.test(idNumber)) {
		const invalidChar = idNumber.match(/[^\d]/)?.[0];
		const position = idNumber.indexOf(invalidChar || "");
		return {
			isValid: false,
			reason: "invalid-chars",
			details: {
				received: invalidChar,
				position: position,
			},
		};
	}

	// Validate checksum
	const digits = idNumber.slice(0, 12).split("");
	const sum = digits.reduce((total: number, digit: string, i: number) => {
		return total + (13 - i) * parseInt(digit, 10);
	}, 0);

	const expectedLastDigit = `${(11 - (sum % 11)) % 10}`;
	const actualLastDigit = idNumber[12];

	if (expectedLastDigit !== actualLastDigit) {
		return {
			isValid: false,
			reason: "checksum",
			details: {
				expected: expectedLastDigit,
				received: actualLastDigit,
			},
		};
	}

	// All validations passed
	return {
		isValid: true,
	};
}
