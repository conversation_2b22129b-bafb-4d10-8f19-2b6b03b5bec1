import { CommonModule } from "@angular/common";
import { Component, Inject, OnInit } from "@angular/core";
import { RouterLink } from "@angular/router";
import {
	IonContent,
	IonFab,
	IonFabButton,
	IonHeader,
	IonIcon,
	IonItem,
	IonLabel,
	IonList,
	IonTitle,
	IonToolbar,
} from "@ionic/angular/standalone";
import { addIcons } from "ionicons";
import { add } from "ionicons/icons";
import { Observable } from "rxjs";
import { User } from "../../../core/domain/user.model";
import { GetUserUseCase } from "../../../core/ports/in/user.port";
import { GET_USER_USE_CASE } from "../../../core/ports/in/user.token";

@Component({
	selector: "app-user-list",
	standalone: true,
	imports: [
		CommonModule,
		RouterLink,
		IonList,
		IonItem,
		IonLabel,
		IonContent,
		IonHeader,
		IonToolbar,
		IonTitle,
		IonFab,
		IonFabButton,
		IonIcon,
	],
	template: `
		<ion-header>
			<ion-toolbar>
				<ion-title>Users</ion-title>
			</ion-toolbar>
		</ion-header>

		<ion-content>
			<ion-list>
				<ion-item *ngFor="let user of users$ | async">
					<ion-label>
						<h2>{{ user.name }}</h2>
						<p>{{ user.email }}</p>
					</ion-label>
				</ion-item>
			</ion-list>

			<ion-fab vertical="bottom" horizontal="end" slot="fixed">
				<ion-fab-button routerLink="/users/new">
					<ion-icon name="add"></ion-icon>
				</ion-fab-button>
			</ion-fab>
		</ion-content>
	`,
})
export class UserListComponent implements OnInit {
	users$: Observable<User[]>;

	constructor(
		@Inject(GET_USER_USE_CASE) private getUserUseCase: GetUserUseCase,
	) {
		this.users$ = this.getUserUseCase.getAllUsers();

		// Initialize Ionic icons
		addIcons({ add });
	}

	ngOnInit(): void {
		// Component initialization logic
	}
}
