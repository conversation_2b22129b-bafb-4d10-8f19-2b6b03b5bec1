import { Component, Inject } from "@angular/core";
import { CommonModule } from "@angular/common";
import {
	Form<PERSON>uilder,
	FormGroup,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { Router } from "@angular/router";
import {
	IonButton,
	IonContent,
	IonHeader,
	IonInput,
	IonItem,
	IonLabel,
	IonTitle,
	IonToolbar,
	IonList,
	IonBackButton,
	IonButtons,
} from "@ionic/angular/standalone";
import { CreateUserUseCase } from "../../../core/ports/in/create-user.port";
import { CREATE_USER_USE_CASE } from "../../../core/ports/in/create-user.token";
import { CreateUserCommand } from "../../../core/ports/in/commands/create-user.command";

@Component({
	selector: "app-user-form",
	standalone: true,
	imports: [
		CommonModule,
		ReactiveFormsModule,
		IonButton,
		IonContent,
		IonHeader,
		IonInput,
		IonItem,
		IonLabel,
		IonTitle,
		IonToolbar,
		IonList,
		IonBackButton,
		IonButtons,
	],
	template: `
		<ion-header>
			<ion-toolbar>
				<ion-buttons slot="start">
					<ion-back-button defaultHref="/users"></ion-back-button>
				</ion-buttons>
				<ion-title>Create User</ion-title>
			</ion-toolbar>
		</ion-header>

		<ion-content>
			<form [formGroup]="userForm" (ngSubmit)="onSubmit()">
				<ion-list>
					<ion-item>
						<ion-label position="floating">Name</ion-label>
						<ion-input formControlName="name" type="text"></ion-input>
					</ion-item>
					<div
						*ngIf="
							userForm.get('name')?.invalid && userForm.get('name')?.touched
						"
						class="error-message">
						<div *ngIf="userForm.get('name')?.errors?.['required']">
							Name is required
						</div>
					</div>

					<ion-item>
						<ion-label position="floating">Email</ion-label>
						<ion-input formControlName="email" type="email"></ion-input>
					</ion-item>
					<div
						*ngIf="
							userForm.get('email')?.invalid && userForm.get('email')?.touched
						"
						class="error-message">
						<div *ngIf="userForm.get('email')?.errors?.['required']">
							Email is required
						</div>
						<div *ngIf="userForm.get('email')?.errors?.['email']">
							Please enter a valid email
						</div>
					</div>

					<ion-button
						expand="block"
						type="submit"
						[disabled]="userForm.invalid"
						class="ion-margin">
						Create User
					</ion-button>
				</ion-list>
			</form>
		</ion-content>
	`,
	styles: [
		`
			.error-message {
				color: red;
				padding: 0 16px;
				font-size: 12px;
			}
		`,
	],
})
export class UserFormComponent {
	userForm: FormGroup;

	constructor(
		private formBuilder: FormBuilder,
		private router: Router,
		@Inject(CREATE_USER_USE_CASE) private createUserUseCase: CreateUserUseCase,
	) {
		this.userForm = this.formBuilder.group({
			name: ["", Validators.required],
			email: ["", [Validators.required, Validators.email]],
		});
	}

	onSubmit(): void {
		if (this.userForm.valid) {
			const { name, email } = this.userForm.value;
			const command = new CreateUserCommand(name, email);

			this.createUserUseCase.createUser(command).subscribe({
				next: () => {
					console.log("User created successfully");
					this.router.navigate(["/users"]);
				},
				error: (error) => {
					console.error("Error creating user:", error);
					// Handle error (e.g., show a toast message)
				},
			});
		}
	}
}
