import { CommonModule } from "@angular/common";
import { Component, Input, OnInit, Type } from "@angular/core";
import { WorkflowModalService } from "../../../../core/application/workflow/workflow-modal.service";
import { WorkflowStep } from "../../../../core/domain/workflow/workflow.model";
import { WorkflowStepperComponent } from "../../shared/components/stepper/workflow-stepper.component";

/**
 * Container component for workflow steps
 * Displays a stepper on the left and the current step component on the right
 */
@Component({
	selector: "app-workflow-container",
	standalone: true,
	imports: [CommonModule, WorkflowStepperComponent],
	styleUrls: ["./workflow-container.component.scss"],
	template: `
		<div class="workflow-container">
			<!-- Left sidebar with stepper -->
			<app-workflow-stepper
				*ngIf="!data.isSubflow"
				[currentStepId]="data.stepId"
				class="workflow-stepper"></app-workflow-stepper>

			<!-- Main content area -->
			<div class="workflow-content">
				<ng-container
					*ngComponentOutlet="currentComponent; inputs: componentInputs"
					class="component-container"></ng-container>
			</div>
		</div>
	`,
})
export class WorkflowContainerComponent implements OnInit {
	@Input() currentComponent!: Type<any>;
	@Input() data: {
		stepId: string;
		stepData: WorkflowStep;
		workflowData: Record<string, any>;
		isSubflow: boolean;
		[key: string]: any;
	} = {
		stepId: "",
		stepData: {} as WorkflowStep,
		workflowData: {},
		isSubflow: false,
	};

	@Input() stepperTitle: string = "ขั้นตอนการสมัคร";

	// All steps in the workflow for the stepper
	allSteps: WorkflowStep[] = [];
	// Completed steps for the stepper
	completedSteps: string[] = [];
	// Component inputs for the dynamic component
	componentInputs: Record<string, any> = {};

	constructor(private workflowService: WorkflowModalService) {}

	ngOnInit(): void {
		// Get all steps and completed steps from the workflow service
		this.allSteps = this.workflowService.getAllSteps();
		this.completedSteps = this.workflowService.completedSteps();

		// Prepare inputs for the dynamic component
		this.prepareComponentInputs();
	}

	/**
	 * Prepare inputs for the dynamic component
	 */
	private prepareComponentInputs(): void {
		// Copy all properties from data to componentInputs
		this.componentInputs = { ...this.data };
	}
}
