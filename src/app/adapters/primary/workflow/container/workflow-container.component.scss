:host {
	@apply flex flex-col h-full w-full;
}

.workflow-container {
	@apply flex w-full h-full absolute inset-0 flex-col;

	@media (min-width: 768px) {
		@apply flex-row;
	}

	/* For tablets in portrait mode */
	@media (min-width: 640px) and (max-width: 767px) {
		@apply flex-col;
	}
}

.workflow-stepper {
	@apply h-full overflow-y-auto hidden bg-[#f5f7fa] border-r border-[#e0e0e0] w-full;
	@apply hidden md:block w-[360px] flex-none;
}

.workflow-content {
	@apply flex-1 h-full overflow-hidden overflow-y-auto flex flex-col relative p-0;
}

/* Dynamically loaded components */
::ng-deep .workflow-content>* {
	@apply w-full h-full block;
}

::ng-deep .component-container>* {
	@apply w-full h-full;
}

::ng-deep .component-container>*:first-child {
	@apply flex flex-col flex-1;
}

.component-container {
	@apply block w-full h-full;
}

.step-container {
	@apply flex flex-col h-full w-full;
}