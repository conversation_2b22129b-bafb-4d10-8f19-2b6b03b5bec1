import { Component, computed, inject, Input, signal } from "@angular/core";
import { AuthService } from "src/app/core/application/auth.service";
import { WorkflowModalService } from "../../../core/application/workflow/workflow-modal.service";
import { WorkflowEvent } from "../../../core/domain/workflow/workflow-event.model";
import { WorkflowStep } from "../../../core/domain/workflow/workflow.model";

@Component({
	template: "",
})
export abstract class WorkflowStepBaseComponent {
	public workflowModalService = inject(WorkflowModalService);
	public authService = inject(AuthService);

	// Properties that match the exact structure of data passed from modal
	@Input() stepId!: string;
	@Input() stepData!: WorkflowStep;
	@Input() workflowData: Record<string, any> = {};
	@Input() pageId?: string;

	// State Management
	protected readonly isLoadingState = signal<boolean>(false);
	protected readonly isLoading = computed(() => this.isLoadingState());

	// Internal properties for consistent usage within component
	protected _step!: WorkflowStep;
	protected _isSubflow: boolean = false;

	// Getter for step to use internally
	get step(): WorkflowStep {
		return this._step || this.stepData;
	}

	// Setter for step (for components that might set step directly)
	@Input() set step(value: WorkflowStep) {
		this._step = value;
		// Also update stepData for consistency
		this.stepData = value;
	}

	// Getter and setter for isSubflow to ensure consistency
	get isSubflow(): boolean {
		return this._isSubflow;
	}

	@Input() set isSubflow(value: boolean) {
		this._isSubflow = value;
	}

	public getTxnIdOrError(): string {
		const txnId = this.authService.getTxnId();
		if (!txnId) {
			throw new Error("Transaction ID is missing.");
		}

		return txnId;
	}

	/**
	 * Dispatch a workflow event
	 */
	dispatch(event: WorkflowEvent): void {
		this.workflowModalService.dispatch(event);
	}

	/**
	 * Go to the next step
	 * @param payload Optional data to pass to the next step
	 */
	next(payload?: Record<string, any>): void {
		this.dispatch({
			command: this.isSubflow ? "next_subflow" : "next",
			payload,
			fromStepId: this.stepId,
		});
	}

	/**
	 * Go back to the previous step
	 */
	back(): void {
		this.dispatch({
			command: this.isSubflow ? "back_subflow" : "back",
			fromStepId: this.stepId,
		});
	}

	/**
	 * Close the workflow
	 */
	close(): void {
		this.dispatch({
			command: "close",
			fromStepId: this.stepId,
		});
	}

	/**
	 * Start a subflow
	 * If targetStepId is not provided, it will use the start step of the current step's subflow
	 * @param targetStepId Optional step ID to start the subflow from
	 * @param payload Optional payload to pass to the subflow
	 */
	startSubflow(targetStepId?: string, payload?: Record<string, any>): void {
		// If no targetStepId is provided, check if the current step has a subflow
		// and use its start step ID
		if (!targetStepId && this.hasSubflow() && this.stepData.subflow?.start) {
			targetStepId = this.stepData.subflow.start;
		}

		if (!targetStepId) {
			return;
		}

		this.dispatch({
			command: "start_subflow",
			targetStepId,
			payload,
			fromStepId: this.stepId,
		});
	}

	/**
	 * Close the current subflow
	 * @param payload Optional data to pass back to the parent workflow
	 */
	closeSubflow(payload?: Record<string, any>): void {
		this.dispatch({
			command: "close_subflow",
			fromStepId: this.stepId,
			payload,
		});
	}

	/**
	 * Jump to a specific step
	 * @param targetStepId ID of the step to jump to
	 * @param payload Optional data to pass to the target step
	 */
	jumpTo(targetStepId: string, payload?: Record<string, any>): void {
		this.dispatch({
			command: "jump_to",
			targetStepId,
			payload,
			fromStepId: this.stepId,
		});
	}

	/**
	 * Report an error
	 * @param error Error information to report
	 */
	reportError(error: Record<string, any>): void {
		this.dispatch({
			command: "error",
			payload: error,
			fromStepId: this.stepId,
		});
	}

	/**
	 * Check if the step has a back step
	 */
	hasBackStep(): boolean {
		return !!(this.stepData && this.stepData.back);
	}

	/**
	 * Check if the step has a next step
	 */
	hasNextStep(): boolean {
		return !!(
			this.stepData &&
			this.stepData.next &&
			this.stepData.next.length > 0
		);
	}

	/**
	 * Check if the step has a subflow
	 */
	hasSubflow(): boolean {
		return !!(this.stepData && this.stepData.subflow);
	}

	/**
	 * Run an async operation with loading state indicator
	 * @param operation Function to run
	 * @returns Result from the operation
	 */
	async runWithLoading<T>(operation: () => Promise<T>): Promise<T | undefined> {
		try {
			this.isLoadingState.set(true);
			return await operation();
		} finally {
			this.isLoadingState.set(false);
		}
	}

	/**
	 * Save workflow state to localStorage (for demo purposes)
	 */
	protected saveWorkflowState(): void {
		// Get current state from service
		const workflowId = this.workflowModalService.currentWorkflowId();
		const currentStepId = this.workflowModalService.currentStepId();
		const completedSteps = this.workflowModalService.completedSteps();
		const workflowData = this.workflowModalService.workflowData();
		const activeSubflow = this.workflowModalService.activeSubflow();

		// Create state object
		const state = {
			workflowId,
			currentStepId,
			completedSteps,
			workflowData,
			activeSubflow,
			error: null,
		};

		// Save to localStorage
		localStorage.setItem("loan-workflow-state", JSON.stringify(state));
	}
}
