/**
 * Main Layout
 */
.workflow-start-container {
	@apply flex h-full w-full;
}

/**
 * Navigation
 */
// Side Navigation Container
.side-nav {
	@apply flex h-full flex-row bg-white;
}

// Primary Navigation (Left sidebar)
.primary-nav {
	@apply flex h-full w-[60px] flex-col items-center border-r border-gray-200;

	.logo-container {
		@apply mb-4 flex items-center justify-center pt-4;
	}

	.logo {
		@apply h-10 w-10;
	}

	.menu-icon {
		@apply flex h-6 w-6 items-center justify-center text-lg text-gray-900;

		img {
			@apply h-6 w-6 transition-all duration-200;
		}
	}

	.user-avatar {
		@apply mx-auto mb-4 flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-500;
	}
}

// Primary Menu
.primary-menu {
	@apply flex flex-1 flex-col items-center space-y-4;

	&-item {
		@apply relative z-20 flex h-10 w-10 cursor-pointer items-center justify-center rounded-md transition-colors duration-200 hover:bg-gray-100;

		&.active {
			@apply bg-primary/10 text-primary;
		}
	}
}

// Secondary Navigation (Category sidebar)
.secondary-nav {
	@apply flex h-full w-[300px] flex-col border-r border-gray-200 transition-all duration-300;

	&.collapsed {
		@apply w-0 overflow-hidden;
	}

	&-header {
		@apply flex h-[30px] flex-row items-center gap-2 border-b border-gray-200 px-4;
	}

	&-content {
		@apply flex-1 overflow-y-auto p-4;
	}
}

// App Info
.app-title,
.app-version {
	@apply text-xs text-gray-500;
}

// Section Titles
.section-title {
	@apply mb-4 text-base text-gray-900;
}

// Loan Products List
.loan-products {
	@apply space-y-2;
}

.loan-product-item {
	@apply relative z-10 flex cursor-pointer items-center rounded-lg border border-gray-200 p-3 transition-colors duration-200 hover:border-primary/20 hover:bg-primary/5;

	&.active {
		@apply border-primary bg-primary/10;
	}
}

// Product Details
.product-icon {
	@apply mr-3 flex h-8 w-8 items-center justify-center;
}

.product-details {
	@apply flex-1;
}

.product-title {
	@apply text-sm font-medium text-gray-900;
}

.product-description {
	@apply mt-1 text-xs text-gray-500;
}

/**
 * Content Area
 */
.content-area {
	@apply flex flex-1 flex-col bg-gray-50;
}

// Header
.header {
	@apply flex h-[30px] items-center justify-between border-b border-gray-200 bg-white px-4;
}

.breadcrumb,
.user-info-mini {
	@apply text-xs text-gray-500;
}

// Product Information
.product-info {
	@apply flex flex-col p-6;

	.product-title {
		@apply mb-1 text-sm font-normal text-gray-900;
	}

	.product-subtitle {
		@apply mb-6 text-xs font-normal text-gray-500;
	}
}

// Product Sections
.product-section {
	@apply mb-4;
}

.section-content {
	@apply text-gray-500;
}

// Disclaimer
.disclaimer {
	@apply mt-4 text-xs italic text-gray-500;
}

// Action Button
.action-button {
	@apply mt-6 flex justify-end;
}

/**
 * Empty States
 */
// Main Empty State
.empty-state {
	@apply flex h-full flex-col items-center justify-center p-6;

	.empty-icon {
		@apply mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100 text-4xl text-gray-500;
	}

	.empty-message {
		@apply text-center text-gray-500;
	}
}

// Menu Empty State
.empty-menu {
	@apply flex h-full flex-col items-center justify-center text-center;
	transition: opacity 0.3s ease;

	.empty-icon {
		@apply mb-5 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-primary;
		box-shadow: 0 2px 10px rgba(59, 130, 246, 0.15);
		transition: all 0.3s ease;
	}

	.empty-title {
		@apply mb-3 text-base font-semibold text-gray-900;
	}

	.empty-description {
		@apply max-w-[220px] text-sm leading-relaxed text-gray-500;
	}
}

/**
 * User Profile
 */
.nav-footer-sticky {
	@apply flex w-full flex-col items-center justify-start bg-white;
}

.user-profile-container {
	@apply flex w-full flex-col overflow-hidden p-4;
}

.user-profile {
	@apply flex w-full items-center justify-between;
}

.avatar {
	@apply flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-primary transition-all duration-300;

	&-text {
		@apply text-sm font-medium text-white;
	}
}

.user-info {
	@apply ml-3 min-w-0 flex-1 overflow-hidden;
	white-space: nowrap;
}

.user-name {
	@apply max-w-full truncate text-sm font-light text-gray-900;
}

.user-id {
	@apply max-w-full truncate text-xs text-gray-500;
}

/**
 * Footer and Toggle
 */
.nav-footer-toggle-container {
	@apply flex w-full items-center justify-start border-t border-gray-100 p-2;

	span {
		@apply ml-2 text-base text-gray-900;
	}
}

/**
 * Drawer Components
 */
// Content Container
.primary-nav-content {
	@apply flex flex-1 flex-col overflow-auto;
}

// Expanded Menu Container
.menu-item-expanded-container {
	@apply mb-4 pb-2;
}

.menu-item-expanded-label {
	@apply mb-3 flex items-center px-4 text-base font-medium text-gray-900;

	span {
		@apply ml-2 rounded-full bg-primary/10 px-2 py-0.5 text-xs font-normal text-primary;
	}
}

// Menu Content Expanded
.menu-item-expanded {
	@apply grid grid-cols-3 gap-3 px-4;
	z-index: 1;
}

.menu-item-expanded-box {
	@apply flex min-h-[70px] w-[80px] cursor-pointer flex-col items-center space-y-1 rounded-lg p-2 transition-all duration-200;
	@apply border border-gray-300;

	&:hover {
		@apply border-primary/30 bg-primary/5;

		span {
			@apply text-primary;
		}
	}

	img {
		@apply h-6 w-6 object-contain;
	}

	&.active {
		@apply border-primary bg-primary/10;

		span {
			@apply text-primary;
		}
	}

	&.disabled {
		@apply cursor-not-allowed opacity-50 hover:border-gray-200 hover:bg-transparent;
	}

	span {
		@apply text-xs text-gray-900;
	}
}

/**
 * Logo Section
 */
.logo-container {
	@apply flex w-full items-center p-4 transition-all duration-300;

	&-img {
		@apply flex h-12 w-12 flex-shrink-0 items-center justify-center;

		img {
			@apply h-12 w-12 object-contain;
		}
	}

	&-text {
		@apply ml-3 flex flex-col items-start justify-between gap-1;

		&-title {
			@apply text-base text-gray-900;
		}

		&-version {
			@apply text-sm text-gray-500;
		}
	}
}