import { CommonModule } from "@angular/common";
import { Component, OnInit, inject, signal } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { IonicModule } from "@ionic/angular";
import { addIcons } from "ionicons";
import {
	arrowForwardOutline,
	businessOutline,
	carSportOutline,
	documentTextOutline,
	homeOutline,
	refreshOutline,
	walletOutline,
} from "ionicons/icons";
import { ButtonModule } from "primeng/button";
import { DrawerModule } from "primeng/drawer";

import {
	DashboardService,
	MenuCategory,
	PrimaryMenuItem,
} from "src/app/core/application/dashboard.service";
import { StarmoneyService } from "../../../core/application/starmoney.service";
import { WorkflowModalService } from "../../../core/application/workflow/workflow-modal.service";

import { LoanTypeComponent } from "../loan/loan-type/loan-type.component";
import { FooterComponent } from "../shared/components/footer/footer.component";
import { StepCounterService } from "../shared/services/step-counter.service";
import { WorkflowStepBaseComponent } from "../workflow/workflow-step-base.component";

@Component({
	selector: "app-dashboard",
	standalone: true,
	imports: [
		CommonModule,
		IonicModule,
		ButtonModule,
		DrawerModule,
		FooterComponent,
		LoanTypeComponent,
	],
	styleUrl: "./dashboard.component.scss",
	template: `
		<ion-content>
			<div class="workflow-start-container">
				<!-- Side Navigation -->
				<div class="side-nav">
					<!-- Primary Nav -->
					<div class="primary-nav">
						<div class="logo-container !p-0 !py-4">
							<img
								src="assets/svgs/normal-starmoney-logo.svg"
								alt="SMART SALE v1.0.0"
								class="logo" />
						</div>

						<!-- Primary Menu Items -->
						<div class="primary-menu">
							@for (menu of menuCategories(); track menu.categoryId) {
								@for (child of menu.children; track child.id) {
									<div
										class="primary-menu-item"
										[class.active]="isActivePrimaryMenu(child)"
										[class.disabled]="child.disabled"
										(click)="selectPrimaryMenu(menu, child)">
										<div class="menu-icon">
											<img [src]="getIconPath(child)" alt="{{ child.label }}" class="menu-icon" />
										</div>
									</div>
								}
							}
						</div>

						<!-- Footer with Toggle Button -->
						<div class="nav-footer-sticky">
							<div class="user-profile-container !p-0 !py-4">
								<div class="user-profile !justify-center">
									<div class="avatar">
										<div class="avatar-text">สท</div>
									</div>
								</div>
							</div>

							<div class="nav-footer-toggle-container !justify-center !p-0 !py-2">
								<p-button
									[rounded]="true"
									[text]="true"
									aria-label="toggle drawer"
									(click)="visible.set(true)">
									<img src="assets/svgs/menu-show.svg" alt="Menu Show" />
								</p-button>
							</div>
						</div>
					</div>

					<!-- Secondary Nav -->
					<div class="secondary-nav">
						<div class="secondary-nav-header">
							<img src="assets/svgs/mini-starmoney-logo.svg" alt="SMART SALE v1.0.0" class="logo" />
							<div class="app-title">SMART SALE</div>
							<div class="app-version">v1.0.0</div>
						</div>

						<div class="secondary-nav-content">
							<div class="section-title">
								{{ activeSecondaryMenu()?.label || "ไม่พบเมนูย่อย" }}
							</div>
							@if (secondaryMenus().length > 0) {
								<div class="loan-products">
									@for (menu of secondaryMenus(); track menu.mappedId) {
										<div
											class="loan-product-item"
											[class.active]="isActiveSecondaryMenu(menu.mappedId)"
											(click)="selectSecondaryMenu(menu.mappedId)">
											<div class="product-icon">
												<img [src]="menu.icon" />
											</div>
											<div class="product-details">
												<div class="product-title">{{ menu.label }}</div>
												<div class="product-description" *ngIf="menu.description">
													{{ menu.description }}
												</div>
											</div>
										</div>
									}
								</div>
							} @else {
								<!-- Empty State -->
								<div class="empty-menu">
									<div class="empty-icon">
										<ion-icon name="document-text-outline"></ion-icon>
									</div>
									<h3 class="empty-title">ไม่พบเมนูย่อย</h3>
									<p class="empty-description">
										เมนูนี้สามารถเข้าถึงได้โดยตรง ไม่มีตัวเลือกย่อยให้เลือก
									</p>
								</div>
							}
						</div>
					</div>
				</div>

				<!-- Content Area -->
				<div class="content-area">
					<!-- Product Information (only visible when secondary menu is selected) -->
					<ng-container *ngIf="activeSecondaryMenu()">
						@if (
							activeSecondaryMenu()?.mappedId &&
							["personal-loan", "hire-purchase"].includes(activeSecondaryMenu()!.mappedId)
						) {
							<app-loan-type class="h-full w-full"></app-loan-type>
						} @else {
							<div class="product-info">
								<h1 class="product-title">ข้อมูลสำคัญของผลิตภัณฑ์</h1>
								<h2 class="product-subtitle">สินเชื่อเช่าซื้อสินค้า</h2>
								<div class="product-details">
									<div class="product-section">
										<h3 class="section-title">1. ผลิตภัณฑ์นี้คืออะไร?</h3>
										<div class="section-content">
											<p>{{ activeSecondaryMenu()?.label }}</p>
										</div>
									</div>

									<div class="disclaimer">
										* กรุณาอ่านรายละเอียดเงื่อนไขก่อนตัดสินใจสมัครสินเชื่อ
									</div>
								</div>

								<div class="action-button">
									<ion-button (click)="startWorkflow()">สมัครสินเชื่อเลยตอนนี้</ion-button>
								</div>
								<!-- Footer -->
								<app-footer>
									<div class="footer-wrapper">
										<p-button
											label="สมัครสินเชื่อเลยตอนนี้"
											[fluid]="true"
											[loading]="isLoading()"
											(onClick)="startWorkflow()" />
									</div>
								</app-footer>
							</div>
						}
					</ng-container>

					<!-- Empty state when no product is selected -->
					<div class="empty-state" *ngIf="!activeSecondaryMenu()">
						<div class="empty-icon">
							<ion-icon name="document-text-outline"></ion-icon>
						</div>
						<div class="empty-message">กรุณาเลือกประเภทสินเชื่อจากเมนูด้านซ้าย</div>
					</div>
				</div>
			</div>
		</ion-content>

		<p-drawer [(visible)]="visible" [closable]="false">
			<ng-template #header>
				<!-- Logo -->
				<div class="logo-container">
					<div class="logo-container-img">
						<img src="assets/svgs/smart-sale-logo.svg" alt="Smart Sale Logo" />
					</div>
					<div class="logo-container-text">
						<p class="logo-container-text-title">STAR MONEY: SMART SALE</p>
						<p class="logo-container-text-version">v1.0.0</p>
					</div>
				</div>
			</ng-template>

			<!-- Primary Nav Content -->
			<div class="primary-nav-content">
				<!-- Menu Categories -->
				@for (menu of menuCategories(); track menu.categoryId) {
					<div class="menu-item-expanded-container">
						<p class="menu-item-expanded-label">
							{{ menu.label }}
						</p>
						<div class="menu-item-expanded">
							@for (child of menu.children; track child.id) {
								<button
									class="menu-item-expanded-box"
									[title]="child.label"
									[class.active]="isActivePrimaryMenu(child)"
									[class.disabled]="child.disabled"
									[disabled]="child.disabled"
									(click)="selectPrimaryMenu(menu, child)">
									<img [src]="getIconPath(child)" [alt]="child.label" />
									<span>{{ child.label }}</span>
								</button>
							}
						</div>
					</div>
				}
			</div>

			<ng-template #footer>
				<!-- Footer with Toggle Button -->
				<div class="nav-footer-sticky">
					<div class="user-profile-container">
						<div class="user-profile">
							<div class="avatar">
								<div class="avatar-text">สท</div>
							</div>
							<div class="user-info">
								<p class="user-name">คุณสตาร์ ทดสอบระบบ</p>
								<p class="user-id">ID: 0000001</p>
							</div>
							<p-button [rounded]="true" [text]="true" styleClass="user-option-button">
								<img src="assets/svgs/option.svg" alt="Option" />
							</p-button>
						</div>
					</div>

					<div class="nav-footer-toggle-container">
						<p-button [rounded]="true" [text]="true" (click)="visible.set(false)">
							<img src="assets/svgs/menu-hide.svg" alt="Menu Hide" />
						</p-button>
						<span>ย่อขนาดเมนู</span>
					</div>
				</div>
			</ng-template>
		</p-drawer>
	`,
})
export class DashboardComponent extends WorkflowStepBaseComponent implements OnInit {
	private workflowService = inject(WorkflowModalService);
	private dashboardService = inject(DashboardService);
	private starmoneyService = inject(StarmoneyService);
	private stepCounterService = inject(StepCounterService);
	private activatedRoute = inject(ActivatedRoute);
	private route = inject(ActivatedRoute);

	protected readonly menuCategories = this.dashboardService.menuCategories;
	protected readonly activeMenuCategory = this.dashboardService.activeMenuCategory;
	protected readonly activePrimaryMenu = this.dashboardService.activePrimaryMenu;
	protected readonly activeSecondaryMenu = this.dashboardService.activeSecondaryMenu;
	protected readonly secondaryMenus = this.dashboardService.secondaryMenus;

	protected visible = signal(false);
	protected override isLoading = signal(false);

	constructor() {
		super();
		const txnId = this.activatedRoute.snapshot.queryParams["txnId"] || null;
		if (txnId) this.authService.setTxnId(String(txnId));

		addIcons({
			documentTextOutline,
			homeOutline,
			carSportOutline,
			businessOutline,
			walletOutline,
			arrowForwardOutline,
			refreshOutline,
		});
	}

	async ngOnInit(): Promise<void> {
		try {
			if (!localStorage.getItem("auth_session")) {
				const txnId = this.authService.getTxnId();
				if (txnId !== null) {
					await this.authService.signInAnonymous(txnId);
					console.log("Signed in anonymously");
				} else {
					throw new Error("Transaction ID is undefined");
				}
			}
		} catch (error) {
			console.error("Error in ngOnInit:", error);
		}

		this.handleUrlParameters();
	}

	private handleUrlParameters(): void {
		this.route.queryParams.subscribe((params) => {
			//Todo: clear completed steps from localStorage
			// clear completed steps from localStorage
			this.stepCounterService.clearCompletedSteps();

			if (params["stepId"]) {
				this.resumeToStep(params["stepId"]);
			} else if (params["pageId"]) {
				this.resumeToPage(params["pageId"]);
			}
		});
	}

	isActiveMenuCategory(menuCategory: MenuCategory): boolean {
		const active = this.activeMenuCategory();
		return active?.categoryId === menuCategory.categoryId;
	}

	protected selectPrimaryMenu(menu: MenuCategory, primaryMenu: PrimaryMenuItem): void {
		this.dashboardService.selectMenuCategory(menu);
		this.dashboardService.selectPrimaryMenu(primaryMenu);
		this.visible.set(false);
	}

	protected isActivePrimaryMenu(menu: PrimaryMenuItem): boolean {
		const active = this.activePrimaryMenu();
		return active?.id === menu.id;
	}

	protected selectSecondaryMenu(menuId: string): void {
		this.dashboardService.selectSecondaryMenu(menuId);
	}

	protected isActiveSecondaryMenu(menuId: string): boolean {
		const active = this.activeSecondaryMenu();
		return active?.mappedId === menuId;
	}

	protected getIconPath(menu: PrimaryMenuItem): string {
		return this.isActivePrimaryMenu(menu) ? menu.icon?.active || "" : menu.icon?.inactive || "";
	}

	protected startWorkflow(): void {
		const activeMenu = this.activeSecondaryMenu();
		if (activeMenu) {
			this.isLoading.set(true);
			this.workflowService.dispatch({
				command: "start",
				targetStepId: "loan-application-workflow",
				payload: {
					productId: activeMenu.mappedId,
					productTitle: activeMenu.label,
				},
			});
		}
	}

	private resumeToStep(stepId: string): void {
		this.workflowService.resumeToStep(stepId);
	}

	private resumeToPage(pageId: string): void {
		this.workflowService.resumeToPage(pageId);
	}
}
