<ion-content class="ion-padding">
	<div class="login-container">
		<div class="login-card">
			<div class="logo-container">
				<img
					src="assets/svgs/smart-sale-logo.svg"
					alt="Star Money Logo"
					class="logo" />
			</div>

			<h1 class="title">STAR MONEY: SMART SALE</h1>
			<p class="version">Version: {{ appVersion }}</p>

			<div class="login-form">
				<h2 class="form-title">ลงชื่อเข้าสู่ระบบ</h2>

				<div class="form-group">
					<span class="p-input-icon-left w-full">
						<i class="pi pi-user"></i>
						<input
							type="text"
							pInputText
							[(ngModel)]="username"
							placeholder="Username"
							class="w-full"
							[disabled]="isLoading" />
					</span>
				</div>

				<div class="form-group">
					<span class="p-input-icon-left w-full">
						<i class="pi pi-lock"></i>
						<p-password
							[(ngModel)]="password"
							[feedback]="false"
							[toggleMask]="true"
							placeholder="Password"
							styleClass="w-full"
							inputStyleClass="w-full"
							[disabled]="isLoading"></p-password>
					</span>
				</div>

				<div class="action-buttons">
					<a (click)="forgotPassword()" class="forgot-password">
						ลืมรหัสผ่าน ?
					</a>

					<p-button
						label="ตรวจสอบสถานะ"
						styleClass="p-button-primary check-status-btn"
						[loading]="isLoading"
						(onClick)="checkStatus()"></p-button>
				</div>
			</div>
		</div>
	</div>
</ion-content>
