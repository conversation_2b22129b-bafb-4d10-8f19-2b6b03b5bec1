import { CommonModule } from "@angular/common";
import { Component, OnInit, inject } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { Router } from "@angular/router";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { InputTextModule } from "primeng/inputtext";
import { PasswordModule } from "primeng/password";
import { AuthService } from "src/app/core/application/auth.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";

@Component({
	selector: "app-login",
	templateUrl: "./login.component.html",
	styleUrls: ["./login.component.scss"],
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		ButtonModule,
		InputTextModule,
		PasswordModule,
	],
})
export class LoginComponent implements OnInit {
	private readonly authService = inject(AuthService);
	private readonly appUtilsService = inject(AppUtilsService);
	private readonly router = inject(Router);

	username: string = "";
	password: string = "";
	isLoading: boolean = false;
	appVersion: string = "20401-P100";

	constructor() {}

	ngOnInit(): void {}

	async login(): Promise<void> {
		if (!this.username || !this.password) {
			await this.appUtilsService.showError(
				"ข้อผิดพลาด",
				"กรุณากรอกชื่อผู้ใช้และรหัสผ่าน",
			);
			return;
		}

		try {
			this.isLoading = true;
			this.router.navigate(["/dashboard"]);
		} catch (error) {
			console.error("Login error:", error);
			await this.appUtilsService.showError(
				"เข้าสู่ระบบไม่สำเร็จ",
				"ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง",
			);
		} finally {
			this.isLoading = false;
		}
	}

	async forgotPassword(): Promise<void> {
		await this.appUtilsService.showInfo(
			"ลืมรหัสผ่าน กรุณาติดต่อผู้ดูแลระบบเพื่อรีเซ็ตรหัสผ่าน",
		);
	}

	checkStatus(): void {
		this.router.navigate(["/dashboard"]);
	}
}
