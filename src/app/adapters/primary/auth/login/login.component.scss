:host {
	display: block;
	height: 100%;
}

ion-content {
	--background: #e6f2fa;
	height: 100%;
}

.login-container {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100%;
	padding: 20px;
}

.login-card {
	background-color: white;
	border-radius: 12px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	width: 100%;
	max-width: 400px;
	padding: 30px;
	text-align: center;
}

.logo-container {
	margin-bottom: 20px;
	display: flex;
	justify-content: center;
}

.logo {
	width: 80px;
	height: 80px;
}

.title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
	margin-bottom: 5px;
}

.version {
	font-size: 12px;
	color: #666;
	margin-bottom: 30px;
}

.form-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin-bottom: 20px;
	text-align: center;
}

.login-form {
	width: 100%;
}

.form-group {
	margin-bottom: 20px;
	width: 100%;
}

.action-buttons {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 30px;
}

.forgot-password {
	color: #0192fb;
	font-size: 14px;
	cursor: pointer;
	text-decoration: none;

	&:hover {
		text-decoration: underline;
	}
}

.check-status-btn {
	background-color: #0192fb;
	border-color: #0192fb;
}

// Make inputs full width
:host ::ng-deep {
	.p-inputtext {
		width: 100%;
	}

	.p-password {
		width: 100%;
	}

	.p-button {
		font-weight: 500;
	}
}
