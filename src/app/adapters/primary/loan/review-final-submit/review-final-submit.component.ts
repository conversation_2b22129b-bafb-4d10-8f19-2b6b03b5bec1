import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject } from "@angular/core";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { StepCounterService } from "../../shared/services/step-counter.service";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-review-final-submit",
	standalone: true,
	imports: [
		CommonModule,
		IonicModule,
		HeaderMiniComponent,
		FooterComponent,
		ButtonModule,
	],
	styleUrl: "./review-final-submit.component.scss",
	template: `
		<ion-content>
			<div class="app-layout">
				<app-header-mini mode="workflow" />

				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper !max-w-full">
						<!-- Content Spliter -->
						<div class="flex gap-6">
							<div class="panel-container">
								<!-- Consent Title -->
								<div class="section-header">
									<h2 class="section-title-text">
										{{ personalInformation.label }}
									</h2>
								</div>

								@for (field of personalInformation.fields; track field.label) {
									<div class="field-container">
										<span class="field-label">{{ field.label }}</span>
										<span class="field-value">{{ field.value }}</span>
									</div>
								}

								<!-- Address Information Title -->
								<div class="section-header mt-4">
									<h2 class="section-title-text">
										{{ addressInformation.label }}
									</h2>
								</div>

								@for (field of addressInformation.fields; track field.label) {
									<div class="field-container">
										<span class="field-label">{{ field.label }}</span>
										<span class="field-value">{{ field.value }}</span>
									</div>
								}

								<!-- Working Information Title -->
								<div class="section-header mt-4">
									<h2 class="section-title-text">
										{{ workingInformation.label }}
									</h2>
								</div>

								@for (field of workingInformation.fields; track field.label) {
									<div class="field-container">
										<span class="field-label">{{ field.label }}</span>
										<span class="field-value">{{ field.value }}</span>
									</div>
								}
							</div>

							<div class="panel-container">
								<!-- Consent Title -->
								<div class="section-header">
									<h2 class="section-title-text">
										{{ loanApplication.label }}
									</h2>
								</div>

								@for (field of loanApplication.fields; track field.label) {
									<div class="field-container">
										<span class="field-label">{{ field.label }}</span>
										<span class="field-value">{{ field.value }}</span>
									</div>
								}

								<p class="disclaimer-text">
									<span>
										* กรุณาแจ้งให้ลูกค้าทราบ
										ข้อมูลดังกล่าวเป็นการประเมินเบื้องต้นจากระบบ
									</span>
									<span>อาจมีการเปลี่ยนแปลงตามผลการพิจารณาสินเชื่อ</span>
								</p>

								<!-- Consent Title -->
								<div class="section-header mt-4">
									<h2 class="section-title-text">
										{{ documentsInformation.label }}
									</h2>
								</div>

								@for (field of documentsInformation.fields; track field.label) {
									<div class="flex flex-col gap-1">
										<span class="mt-2 text-sm font-light text-black">
											{{ field.label }}
										</span>
										<span class="text-sm font-light text-gray-500">
											{{ field.value }}
										</span>
										<hr />
									</div>
								}
								<p class="mt-2 text-left text-xs text-gray-500">
									หากตรวจสอบแล้วพบว่ามีข้อมูลส่วนใดผิดพลาด
									ท่านสามารถแจ้งเจ้าหน้าที่ของเราทราบ
									เพื่อดำเนินการแก้ไขข้อมูลให้ถูกต้องได้
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Footer -->
			<app-footer>
				<div class="footer-wrapper !justify-between">
					<p-button
						label="ย้อนกลับ"
						[outlined]="true"
						[fluid]="true"
						(onClick)="back()" />
					<p-button
						label="ยืนยันการสมัคร"
						[fluid]="true"
						[loading]="isLoading()"
						(onClick)="submit()" />
				</div>
			</app-footer>
		</ion-content>
	`,
})
export class ReviewFinalSubmitComponent extends WorkflowStepBaseComponent {
	public stepCounterService = inject(StepCounterService);
	private appUtilsService = inject(AppUtilsService);

	activeStepId: string = "2.5";
	personalInformation = {
		label: "ข้อมูลส่วนตัวของผู้สมัครสินเชื่อ",
		fields: [
			{
				label: "ชื่อ-นามสกุล",
				value: "นายสมชาย สายเสมอ",
			},
			{
				label: "วันเกิด",
				value: "31/12/2537",
			},
			{
				label: "เลขบัตรประชาชน",
				value: "* **** ***** 12 3",
			},
			{
				label: "เบอร์โทรศัพท์มือถือ",
				value: "0123456789",
			},
			{
				label: "อีเมลแอดเดรส",
				value: "<EMAIL>",
			},
		],
	};

	addressInformation = {
		label: "ข้อมูลที่อยู่",
		fields: [
			{
				label: "ที่อยู่ตามหน้าบัตรประชาชน",
				value: "1 บางนา บางนา กรุงเทพมหานคร 10210",
			},
			{
				label: "ที่อยู่ปัจจุบัน",
				value: "1 บางนา บางนา กรุงเทพมหานคร 10210",
			},
			{
				label: "ลักษณะที่อยู่อาศัย",
				value: "บ้านพักอยู่",
			},
		],
	};

	workingInformation = {
		label: "ข้อมูลการทำงาน",
		fields: [
			{
				label: "อาชีพ",
				value: "บริษัท",
			},
			{
				label: "กลุ่มอาชีพ",
				value: "พนักงาน",
			},
			{
				label: "ตำแหน่งงาน",
				value: "หัวหน้าคลังสินค้า",
			},
			{
				label: "วันที่เริ่มงาน",
				value: "15 สิงหาคม 2560",
			},
			{
				label: "อายุงาน",
				value: "7 ปี 2 เดือน",
			},
			{
				label: "รายได้ประจำต่อเดือน:",
				value: "15,000 บาท",
			},
			{
				label: "รายได้อื่นๆ ต่อเดือน",
				value: "0 บาท",
			},
			{
				label: "รายจ่ายต่อเดือน",
				value: "12,000 บาท",
			},
		],
	};

	loanApplication = {
		label: "ข้อมูลการสมัครสินเชื่อ",
		fields: [
			{
				label: "ชื่อผลิตภัณฑ์",
				value: "สินเชื่อเช่าซื้อสินค้า",
			},
			{
				label: "ยี่ห้อ / รุ่น",
				value: "Oppo CPH2579 (A38 4+128G)",
			},
			{
				label: "ราคาสินค้า",
				value: "5,199.00 บาท",
			},
			{
				label: "เงินดาวน์*",
				value: "1,000.00 บาท",
			},
			{
				label: "ยอดจัดสินเชื่อ*",
				value: "4,199.00 บาท",
			},
			{
				label: "อัตราดอกเบี้ยคงที่  (Flat Rate) *",
				value: "4.00% ต่อเดือน",
			},
			{
				label: "จำนวนงวดที่ผ่อนชำระ*",
				value: "12 งวด",
			},
			{
				label: "จำนวนเงินที่ต้องผ่อนชำระ*",
				value: "518.00 บาท",
			},
		],
	};

	documentsInformation = {
		label: "เอกสารหลักฐานที่แนบมา",
		fields: [
			{
				label: "หนังสือรับรองเงินเดือน.pdf",
				value: "หนังสือรับรองเงินเดือน",
			},
			{
				label: "IMG_2024_12_25.png",
				value: "สลิปเงินเดือน",
			},
			{
				label: "IMG_2024_12_25.png",
				value: "???",
			},
		],
	};

	async submit(): Promise<void> {
		try {
			this.appUtilsService.showAlertDialog({
				header: "ยืนยันการสมัคร",
				message:
					"หลังจากยืนยันการสมัคร ท่านจะไม่สามารถย้อนกลับมาแก้ไขข้อมูลส่วนนี้ได้อีก",
				subMessage: `เลือก "<span class="font-bold">ยืนยัน</span>" เพื่อดำเนินการต่อไป <br />
        เลือก "<span class="font-bold">ย้อนกลับ</span>" หากท่านต้องการแก้ไขข้อมูล`,
				acceptLabel: "ตกลง",
				acceptCallback: () => this.next(),
				rejectLabel: "ย้อนกลับ",
				rejectCallback: () => {},
			});
		} catch (error) {
			await this.handleError(error);
		}
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
