import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	computed,
	ElementRef,
	inject,
	OnD<PERSON>roy,
	Renderer2,
	signal,
	ViewChild,
} from "@angular/core";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { Subject } from "rxjs";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { WebcamError } from "ts-webcam";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { CameraHandlerOptions, WebcamService } from "../../shared/services/webcam.service";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";
import {
	DocumentScanResult,
	DocumentScanStatus,
	IdCardDetectionService,
	ProcessedOCRResult,
} from "./id-card-detection.service";

// Types and Interfaces

export interface OverlayDimensions {
	width: number;
	height: number;
	scale: number;
	offsetX: number;
	offsetY: number;
}

export interface IdCardOverlayConfig {
	aspectRatio: number;
	baseWidth: number;
	baseHeight: number;
	overlayWidthPercentage: number;
}

// Constants
export const ID_CARD_CONFIG: IdCardOverlayConfig = {
	aspectRatio: 1.586,
	baseWidth: 338,
	baseHeight: 212,
	overlayWidthPercentage: 1,
};

export const STATUS_COLORS = {
	initializing: "#9CA3AF", // gray-400
	ready: "#10B981", // emerald-500
	scanning: "#F59E0B", // amber-500
	success: "#059669", // emerald-600
	error: "#DC2626", // red-600
	white: "#FFFFFF", // white for insufficient text
} as const;

const SCAN_STATUS_PERCENTAGES: Partial<Record<DocumentScanStatus, number>> = {
	[DocumentScanStatus.TextNotEnough]: 30,
	[DocumentScanStatus.InvalidSize]: 50,
	[DocumentScanStatus.InvalidFormat]: 75,
	[DocumentScanStatus.WordNotEnough]: 85,
	[DocumentScanStatus.Success]: 95, // Changed from 100 to 95 to avoid confusion
	[DocumentScanStatus.TextNotFound]: 0,
} as const;

export const ANIMATION_DURATIONS = {
	success: 1200,
} as const;

export const OCR_THRESHOLDS = {
	minTextLength: 20,
	progressUpdateDelay: 100,
} as const;

export const PERFORMANCE_CONFIG = {
	debounceTime: 16, // ~60fps for smooth animations
	resizeDebounceTime: 100, // Debounce resize events
	ocrUpdateThrottle: 50, // Throttle OCR updates
	maxChangeDetectionCycles: 3, // Limit change detection cycles
} as const;

const UI_MESSAGES = {
	initialGuideline: "กรุณาวางบัตรประชาชนให้อยู่ในกรอบ",
	scanningGuideline: "อยู่นิ่งๆสักครู่",
	successGuideline: "ถ่ายรูปบัตรประชาชนสำเร็จ",
	loadingCamera: "กำลังเปิดกล้อง...",
	scanningInProgress: "กำลังสแกน...",
	captureSuccess: "ถ่ายรูปสำเร็จ!",
	errorOccurred: "เกิดข้อผิดพลาด",
} as const;

export enum CaptureMode {
	AUTO = "auto",
	MANUAL = "manual",
}

export enum ScanningState {
	IDLE = "idle",
	SCANNING = "scanning",
	COMPLETED = "completed",
	ERROR = "error",
}

export const ID_PHOTO_RESOLUTIONS: {
	name: string;
	width: number;
	height: number;
}[] = [
	{ name: "SQUARE-1920", width: 1920, height: 1920 },
	{ name: "SQUARE-1080", width: 1080, height: 1080 },
	{ name: "SQUARE-720", width: 720, height: 720 },
];

@Component({
	standalone: true,
	imports: [CommonModule, IonicModule, FooterComponent, ButtonModule],
	selector: "app-take-id-photo",
	styleUrl: "./take-id-photo.component.scss",
	templateUrl: "./take-id-photo.component.html",
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TakeIdPhotoComponent
	extends WorkflowStepBaseComponent
	implements AfterViewInit, OnDestroy
{
	// ViewChild references
	@ViewChild("videoElement", { static: false })
	private readonly videoElement!: ElementRef<HTMLVideoElement>;
	@ViewChild("svgOverlay", { static: false })
	private readonly svgOverlay!: ElementRef<SVGElement>;

	// Services
	private readonly idCardDetectionService = inject(IdCardDetectionService);
	private readonly webcamService = inject(WebcamService);
	private readonly appUtilsService = inject(AppUtilsService);
	private readonly renderer = inject(Renderer2);
	private readonly cdr = inject(ChangeDetectorRef);

	// Reactive state with signals
	private readonly _scanningState = signal<ScanningState>(ScanningState.IDLE);
	private readonly _progress = signal<number>(0);
	private readonly _overlayDimensions = signal<OverlayDimensions | null>(null);
	private readonly _processingMode = signal<CaptureMode>(CaptureMode.AUTO);
	private readonly _ocrInfo = signal<ProcessedOCRResult | null>(null);
	private readonly _guidelineText = signal<string>(UI_MESSAGES.initialGuideline);
	private readonly _webcamStatus = signal<string>("initializing");

	// Computed properties with performance optimization
	public readonly currentWebcamStatus = computed(() => this._webcamStatus());
	public readonly currentScanningState = computed(() => this._scanningState());
	public readonly captureProgress = computed(() => this._progress());
	public readonly overlayDimensions = computed(() => this._overlayDimensions());
	public readonly captureMode = computed(() => this._processingMode());
	public readonly buttonLabel = computed(() => this._getButtonLabel());
	public readonly guidelineText = computed(() => this._guidelineText());
	public readonly overlayColor = computed(() => this._getOverlayColor());
	public readonly ocrInfo = computed(() => this._ocrInfo());

	// Performance-optimized computed properties
	public readonly isProcessingActive = computed(
		() => this.currentScanningState() === ScanningState.SCANNING,
	);
	public readonly isReadyForCapture = computed(
		() =>
			this.currentWebcamStatus() === "ready" && this.currentScanningState() !== ScanningState.ERROR,
	);
	public readonly shouldShowOverlay = computed(() => this.overlayDimensions() !== null);

	// Observables and cleanup
	private readonly destroy$ = new Subject<void>();
	private resizeObserver?: ResizeObserver;
	private animationFrameId?: number;

	// Performance optimization
	private changeDetectionCycles = 0;
	private lastOcrUpdateTime = 0;
	private resizeTimeoutId?: ReturnType<typeof setTimeout>;
	private isDetached = false;
	private webcamStatusSyncInterval?: ReturnType<typeof setInterval>;

	constructor() {
		super();
		this.setupPerformanceOptimizations();
	}

	/**
	 * Setup performance optimizations for OnPush change detection
	 */
	private setupPerformanceOptimizations(): void {
		// Detach change detector initially to prevent unnecessary cycles
		this.detachChangeDetector();
	}

	/**
	 * Detach change detector for manual control
	 */
	private detachChangeDetector(): void {
		if (!this.isDetached) {
			this.cdr.detach();
			this.isDetached = true;
		}
	}

	/**
	 * Reattach change detector when needed
	 */
	private reattachChangeDetector(): void {
		if (this.isDetached) {
			this.cdr.reattach();
			this.isDetached = false;
		}
	}

	/**
	 * Manually trigger change detection with cycle limit
	 */
	private triggerChangeDetection(): void {
		if (this.changeDetectionCycles < PERFORMANCE_CONFIG.maxChangeDetectionCycles) {
			this.changeDetectionCycles++;
			this.cdr.markForCheck();

			// Reset cycle counter after a delay
			setTimeout(() => {
				this.changeDetectionCycles = 0;
			}, PERFORMANCE_CONFIG.debounceTime);
		}
	}

	/**
	 * Optimized method to update UI state with throttling
	 */
	private updateUIState(): void {
		const now = performance.now();
		if (now - this.lastOcrUpdateTime > PERFORMANCE_CONFIG.ocrUpdateThrottle) {
			this.lastOcrUpdateTime = now;
			this.triggerChangeDetection();
		}
	}

	/**
	 * Sync webcam status from service to signal
	 */
	private syncWebcamStatus(): void {
		const currentStatus = this.webcamService.getStatus();
		if (this._webcamStatus() !== currentStatus) {
			this._webcamStatus.set(currentStatus);
			this.triggerChangeDetection();
		}
	}

	/**
	 * Start periodic webcam status synchronization
	 */
	private startWebcamStatusSync(): void {
		// Initial sync
		this.syncWebcamStatus();

		// Set up periodic sync every 100ms for responsive status updates
		this.webcamStatusSyncInterval = setInterval(() => {
			this.syncWebcamStatus();
		}, 100);
	}

	/**
	 * Stop webcam status synchronization
	 */
	private stopWebcamStatusSync(): void {
		if (this.webcamStatusSyncInterval) {
			clearInterval(this.webcamStatusSyncInterval);
			this.webcamStatusSyncInterval = undefined;
		}
	}

	/**
	 * Check if ViewChild elements are available
	 */
	private areViewChildElementsReady(): boolean {
		return !!(this.videoElement?.nativeElement && this.svgOverlay?.nativeElement);
	}

	/**
	 * Wait for ViewChild elements to be ready with retry mechanism
	 */
	private async waitForViewChildElements(maxRetries: number = 5): Promise<boolean> {
		for (let i = 0; i < maxRetries; i++) {
			if (this.areViewChildElementsReady()) {
				return true;
			}
			console.warn(`ViewChild elements not ready, retry ${i + 1}/${maxRetries}`);
			await this.delay(100);
		}
		return false;
	}

	async ngAfterViewInit(): Promise<void> {
		try {
			// Reattach change detector for initialization
			this.reattachChangeDetector();

			// Wait for ViewChild elements to be ready
			const elementsReady = await this.waitForViewChildElements();
			if (!elementsReady) {
				throw new Error("ViewChild elements failed to initialize after multiple retries");
			}

			this.setupResizeObserver();
			await this.initializeCamera();

			// Start periodic webcam status sync
			this.startWebcamStatusSync();

			// Trigger initial change detection
			this.triggerChangeDetection();
		} catch (error) {
			await this.handleError(error, "Component initialization failed");
		}
	}

	ngOnDestroy(): void {
		this.cleanup();
	}

	/**
	 * Initialize camera and start webcam
	 */
	private async initializeCamera(): Promise<void> {
		try {
			// Check if video element is available
			if (!this.videoElement?.nativeElement) {
				throw new Error("Video element not available for camera initialization");
			}

			// Attach video element for camera preview
			this.webcamService.attachPreviewElement(this.videoElement.nativeElement);

			// Camera handler options
			const options: CameraHandlerOptions = {
				onStart: () => this.onCameraStart(),
				onError: (error: any) => this.onCameraError(error),
			};

			// Create camera configuration
			const configuration = await this.webcamService.createCameraConfiguration(
				"environment",
				ID_PHOTO_RESOLUTIONS,
				options,
			);
			if (configuration) {
				await this.webcamService.startCamera(configuration, options);
			}
		} catch (error) {
			await this.handleError(error, "Camera initialization failed");
		}
	}

	/**
	 * Handle camera start event
	 */
	private async onCameraStart(): Promise<void> {
		console.log("Camera started successfully");

		// Sync webcam status immediately
		this.syncWebcamStatus();

		this.updateOverlayDimensions();
		await this.startProcessing();

		// Trigger change detection for camera start
		this.triggerChangeDetection();
	}

	/**
	 * Handle camera error event
	 */
	private onCameraError(error: WebcamError): void {
		console.error("Camera error:", error);

		// Sync webcam status immediately
		this.syncWebcamStatus();

		this._scanningState.set(ScanningState.ERROR);
		this.handleError(error, "Camera initialization failed");
	}

	private async startProcessing(): Promise<void> {
		this._scanningState.set(ScanningState.IDLE);

		// Start processing only in auto mode
		if (this._processingMode() === CaptureMode.AUTO) {
			this._scanningState.set(ScanningState.SCANNING);
			await this.idCardDetectionService.startProcessing(
				"thai-front",
				this.handleScanSuccess.bind(this),
				this.handleOCRResult.bind(this),
			);
		}
	}

	/**
	 * Handle OCR result from document scanning
	 * Updates progress, guideline text, and overlay based on OCR results
	 */
	public handleOCRResult(result: DocumentScanResult): void {
		// Extract OCR data from DocumentScanResult
		const ocrResult: ProcessedOCRResult = {
			status: result.status,
			extractedText: result.recognition?.extractedText || "",
			idNumber: result.recognition?.idNumber || "",
			detectedWords: result.recognition?.detectedWords || [],
			textAfterID: result.recognition?.textAfterId || "",
			processingTimeMs: result.recognition?.processingTimeMs || 0,
		};

		this._ocrInfo.set(ocrResult);

		// Update progress based on OCR status
		const progressPercentage = SCAN_STATUS_PERCENTAGES[result.status] ?? 0;
		this._progress.set(progressPercentage);

		// Update guideline text based on extracted text length
		this.updateGuidelineText(result.recognition?.extractedText?.length ?? 0);

		// Update overlay dimensions to trigger color change
		this.updateOverlayDimensions();

		// Use throttled UI update for performance
		this.updateUIState();
	}

	/**
	 * Update guideline text based on extracted text length
	 */
	private updateGuidelineText(extractedTextLength: number): void {
		if (extractedTextLength > OCR_THRESHOLDS.minTextLength) {
			this._guidelineText.set(UI_MESSAGES.scanningGuideline);
		} else {
			this._guidelineText.set(UI_MESSAGES.initialGuideline);
		}
	}

	private async handleScanSuccess(result?: DocumentScanResult): Promise<void> {
		if (!result) {
			return;
		}

		// Update guideline text for success
		this._guidelineText.set(UI_MESSAGES.successGuideline);
		this._progress.set(100);

		this.idCardDetectionService.setThaiFront(result);
		this._scanningState.set(ScanningState.COMPLETED);

		// Trigger immediate change detection for success state
		this.triggerChangeDetection();

		await this.delay(ANIMATION_DURATIONS.success);
		this.next();
	}

	/**
	 * Setup resize observer for responsive overlay with performance optimization
	 */
	private setupResizeObserver(): void {
		if (!this.videoElement?.nativeElement) {
			console.warn("Video element not available for resize observer");
			return;
		}

		this.resizeObserver = new ResizeObserver(() => {
			// Clear existing timeouts
			if (this.resizeTimeoutId) {
				clearTimeout(this.resizeTimeoutId);
			}
			if (this.animationFrameId) {
				cancelAnimationFrame(this.animationFrameId);
			}

			// Debounce resize events for better performance
			this.resizeTimeoutId = setTimeout(() => {
				this.animationFrameId = requestAnimationFrame(() => {
					// Double-check elements are still available
					if (this.areViewChildElementsReady()) {
						this.updateOverlayDimensions();
						// Only trigger change detection if necessary
						this.updateUIState();
					}
				});
			}, PERFORMANCE_CONFIG.resizeDebounceTime);
		});

		this.resizeObserver.observe(this.videoElement.nativeElement);
	}

	public setOverlayWidthPercentage(percentage: number): void {
		if (percentage < 0.1 || percentage > 1.0) {
			console.warn("Overlay width percentage must be between 0.1 and 1.0");
			return;
		}

		ID_CARD_CONFIG.overlayWidthPercentage = percentage;
		this.updateOverlayDimensions();
	}

	public getOverlayWidthPercentage(): number {
		return ID_CARD_CONFIG.overlayWidthPercentage;
	}

	/**
	 * Get overlay status for UI display
	 */
	public getOverlayStatus(): string {
		const scanningState = this.currentScanningState();
		const ocrInfo = this.ocrInfo();

		// If we have OCR results and we're scanning, use OCR-based status
		if (scanningState === ScanningState.SCANNING && ocrInfo?.extractedText) {
			const extractedTextLength = ocrInfo.extractedText.length;
			return extractedTextLength > OCR_THRESHOLDS.minTextLength ? "green" : "white";
		}

		// For scanning state without OCR results, start with white
		if (scanningState === ScanningState.SCANNING) {
			return "white";
		}

		// Otherwise use current scanning state
		return scanningState;
	}

	/**
	 * Get overlay color based on current state and OCR results
	 */
	private _getOverlayColor(): string {
		const scanningState = this.currentScanningState();
		const ocrInfo = this.ocrInfo();

		// If we have OCR results and we're scanning, use OCR-based color logic
		if (scanningState === ScanningState.SCANNING && ocrInfo?.extractedText) {
			const extractedTextLength = ocrInfo.extractedText.length;
			return extractedTextLength > OCR_THRESHOLDS.minTextLength
				? STATUS_COLORS.ready
				: STATUS_COLORS.white;
		}

		// For scanning state without OCR results, start with white
		if (scanningState === ScanningState.SCANNING) {
			return STATUS_COLORS.white;
		}

		// Map scanning states to colors
		switch (scanningState) {
			case ScanningState.IDLE:
				return STATUS_COLORS.initializing;
			case ScanningState.COMPLETED:
				return STATUS_COLORS.success;
			case ScanningState.ERROR:
				return STATUS_COLORS.error;
			default:
				return STATUS_COLORS.white;
		}
	}

	/**
	 * Update overlay dimensions based on video size
	 */
	private updateOverlayDimensions(): void {
		const videoElement = this.videoElement?.nativeElement;
		if (!videoElement) {
			console.warn("Video element not available for overlay dimension update");
			return;
		}

		const { videoWidth, videoHeight } = videoElement;
		const { width: containerWidth, height: containerHeight } = videoElement.getBoundingClientRect();

		if (!videoWidth || !videoHeight || !containerWidth || !containerHeight) {
			console.warn("Video dimensions not available yet");
			return;
		}

		// Calculate actual video display size (object-contain)
		const videoAspectRatio = videoWidth / videoHeight;
		const containerAspectRatio = containerWidth / containerHeight;

		let actualWidth: number;
		let actualHeight: number;

		if (containerAspectRatio > videoAspectRatio) {
			actualHeight = containerHeight;
			actualWidth = actualHeight * videoAspectRatio;
		} else {
			actualWidth = containerWidth;
			actualHeight = actualWidth / videoAspectRatio;
		}

		// Calculate overlay dimensions based on ID card aspect ratio
		const overlayWidth = actualWidth * ID_CARD_CONFIG.overlayWidthPercentage;
		const overlayHeight = overlayWidth / ID_CARD_CONFIG.aspectRatio;

		// Ensure overlay doesn't exceed container height
		const maxOverlayHeight = actualHeight * 0.9; // Max 90% of video height
		const finalOverlayWidth =
			overlayHeight > maxOverlayHeight
				? maxOverlayHeight * ID_CARD_CONFIG.aspectRatio
				: overlayWidth;
		const finalOverlayHeight = finalOverlayWidth / ID_CARD_CONFIG.aspectRatio;

		const scale = finalOverlayWidth / ID_CARD_CONFIG.baseWidth;

		const offsetX = (containerWidth - finalOverlayWidth) / 2;
		const offsetY = (containerHeight - finalOverlayHeight) / 2;

		this._overlayDimensions.set({
			width: finalOverlayWidth,
			height: finalOverlayHeight,
			scale,
			offsetX,
			offsetY,
		});

		this.updateOverlayStyles();
	}

	/**
	 * Update SVG overlay styles
	 */
	private updateOverlayStyles(): void {
		const dimensions = this._overlayDimensions();
		const svgElement = this.svgOverlay?.nativeElement;

		if (!dimensions || !svgElement) {
			console.warn("Overlay dimensions or SVG element not available for style update");
			return;
		}

		const { width, height, offsetX, offsetY } = dimensions;
		const color = this._getOverlayColor();

		// Update SVG container position and size
		this.renderer.setStyle(svgElement, "position", "absolute");
		this.renderer.setStyle(svgElement, "left", `${offsetX}px`);
		this.renderer.setStyle(svgElement, "top", `${offsetY}px`);
		this.renderer.setStyle(svgElement, "width", `${width}px`);
		this.renderer.setStyle(svgElement, "height", `${height}px`);
		this.renderer.setStyle(svgElement, "pointer-events", "none");
		this.renderer.setStyle(svgElement, "z-index", "10");

		// Update path color based on OCR results or status
		this.renderer.setStyle(svgElement, "color", color);

		// Add transition for smooth color changes
		this.renderer.setStyle(svgElement, "transition", "color 0.3s ease-out");
	}

	private async cleanup(): Promise<void> {
		this.destroy$.next();
		this.destroy$.complete();

		// Stop webcam status synchronization
		this.stopWebcamStatusSync();

		// Clean up performance optimization timers
		if (this.resizeTimeoutId) {
			clearTimeout(this.resizeTimeoutId);
			this.resizeTimeoutId = undefined;
		}

		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
			this.resizeObserver = undefined;
		}

		if (this.animationFrameId) {
			cancelAnimationFrame(this.animationFrameId);
			this.animationFrameId = undefined;
		}

		// Reattach change detector before cleanup to ensure proper cleanup
		this.reattachChangeDetector();

		// Stop processing
		await this.idCardDetectionService.stopProcessing();
		this.webcamService.instance.stop();
		this._scanningState.set(ScanningState.IDLE);
	}

	/**
	 * Handle photo capture with performance optimization
	 */
	public async handleTakePhoto(): Promise<void> {
		if (this.currentWebcamStatus() !== "ready") return;

		try {
			this._scanningState.set(ScanningState.SCANNING);
			this.triggerChangeDetection();

			// Simulate capture process
			await this.simulateCapture();

			// Take actual photo
			const photoData = await this.webcamService.takePhoto();
			if (photoData) {
				this._scanningState.set(ScanningState.COMPLETED);
				this.triggerChangeDetection();
				await this.delay(ANIMATION_DURATIONS.success);
				this.next();
			} else {
				throw new Error("Failed to capture photo");
			}
		} catch (error) {
			this._scanningState.set(ScanningState.ERROR);
			this.triggerChangeDetection();
			await this.handleError(error, "Photo capture failed");
		}
	}

	public async handleMirrorClick(): Promise<void> {
		this.webcamService.instance.toggleMirror();
	}

	/**
	 * Retry capture process - reset state and restart scanning with performance optimization
	 */
	public async retryCapture(): Promise<void> {
		try {
			// Reset all state using utility method
			this.resetScanningState();
			this.triggerChangeDetection();

			// Restart processing if in auto mode
			await this.startProcessing();
		} catch (error) {
			this._scanningState.set(ScanningState.ERROR);
			this.triggerChangeDetection();
			await this.handleError(error, "Retry capture failed");
		}
	}

	/**
	 * Simulate capture process with progress
	 */
	private async simulateCapture(): Promise<void> {
		return new Promise((resolve) => {
			let progress = 0;
			const interval = setInterval(() => {
				progress += 10;
				this._progress.set(progress);

				if (progress >= 100) {
					clearInterval(interval);
					resolve();
				}
			}, OCR_THRESHOLDS.progressUpdateDelay);
		});
	}

	/**
	 * Utility delay function
	 */
	private delay(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * Check if scanning is in progress
	 */
	public isScanningInProgress(): boolean {
		return this.currentScanningState() === ScanningState.SCANNING;
	}

	/**
	 * Check if scanning is completed
	 */
	public isScanningCompleted(): boolean {
		return this.currentScanningState() === ScanningState.COMPLETED;
	}

	/**
	 * Check if there's an error state
	 */
	public hasError(): boolean {
		return this.currentScanningState() === ScanningState.ERROR;
	}

	/**
	 * Reset scanning state to idle
	 */
	public resetScanningState(): void {
		this._scanningState.set(ScanningState.IDLE);
		this._progress.set(0);
		this._ocrInfo.set(null);
		this._guidelineText.set(UI_MESSAGES.initialGuideline);
	}

	/**
	 * Set capture mode (AUTO or MANUAL)
	 */
	public setCaptureMode(mode: CaptureMode): void {
		this._processingMode.set(mode);
	}

	/**
	 * Get current capture mode
	 */
	public getCaptureMode(): CaptureMode {
		return this._processingMode();
	}

	/**
	 * Get current scanning progress percentage
	 */
	public getScanningProgress(): number {
		return this._progress();
	}

	/**
	 * Performance monitoring method
	 */
	public getPerformanceMetrics(): {
		changeDetectionCycles: number;
		isDetached: boolean;
		lastOcrUpdateTime: number;
	} {
		return {
			changeDetectionCycles: this.changeDetectionCycles,
			isDetached: this.isDetached,
			lastOcrUpdateTime: this.lastOcrUpdateTime,
		};
	}

	/**
	 * Force change detection (use sparingly for debugging)
	 */
	public forceChangeDetection(): void {
		this.cdr.detectChanges();
	}

	/**
	 * Force immediate webcam status sync (for manual updates)
	 */
	public forceWebcamStatusSync(): void {
		this.syncWebcamStatus();
	}

	private _getButtonLabel(): string {
		const status = this.currentScanningState();
		switch (status) {
			case ScanningState.SCANNING:
				return "กำลังถ่าย...";
			case ScanningState.IDLE:
				return "รอสักครู่...";
			case ScanningState.COMPLETED:
				return "เสร็จสิ้น";
			case ScanningState.ERROR:
				return "ลองใหม่";
			default:
				return "ถ่ายรูป";
		}
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage ? `${errorMessage} : ${apiMessage}` : errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
