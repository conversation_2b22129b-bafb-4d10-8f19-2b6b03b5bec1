<ion-content>
	<div class="app-layout-dialog">
		<div class="app-content">
			<div class="content-wrapper">
				<div class="header-section">
					<h1 class="page-title">
						{{ guidelineText() }}
					</h1>
					<p class="page-subtitle">ระบบกำลังทำการถ่ายรูปหน้าบัตรประชาชนของท่าน</p>
					<div class="progress-container">
						<div class="progress-bar" [style.width.%]="captureProgress()"></div>
					</div>
				</div>
				<div class="camera-container">
					<div class="mirror-button-container">
						<p-button
							[rounded]="true"
							[outlined]="true"
							[loading]="isLoading()"
							(onClick)="handleMirrorClick()">
							<img src="assets/svgs/switch-camera.svg" alt="Switch Camera" class="h-5 w-5" />
						</p-button>
					</div>
					<video #videoElement autoplay playsinline muted class="camera-video"></video>
					<svg
						#svgOverlay
						class="id-card-svg-overlay"
						[attr.data-status]="getOverlayStatus()"
						width="338"
						height="212"
						viewBox="0 0 338 212"
						fill="none"
						xmlns="http://www.w3.org/2000/svg">
						<path
							fill-rule="evenodd"
							clip-rule="evenodd"
							d="M329.622 4H8C5.79087 4 4 5.79087 4 8.00001V203.014C4 205.223 5.79086 207.014 8 207.014H329.622C331.831 207.014 333.622 205.223 333.622 203.014V8C333.622 5.79086 331.831 4 329.622 4ZM8 0C3.58172 0 0 3.58173 0 8.00001V203.014C0 207.432 3.58172 211.014 8 211.014H329.622C334.041 211.014 337.622 207.432 337.622 203.014V8C337.622 3.58172 334.041 0 329.622 0H8Z"
							fill="currentColor" />
					</svg>
					<div *ngIf="currentWebcamStatus() === 'initializing'" class="loading-overlay">
						<ion-spinner name="circular" class="loading-spinner"></ion-spinner>
						<p class="loading-text">กำลังเปิดกล้อง...</p>
					</div>
					<div *ngIf="isScanningCompleted()" class="success-overlay"></div>
					<div *ngIf="isScanningCompleted()" class="success-content">
						<div class="circle-green">
							<div class="check-icon"></div>
						</div>
						<p class="success-text">ถ่ายรูปสำเร็จ!</p>
					</div>
					<div *ngIf="currentWebcamStatus() === 'error' || hasError()" class="error-overlay">
						<div class="error-icon">⚠️</div>
						<p class="error-text">เกิดข้อผิดพลาด</p>
						<button class="retry-button" (click)="retryCapture()">ลองใหม่</button>
					</div>
				</div>
			</div>
		</div>

		<app-footer styleClass="!border-none">
			<div class="footer-wrapper !justify-center">
				<p-button
					[label]="buttonLabel()"
					styleClass="w-full"
					class="w-full max-w-md"
					[loading]="isLoading()"
					[disabled]="!isReadyForCapture() || captureMode() === 'auto'"
					(click)="handleTakePhoto()" />
			</div>
		</app-footer>
	</div>
</ion-content>
