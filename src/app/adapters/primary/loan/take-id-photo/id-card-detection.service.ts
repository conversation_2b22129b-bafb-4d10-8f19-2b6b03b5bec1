import { computed, inject, Injectable, signal } from "@angular/core";
import { Capacitor } from "@capacitor/core";
import { OcrDetectionService } from "src/app/core/application/ocr-detection.service";
import { CropConfigurationPreset, CropConfigurationPresets } from "src/app/core/utils/crop-preset";
import { ImageProcessorUtils } from "src/app/core/utils/image-processor-utils";
import { Resolution } from "ts-webcam";
import { CardRegExpService } from "../../shared/services/card-reg-exp.service";
import { UaInfoService } from "../../shared/services/ua-info.service";
import { WebcamService } from "../../shared/services/webcam.service";

export enum DocumentScanStatus {
	Success = "success",
	Failed = "failed",
	Error = "error",
	ValidationFailed = "validation-failed",
	// OCR-related states
	OcrFailed = "ocr-failed",
	TextNotFound = "text-not-found",
	TextNotEnough = "text-not-enough",
	WordNotEnough = "word-not-enough",
	InvalidIdCardNumber = "invalid-id-card-number",
	// YOLO-related state
	NoImageDetected = "no-image-detected",
	DocumentNotFound = "document-not-found",
	LowConfidence = "low-confidence",
	InvalidDocument = "invalid-document",
	InvalidFormat = "invalid-format",
	InvalidType = "invalid-type",
	InvalidSize = "invalid-size",
}

export interface DocumentScanResult {
	// Basic information
	timestamp: string;
	processingTimeMs: number;
	status: DocumentScanStatus;

	// OCR Recognition Result
	recognition: {
		isValidIdNumber: boolean;
		extractedText: string;
		idNumber: string | null;
		detectedWords: string[];
		textAfterId: string;
		processingTimeMs: number;
	} | null;

	// Processed Images
	images: {
		original: string;
		cropped: string | null;
	};

	// Errors (if any)
	errors?: string[];
}

export interface DocumentScanConfiguration {
	// Image Processing Configuration
	imageProcessing: {
		cropConfig: CropConfigurationPreset;
		mirrorMode: boolean; // Whether to mirror the image
		scaleFactor: number; // Scaling factor for resizing images
		frameInterval: number;
		frameDelay: number;
	};

	// OCR Configuration
	ocr: {
		minWordsLength: number; // Minimum number of words required for valid OCR
		minTextLength: number; // Minimum length of text required for valid OCR
	};
}

export interface ProcessedOCRResult {
	status: DocumentScanStatus;
	extractedText?: string;
	idNumber?: string | null;
	detectedWords?: string[];
	textAfterID?: string;
	processingTimeMs: number;
}

@Injectable({
	providedIn: "root",
})
export class IdCardDetectionService {
	private webcamService = inject(WebcamService);
	private ocrDetectionService = inject(OcrDetectionService);
	private cardRegExpService = inject(CardRegExpService);
	private uaInfoService = inject(UaInfoService);

	private _isProcessing = signal<boolean>(false);
	private _frameCount = signal<number>(0);
	private _currentMode = signal<"thai-front" | "thai-back">("thai-front");
	private _thaiFront = signal<DocumentScanResult | null>(null);
	private _thaiBack = signal<DocumentScanResult | null>(null);

	public readonly isProcessing = computed(() => this._isProcessing());
	public readonly currentMode = computed(() => this._currentMode());
	public readonly thaiFront = computed(() => this._thaiFront());
	public readonly thaiBack = computed(() => this._thaiBack());

	private handleSuccessfulScan: ((result?: DocumentScanResult) => Promise<void>) | null = null;
	private handleOCRResult: ((ocrResult: DocumentScanResult) => void) | null = null;

	// Default configuration
	private config: DocumentScanConfiguration = {
		imageProcessing: {
			cropConfig: CropConfigurationPresets[1],
			mirrorMode: false, // Disable mirror mode by default
			scaleFactor: 1, // No scaling by default
			frameInterval: 10,
			frameDelay: 50,
		},
		ocr: {
			minWordsLength: 2, // Require at least 2 words for valid OCR
			minTextLength: 10, // Require at least 10 characters for valid OCR
		},
	};

	constructor() {}

	/**
	 * Set front id card result
	 */
	public setThaiFront(result: DocumentScanResult | null) {
		this._thaiFront.set(result);
	}

	/**
	 * Set back id card result
	 */
	public setThaiBack(result: DocumentScanResult | null) {
		this._thaiBack.set(result);
	}

	public async setupScanConfiguration() {
		const isAndroid = this.uaInfoService.isOS("Android");
		const isIOS = this.uaInfoService.isOS(["MacOS", "iOS"]);

		if (isAndroid) {
			this.setupAndroidConfiguration();
		} else if (isIOS) {
			this.setupIOSConfiguration();
		}

		const resolution = this.webcamService.instance.getCurrentResolution();
		this.setupResolutionConfiguration(resolution);
	}

	public setupResolutionConfiguration(resolution: Resolution | null) {
		if (!resolution) {
			return;
		}

		const name = resolution?.label;
		let scaleFactor = 1;

		if (this.currentMode() !== "thai-back") {
			switch (name) {
				case "SQUARE-1920":
					scaleFactor = 0.8;
					break;
				case "SQUARE-1440":
					scaleFactor = 0.85;
					break;
				case "SQUARE-1280":
					scaleFactor = 0.9;
					break;
				case "SQUARE-1080":
					scaleFactor = 0.95;
					break;
				case "SQUARE-720":
				case "SQUARE-480":
					scaleFactor = 1;
					break;
				default:
					scaleFactor = 1;
					break;
			}
		}

		// Apply the selected preset to the configuration
		this.config = {
			...this.config,
			imageProcessing: {
				...this.config.imageProcessing,
				scaleFactor: scaleFactor,
			},
		};
	}

	public applyResizeScaleFactor(scaleFactor: number) {
		this.config.imageProcessing.scaleFactor = scaleFactor;
	}

	private setupAndroidConfiguration() {
		const cpuCores = this.uaInfoService.getCpuCoreCount() || 0;
		const ramSize = this.uaInfoService.getMemory() || 0;

		if (cpuCores < 8) {
			this.applyConfiguration("extra-low");
		} else {
			if (ramSize === 0) {
				this.applyConfiguration("extra-low");
			} else if (ramSize <= 2) {
				this.applyConfiguration("extra-low");
			} else if (ramSize > 3 && ramSize < 6) {
				this.applyConfiguration("low");
			} else if (ramSize >= 6 && ramSize < 8) {
				this.applyConfiguration("medium");
			} else if (ramSize >= 8) {
				this.applyConfiguration("high");
			}
		}
	}

	private setupIOSConfiguration() {
		if (!this.uaInfoService.isOSVersionAtLeast("16")) {
			this.applyConfiguration("low");
		} else {
			this.applyConfiguration("high");
		}
	}

	private applyConfiguration(preset: "extra-low" | "low" | "medium" | "high") {
		const configurationPresets = {
			"extra-low": {
				cropConfig: CropConfigurationPresets[0],
				minWordsLength: 0,
				minTextLength: 10,
				frameInterval: 10,
			},
			"low": {
				cropConfig: CropConfigurationPresets[1],
				minWordsLength: 2,
				minTextLength: 20,
				frameInterval: 10,
			},
			"medium": {
				cropConfig: CropConfigurationPresets[2],
				minWordsLength: 2,
				minTextLength: 20,
				frameInterval: 5,
			},
			"high": {
				cropConfig: CropConfigurationPresets[3],
				minWordsLength: 3,
				minTextLength: 30,
				frameInterval: 5,
			},
		};

		// Apply the selected preset to the configuration
		const selectedPreset = configurationPresets[preset];
		const isFrontCard = this.currentMode() === "thai-front";
		const cropPreset = isFrontCard ? selectedPreset.cropConfig : CropConfigurationPresets[4];

		this.config = {
			...this.config,
			imageProcessing: {
				...this.config.imageProcessing,
				cropConfig: cropPreset,
			},
			ocr: {
				...this.config.ocr,
				minWordsLength: selectedPreset.minWordsLength,
				minTextLength: selectedPreset.minTextLength,
			},
		};
	}

	async startProcessing(
		mode: "thai-front" | "thai-back",
		handleSuccessfulScan: (result?: DocumentScanResult) => Promise<void>,
		handleOCRResult?: (result: DocumentScanResult) => void,
	): Promise<void> {
		if (this.isProcessing()) {
			return;
		}

		try {
			// Set mode
			this._currentMode.set(mode);

			// Setup configuration
			await this.setupScanConfiguration();

			// update handle success function
			this.handleSuccessfulScan = handleSuccessfulScan;
			this.handleOCRResult = handleOCRResult || null;

			// Start processing
			this._isProcessing.set(true);

			// Start processing
			await this.runProcessing();
		} catch (error) {
			throw error;
		}
	}

	async stopProcessing(): Promise<void> {
		// clear all results
		this._isProcessing.set(false);
		// this.resultSubject.next(null);
		// this.frameCount = 0;
	}

	private async runProcessing(): Promise<void> {
		if (!this.isProcessing()) {
			return; // หยุดการประมวลผลหากไม่ได้อยู่ในสถานะประมวลผล
		}

		try {
			// Increment frame count
			this._frameCount.update((count) => count + 1);

			// Check frame interval
			if (!this.isFrameIntervalMet()) {
				return await this.scheduleNextFrame();
			}

			// Capture and validate frame
			const frame = await this.captureFrame();
			// Validate frame uri
			if (!frame) {
				return await this.scheduleNextFrame();
			}

			const result = await this.processNextFrame(frame, performance.now());
			console.log("Result:", result);
			// Call OCR result handler for real-time feedback (both success and failure)
			if (this.handleOCRResult && result) {
				this.handleOCRResult(result);
			}

			if (!result || result.status !== DocumentScanStatus.Success) {
				return await this.runProcessing();
			}

			if (result.status === DocumentScanStatus.Success && result !== undefined) {
				this._isProcessing.set(false);

				// handle success case function
				if (this.handleSuccessfulScan) {
					await this.handleSuccessfulScan(result);
				}

				return;
			}

			if (!this.isProcessing()) return;
			return await this.runProcessing();
		} catch (error) {
			console.error("Processing Error:", error);
			if (!this.isProcessing()) return;
			return await this.runProcessing();
		}
	}

	/**
	 * Check if the frame interval is met.
	 */
	private isFrameIntervalMet(): boolean {
		return this._frameCount() % 5 === 0;
	}

	/**
	 * Check if the frame is valid.
	 */
	private isFrameValid(frame: string | null): boolean {
		if (!frame || frame === null) {
			console.warn("Invalid or missing frame");
			return false;
		}

		return true;
	}

	/**
	 * Schedules the next frame for processing, given the current stage.
	 */
	private async scheduleNextFrame(): Promise<void> {
		if (!this._isProcessing()) {
			return;
		}

		await this.runProcessing();
	}

	private async captureFrame(): Promise<string | null> {
		try {
			return await this.webcamService.takePhoto({
				imageType: "image/jpeg",
				quality: 0.92,
				scale: 1,
			});
		} catch (error) {
			console.error("Error capturing frame:", error);
			return null;
		}
	}

	private async processNextFrame(
		imageDataUrl: string,
		startTime: number,
	): Promise<DocumentScanResult> {
		try {
			if (!imageDataUrl) {
				return this.createEmptyScanResult(
					startTime,
					DocumentScanStatus.NoImageDetected,
					imageDataUrl,
				);
			}

			const targetImageOcr = await this.cropImageForOcr(imageDataUrl);
			const ocrProcessing = await this.runOCR(targetImageOcr);
			if (ocrProcessing.status !== DocumentScanStatus.Success) {
				return this.createEmptyScanResult(
					startTime,
					ocrProcessing.status,
					imageDataUrl,
					ocrProcessing,
				);
			}

			// Step 3: Validate the ID number extracted from OCR
			const isValidIdNumber =
				this.currentMode() === "thai-front"
					? this.cardRegExpService.validThaiIdNumber(ocrProcessing.idNumber || "")
					: (ocrProcessing?.idNumber?.length ?? 0) > 0;

			if (!isValidIdNumber) {
				return this.createEmptyScanResult(
					startTime,
					DocumentScanStatus.InvalidFormat,
					imageDataUrl,
					ocrProcessing,
				);
			}

			if (!ocrProcessing) {
				return this.createEmptyScanResult(
					startTime,
					DocumentScanStatus.Failed,
					imageDataUrl,
					ocrProcessing,
				);
			}

			const finalStatus = this.determineEnhancedProcessingState(ocrProcessing, {
				isValidIdNumber: isValidIdNumber,
			});

			return this.createScanResult(startTime, finalStatus, imageDataUrl, ocrProcessing);
		} catch (error) {
			console.error("Processing error:", error);
			return this.createEmptyScanResult(startTime, DocumentScanStatus.Error, imageDataUrl);
		}
	}

	private async cropImageForOcr(imageDataUrl: string) {
		try {
			const cropRatio = this.config.imageProcessing.cropConfig.config;
			return await ImageProcessorUtils.cropFromCenterWithConfig(imageDataUrl, cropRatio);
		} catch (error) {
			console.error("Error processing image:", error);
			throw error;
		}
	}

	/**
	 * Perform OCR on the image and extract relevant information
	 */
	private async runOCR(imageDataUrl: string): Promise<ProcessedOCRResult> {
		const startTime = performance.now();
		try {
			// Process OCR
			const ocrResult = await this.processOcrText(imageDataUrl);

			// Return immediately if OCR fails
			if (ocrResult.status !== DocumentScanStatus.Success || !ocrResult.text) {
				return this.createOcrResult(
					ocrResult.status,
					ocrResult.text || "",
					"",
					[],
					"",
					ocrResult.processingTimeMs || performance.now() - startTime,
				);
			}

			// Process OCR result
			const processedText = this.processOcrResult(ocrResult.text.trim());

			return this.createOcrResult(
				DocumentScanStatus.Success,
				processedText.extractedText || "",
				processedText.idNumber || "",
				processedText.detectedWords || [],
				processedText.textAfterID || "",
				performance.now() - startTime,
			);
		} catch (error) {
			console.error("Error processing OCR:", error);
			return this.createOcrResult(
				DocumentScanStatus.Error,
				"",
				"",
				[],
				"",
				performance.now() - startTime,
			);
		}
	}

	private determineEnhancedProcessingState(
		ocrResult: ProcessedOCRResult,
		metadata: {
			isValidIdNumber: boolean;
		},
	): DocumentScanStatus {
		if (!ocrResult) {
			return DocumentScanStatus.TextNotFound;
		}

		const { name } = this.config.imageProcessing.cropConfig;
		const { isValidIdNumber } = metadata;
		const { extractedText, detectedWords, textAfterID } = ocrResult;

		if (this.currentMode() === "thai-front") {
			// Cache repeated calculations
			const extractedTextLength = extractedText?.length || 0;
			const detectedWordsLength = detectedWords?.length || 0;
			const textAfterIDLength = textAfterID?.length || 0;

			// Common validation logic
			const hasSufficientText = extractedTextLength >= this.config.ocr.minTextLength;
			const hasSufficientWords = detectedWordsLength >= this.config.ocr.minWordsLength;
			const hasSufficientTextAfterID = textAfterIDLength >= 8;

			// Determine state based on crop configuration
			switch (name) {
				case "extra-low":
					if (isValidIdNumber && hasSufficientText) {
						return DocumentScanStatus.Success;
					}
					if (!isValidIdNumber && hasSufficientText) {
						return !isValidIdNumber
							? DocumentScanStatus.InvalidFormat
							: DocumentScanStatus.InvalidDocument;
					}
					break;

				case "low":
					if (isValidIdNumber && hasSufficientWords && hasSufficientText) {
						return DocumentScanStatus.Success;
					}
					if (!isValidIdNumber && hasSufficientWords && hasSufficientText) {
						return !isValidIdNumber
							? DocumentScanStatus.InvalidFormat
							: DocumentScanStatus.InvalidDocument;
					}
					if (isValidIdNumber && !hasSufficientWords && hasSufficientText) {
						return DocumentScanStatus.WordNotEnough;
					}
					break;

				case "medium":
				case "high":
					if (isValidIdNumber && hasSufficientWords && hasSufficientTextAfterID) {
						return DocumentScanStatus.Success;
					}
					if (isValidIdNumber && hasSufficientTextAfterID) {
						return DocumentScanStatus.WordNotEnough;
					}
					if (isValidIdNumber && hasSufficientWords && hasSufficientTextAfterID) {
						return DocumentScanStatus.InvalidSize;
					}
					if (hasSufficientWords) {
						return DocumentScanStatus.InvalidFormat;
					}
					break;
			}
		} else if (this.currentMode() === "thai-back") {
			if (!extractedText) {
				return DocumentScanStatus.TextNotFound;
			}

			if (extractedText.length <= 5) {
				return DocumentScanStatus.TextNotEnough;
			}

			if (!isValidIdNumber) {
				return DocumentScanStatus.InvalidFormat;
			}

			return DocumentScanStatus.Success;
		}

		// Default fallback
		return DocumentScanStatus.Failed;
	}

	/**
	 * Processes OCR
	 */
	private async processOcrText(croppedImage: string): Promise<{
		text: string | null;
		status: DocumentScanStatus;
		processingTimeMs: number;
	}> {
		const startTime = performance.now();
		try {
			let extractedText: string | null = null;
			if (Capacitor.isNativePlatform()) {
				// Process with MLKit for native platforms
				// const result = await this.ocrDetectionService.processImageWithMlKit(croppedImage);
				extractedText = null;
			} else {
				// Process with Tesseract for web
				const bitmap = await ImageProcessorUtils.base64ToImageBitmap(croppedImage);
				extractedText = await this.ocrDetectionService.getOCR(bitmap, true);
				extractedText = extractedText.replace(/\s/g, "").replace(/\n/g, "");
			}

			// Validate extracted text
			if (!extractedText) {
				return this.createOcrErrorResult(DocumentScanStatus.TextNotFound, startTime);
			}

			return {
				text: extractedText,
				status: DocumentScanStatus.Success,
				processingTimeMs: performance.now() - startTime,
			};
		} catch (error) {
			console.error("OCR processing error:", error);
			return this.createOcrErrorResult(DocumentScanStatus.Error, startTime);
		}
	}

	private processOcrResult(text: string): ProcessedOCRResult {
		const startTime = performance.now();
		try {
			// Validate input text
			if (!text || typeof text !== "string") {
				return {
					extractedText: text || "",
					idNumber: null,
					detectedWords: [],
					textAfterID: "",
					status: DocumentScanStatus.TextNotFound, // Indicate no valid text
					processingTimeMs: performance.now() - startTime,
				};
			}

			switch (this.currentMode()) {
				case "thai-front":
					return this.processThaiFront(text, startTime);
				case "thai-back":
					return this.processThaiBack(text, startTime);
				default:
					return {
						extractedText: text || "",
						idNumber: null,
						detectedWords: [],
						textAfterID: "",
						status: DocumentScanStatus.Error, // Indicate a general error
						processingTimeMs: performance.now() - startTime,
					};
			}
		} catch (error) {
			console.error("Error processing OCR result:", error);
			return {
				extractedText: text || "",
				idNumber: null,
				detectedWords: [],
				textAfterID: "",
				status: DocumentScanStatus.Error, // Indicate a general error
				processingTimeMs: performance.now() - startTime,
			};
		}
	}

	private processThaiFront(text: string, startTime: number): ProcessedOCRResult {
		// Clean up text
		const cleanedText = text.trim();

		// Extract ID number
		const idNumber = this.cardRegExpService.extractThaiIdNumber(cleanedText);
		if (!idNumber) {
			return {
				extractedText: cleanedText,
				idNumber: null,
				detectedWords: [],
				textAfterID: "",
				status: DocumentScanStatus.InvalidFormat, // Indicate invalid format (no ID number found)
				processingTimeMs: performance.now() - startTime,
			};
		}

		// Detect English words
		const detectedWords = this.cardRegExpService.detectEnglishWords(cleanedText);

		// Extract text after ID
		const textAfterID = this.cardRegExpService.findTextAfterID(cleanedText, idNumber);

		// Success case
		return {
			extractedText: cleanedText,
			idNumber,
			detectedWords,
			textAfterID,
			status: DocumentScanStatus.Success, // Indicate successful processing
			processingTimeMs: performance.now() - startTime,
		};
	}

	processThaiBack(text: string, startTime: number): ProcessedOCRResult {
		// Clean up text
		const cleanedText = this.cardRegExpService.cleanLaserIdText(text);
		const idNumber = this.cardRegExpService.extractLaserIdNumber(cleanedText);
		if (!idNumber) {
			return {
				extractedText: cleanedText,
				idNumber: null,
				detectedWords: [],
				textAfterID: "",
				status: DocumentScanStatus.InvalidFormat, // Indicate invalid format (no ID number found)
				processingTimeMs: performance.now() - startTime,
			};
		}

		return {
			extractedText: cleanedText,
			idNumber: idNumber,
			detectedWords: [],
			textAfterID: "",
			status: DocumentScanStatus.Success,
			processingTimeMs: performance.now() - startTime,
		};
	}

	/**
	 * Utility function to create OCR result object
	 */
	private createOcrResult(
		status: DocumentScanStatus,
		extractedText: string,
		idNumber: string,
		detectedWords: string[],
		textAfterID: string,
		processingTimeMs: number,
	): ProcessedOCRResult {
		return {
			status,
			extractedText,
			idNumber,
			detectedWords,
			textAfterID,
			processingTimeMs,
		};
	}

	/**
	 *  Utility function to create OCR error result
	 */
	private createOcrErrorResult(
		status: DocumentScanStatus,
		startTime: number,
	): {
		text: string | null;
		status: DocumentScanStatus;
		processingTimeMs: number;
	} {
		return {
			text: null,
			status,
			processingTimeMs: performance.now() - startTime,
		};
	}

	/**
	 * Create a ScanResult object
	 */
	private createScanResult(
		startTime: number,
		status: DocumentScanStatus,
		imageDataUrl: string,
		ocrInfo: ProcessedOCRResult,
	): DocumentScanResult {
		return {
			timestamp: new Date().toISOString(),
			processingTimeMs: performance.now() - startTime,
			status: status,
			recognition: {
				isValidIdNumber: this.cardRegExpService.validThaiIdNumber(ocrInfo.idNumber || ""),
				extractedText: ocrInfo.extractedText || "",
				idNumber: ocrInfo.idNumber || "",
				detectedWords: ocrInfo.detectedWords || [],
				textAfterId: ocrInfo.textAfterID || "",
				processingTimeMs: ocrInfo.processingTimeMs,
			},
			images: {
				original: imageDataUrl,
				cropped: "",
			},
			errors: [],
		};
	}

	/**
	 * Create empty scan result
	 */
	private createEmptyScanResult(
		startTime: number,
		status: DocumentScanStatus,
		imageDataUrl: string,
		ocrInfo?: ProcessedOCRResult,
		errors: string[] = [],
	): DocumentScanResult {
		return {
			timestamp: new Date().toISOString(),
			processingTimeMs: performance.now() - startTime,
			status: status,
			recognition: ocrInfo
				? {
						isValidIdNumber: false,
						extractedText: ocrInfo.extractedText || "",
						idNumber: ocrInfo.idNumber || "",
						detectedWords: ocrInfo.detectedWords || [],
						textAfterId: ocrInfo.textAfterID || "",
						processingTimeMs: ocrInfo.processingTimeMs || 0,
					}
				: null,
			images: {
				original: imageDataUrl,
				cropped: "",
			},
			errors,
		};
	}
}
