import { CommonModule } from "@angular/common";
import { Component, OnInit, inject, signal } from "@angular/core";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { UaInfoService } from "src/app/adapters/primary/shared/services/ua-info.service";
import { WebcamService } from "src/app/adapters/primary/shared/services/webcam.service";
import {
	CameraCheckResult,
	CheckStatus,
	DeviceCheckItem,
} from "src/app/core/application/device-check.service";
import { MediaPipeService } from "src/app/core/application/media-pipe.service";
import {
	ENGLISH_FAST_MODEL,
	OcrDetectionService,
} from "src/app/core/application/ocr-detection.service";
import { DeviceManagerUtils } from "src/app/core/utils/device-manager-utils";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	standalone: true,
	imports: [CommonModule, IonicModule, FooterComponent, ButtonModule],
	selector: "app-webcam-check",
	styleUrl: "./webcam-check.component.scss",
	template: `
		<ion-content>
			<div class="app-layout-dialog">
				<div class="app-content">
					<div class="content-wrapper">
						<!-- Header -->
						<div class="text-center">
							<p class="page-title">ตรวจสอบรายการความพร้อมของอุปกรณ์</p>
						</div>

						<!-- Webcam Image -->
						<div class="my-8 flex flex-col items-center justify-center">
							<img class="h-auto max-w-[160px]" src="assets/svgs/webcam.svg" />
						</div>

						<!-- Check Items -->
						<div
							*ngFor="let item of checkItems"
							class="bg-white transition-all duration-300">
							<div class="flex flex-row py-2">
								<!-- Status Icon -->
								<div
									[ngClass]="getStatusClass(item.status)"
									class="flex h-8 w-8 shrink-0 items-center justify-center rounded-full text-xs">
									<span
										*ngIf="item.status === 'checking'"
										class="spinner"></span>
									<span *ngIf="item.status === 'pending'">
										{{ getItemIndex(item) + 1 }}
									</span>
									<img
										*ngIf="
											item.status !== 'checking' && item.status !== 'pending'
										"
										[src]="getStatusIcon(item.status)"
										class="status-icon" />
								</div>
								<!-- Description and Error -->
								<div class="ml-4 flex-1">
									<p class="mt-1 text-sm" [ngClass]="getTextClass(item.status)">
										{{ item.description }}
									</p>
									<p
										*ngIf="getErrorMessage(item)"
										class="mt-1 text-xs text-red-500">
										{{ getErrorMessage(item) }}
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer styleClass="!border-none">
					<div class="footer-wrapper !justify-center">
						<p-button
							[label]="allChecksCompleted() ? 'ต่อไป' : 'ตรวจสอบอีกครั้ง'"
							styleClass="w-full"
							class="w-full max-w-md"
							[loading]="isLoading()"
							[disabled]="isChecking()"
							(onClick)="handleButtonClick()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class WebcamCheckComponent
	extends WorkflowStepBaseComponent
	implements OnInit
{
	private webcamService = inject(WebcamService);
	private uaInfoService = inject(UaInfoService);
	private mediaPipeService = inject(MediaPipeService);
	private ocrDetectionService = inject(OcrDetectionService);

	private _isChecking = signal<boolean>(false);
	public isChecking = this._isChecking.asReadonly();

	private _allChecksCompleted = signal<boolean>(false);
	public allChecksCompleted = this._allChecksCompleted.asReadonly();

	private _errorMessages = signal<{ id: string; message: string }[]>([]);

	public checkItems: DeviceCheckItem[] = [
		{
			id: "device-camera",
			name: "Device Camera",
			description: "ตรวจสอบความพร้อมของอุปกรณ์ และกล้องถ่ายรูป",
			status: CheckStatus.Pending,
			isRequired: true,
		},
		{
			id: "camera-access",
			name: "Camera Access",
			description: "การอนุญาตการเข้าถึงกล้องถ่ายรูป",
			status: CheckStatus.Pending,
			isRequired: true,
		},
		{
			id: "ai-model",
			name: "AI Model",
			description: "ดาวน์โหลด AI เพื่อเตรียมใช้งานระบบ",
			status: CheckStatus.Pending,
			isRequired: true,
		},
	];

	ngOnInit(): void {
		// Start checks automatically when component loads
		setTimeout(async () => {
			await this.startChecks();
		}, 1000);
	}

	private async startChecks(): Promise<void> {
		this._isChecking.set(true);

		try {
			await this.runDeviceChecks();
			this._allChecksCompleted.set(true);
		} catch (error) {
			console.error("Error during device checks:", error);
			this.handleCheckError(error);
		} finally {
			this._isChecking.set(false);
		}
	}

	async handleButtonClick(): Promise<void> {
		if (this.allChecksCompleted()) {
			this.next();
			return;
		}

		// If checks failed or are still in progress, restart them
		await this.runWithLoading(async () => {
			await this.startChecks();
		});
	}

	private async runDeviceChecks(): Promise<void> {
		await this.executeDeviceAndCameraCheck();
		await this.executePrepareCamera();
		await this.executeAiModelPreparation();
	}

	private async executeCheck(
		index: number,
		checkFn: () => Promise<boolean>,
	): Promise<void> {
		this.checkItems[index].status = CheckStatus.Checking;

		try {
			await checkFn();
			this.checkItems[index].status = CheckStatus.Success;
		} catch (error) {
			this.checkItems[index].status = CheckStatus.Error;
			this._errorMessages.set([
				...this._errorMessages(),
				{
					id: this.checkItems[index].id,
					message: error instanceof Error ? error.message : String(error),
				},
			]);
			throw error;
		}

		await new Promise((resolve) => setTimeout(resolve, 500));
	}

	private async executeDeviceAndCameraCheck(): Promise<void> {
		await this.executeCheck(0, async (): Promise<boolean> => {
			try {
				// Use webcamService for permission check
				const permissionState: string =
					await this.webcamService.checkCameraPermission();
				if (permissionState !== "granted") {
					const permissions =
						await this.webcamService.instance.requestPermissions();
					return permissions.camera === "granted";
				} else if (permissionState === "granted") {
					return true;
				} else if (permissionState === "denied") {
					throw new Error("Camera permission denied");
				}

				return false;
			} catch (error) {
				console.error("Error during device and camera check:", error);
				throw error;
			}
		});
	}

	private async executePrepareCamera(): Promise<void> {
		await this.executeCheck(1, async () => {
			try {
				await new Promise((resolve) => setTimeout(resolve, 1000));

				const isMobileOrTablet =
					this.uaInfoService.isMobile() || this.uaInfoService.isTablet();
				const result = isMobileOrTablet
					? await this.checkMobileDevices()
					: await this.checkDesktopDevices();

				if (!result.isAvailable) {
					console.warn(
						"Camera preparation check failed:",
						result.errors.join(", "),
					);
					throw new Error(result.errors.join(", "));
				}

				return result.isAvailable;
			} catch (error) {
				console.error("Camera preparation check failed:", error);
				throw error;
			}
		});
	}

	private async checkMobileDevices(): Promise<CameraCheckResult> {
		const dmu = new DeviceManagerUtils();
		const videoDevices = await this.webcamService.instance.getVideoDevices();
		const cameras = {
			back: await dmu.selectCamera(videoDevices, "user"),
			front: await dmu.selectCamera(videoDevices, "environment"),
		};

		const errors: string[] = [];
		if (!cameras.back) errors.push("Back camera not available");
		if (!cameras.front) errors.push("Front camera not available");

		return {
			isAvailable: cameras.back !== null && cameras.front !== null,
			errors,
		};
	}

	private async checkDesktopDevices(): Promise<CameraCheckResult> {
		const videoDevices = await this.webcamService.instance.getVideoDevices();
		return {
			isAvailable: videoDevices.length > 0,
			errors: videoDevices.length === 0 ? ["No camera devices found"] : [],
		};
	}

	private async executeAiModelPreparation(): Promise<void> {
		await this.executeCheck(2, async () => {
			try {
				await Promise.all([
					this.ocrDetectionService.initialize(ENGLISH_FAST_MODEL),
					this.mediaPipeService.initialize(),
				]);

				return true;
			} catch (error) {
				console.error("Error preparing AI model:", error);
				throw error;
			}
		});
	}

	private handleCheckError(error: unknown): void {
		const errorMessage = error instanceof Error ? error.message : String(error);
		this.checkItems.forEach((item, index) => {
			if (
				item.status === CheckStatus.Checking ||
				item.status === CheckStatus.Pending
			) {
				this.updateCheckStatus(index, CheckStatus.Error);

				this._errorMessages.set([
					...this._errorMessages(),
					{
						id: item.id,
						message: errorMessage,
					},
				]);
			}
		});
	}

	private updateCheckStatus(index: number, status: CheckStatus): void {
		this.checkItems[index].status = status;
	}

	getStatusClass(status: CheckStatus): string {
		switch (status) {
			case CheckStatus.Success:
				return "bg-green-100 text-green-500";
			case CheckStatus.Error:
				return "bg-red-100 text-red-500";
			case CheckStatus.Checking:
				return "bg-blue-100 text-primary";
			default:
				return "bg-[#D4EDFF] text-[#0192FB]";
		}
	}

	getStatusIcon(status: string): string {
		switch (status) {
			case CheckStatus.Success:
				return "assets/svgs/correct.svg";
			case CheckStatus.Error:
				return "assets/svgs/incorrect.svg";
			default:
				return "assets/svgs/pending.svg";
		}
	}

	getTextClass(status: CheckStatus): string {
		switch (status) {
			case CheckStatus.Success:
				return "text-green-500";
			case CheckStatus.Error:
				return "text-red-500";
			case CheckStatus.Checking:
				return "text-primary";
			default:
				return "text-gray-500";
		}
	}

	getItemIndex(item: DeviceCheckItem): number {
		return this.checkItems.findIndex((i) => i.id === item.id);
	}

	getErrorMessage(item: DeviceCheckItem): string | undefined {
		const errorItem = this._errorMessages().find((e) => e.id === item.id);
		return errorItem?.message;
	}
}
