import { Component, inject } from "@angular/core";
import { CommonModule } from "@angular/common";
import {
	IonButton,
	IonContent,
	IonHeader,
	IonTitle,
	IonToolbar,
} from "@ionic/angular/standalone";
import { WorkflowModalService } from "../../../core/application/workflow/workflow-modal.service";

@Component({
	selector: "app-loan-application-page",
	standalone: true,
	imports: [
		CommonModule,
		IonButton,
		IonContent,
		IonHeader,
		IonTitle,
		IonToolbar,
	],
	template: `
		<ion-header>
			<ion-toolbar>
				<ion-title>สมัครสินเชื่อ</ion-title>
			</ion-toolbar>
		</ion-header>

		<ion-content class="ion-padding">
			<h1>ยินดีต้อนรับสู่การสมัครสินเชื่อออนไลน์</h1>
			<p>กรุณาเลือกเริ่มต้นการสมัครเพื่อดำเนินการต่อ</p>

			<div class="button-container">
				<ion-button (click)="startWorkflow()">เริ่มต้นการสมัคร</ion-button>
				<ion-button (click)="resumeWorkflow()" [disabled]="!hasWorkflowData">
					ดำเนินการต่อ
				</ion-button>
			</div>
		</ion-content>
	`,
	styles: [
		`
			.button-container {
				margin-top: 2rem;
				display: flex;
				gap: 1rem;
			}
		`,
	],
})
export class LoanApplicationPageComponent {
	private workflowService = inject(WorkflowModalService);

	// Check if there's saved workflow data in localStorage
	get hasWorkflowData(): boolean {
		return localStorage.getItem("loan-workflow-state") !== null;
	}

	/**
	 * Start a new workflow
	 */
	startWorkflow(): void {
		this.workflowService.dispatch({
			command: "start",
			targetStepId: "loan-application-workflow",
		});
	}

	/**
	 * Resume a saved workflow
	 */
	resumeWorkflow(): void {
		// Get saved state from localStorage
		const savedState = localStorage.getItem("loan-workflow-state");
		if (!savedState) {
			console.error("No saved workflow state found");
			return;
		}

		try {
			const state = JSON.parse(savedState);
			this.workflowService.dispatch({
				command: "resume",
				payload: { state },
			});
		} catch (error) {
			console.error("Error parsing saved workflow state:", error);
		}
	}
}
