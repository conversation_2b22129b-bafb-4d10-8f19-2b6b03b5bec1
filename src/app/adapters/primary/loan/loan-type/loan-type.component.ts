// Angular imports
import { CommonModule } from "@angular/common";
import { Component, On<PERSON><PERSON>roy, computed, effect, inject, signal } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from "@angular/platform-browser";

// Ionic & PrimeNG imports
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";

// Domain models & DTOs
import { LoanTerm } from "@domain/models/loan.model";

// Application services
import { DashboardService } from "src/app/core/application/dashboard.service";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";

// Shared components
import { HttpErrorResponse } from "@angular/common/http";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

/**
 * Loan Type Component
 *
 * Displays loan terms and conditions for the selected loan type
 * and allows the user to start the loan application workflow.
 */
@Component({
	selector: "app-loan-type",
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		ButtonModule,
		HeaderMiniComponent,
		FooterComponent,
	],
	styleUrl: "./loan-type.component.scss",
	template: `
		<ion-content>
			<div class="app-layout">
				<app-header-mini mode="root" />

				<div class="app-content">
					<div class="content-wrapper">
						<ng-container *ngIf="loanTerm()">
							<div
								class="prose prose-sm text-gray-600"
								[innerHTML]="getSanitizedContent(loanTerm())"></div>
						</ng-container>
					</div>
				</div>

				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper">
						<p class="footer-warning">
							* กรุณาแจ้งรายละเอียดลูกค้าให้ครบถ้วนก่อนเริ่มต้นทำการสมัคร
						</p>
						<p-button
							label="สมัครสินเชื่อให้ลูกค้า"
							[fluid]="true"
							[loading]="isLoading()"
							(click)="openSetLoanTypeConfirmDialog()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class LoanTypeComponent extends WorkflowStepBaseComponent implements OnDestroy {
	// Injected services
	private readonly starmoneyService = inject(StarmoneyService);
	private readonly dashboardService = inject(DashboardService);
	private readonly appUtilsService = inject(AppUtilsService);
	private readonly sanitizer = inject(DomSanitizer);

	// State management
	protected readonly loanTermState = signal<LoanTerm | null>(null);
	protected readonly loanTerm = computed(() => this.loanTermState());

	/**
	 * Mapping of loan IDs to their corresponding term code identifiers
	 * Used to fetch the correct terms and conditions for each loan type
	 */
	private readonly loanTermCodeMap: Record<number, string> = {
		6: "hire_purchase_data", // Hire purchase loan
		2: "personal_loan_data", // Personal loan
	};

	/**
	 * Effect to handle menu changes
	 * Automatically fetches loan terms when the active secondary menu changes
	 */
	private readonly menuChangeEffect = effect(() => {
		const activeMenu = this.dashboardService.activeSecondaryMenu();
		if (activeMenu) {
			this.fetchLoanTerms(activeMenu.id);
		}
	});

	/**
	 * Component cleanup
	 * Destroys the menu change effect to prevent memory leaks
	 */
	ngOnDestroy(): void {
		this.menuChangeEffect.destroy();
	}

	/**
	 * Sanitizes HTML content from the loan terms to safely display it
	 * @param loanTerm The loan term object containing HTML content
	 * @returns Sanitized HTML that can be safely rendered
	 */
	public getSanitizedContent(loanTerm: LoanTerm | null): SafeHtml {
		return loanTerm ? this.sanitizer.bypassSecurityTrustHtml(loanTerm.content) : "";
	}

	/**
	 * Opens a confirmation dialog for the selected loan type
	 * When confirmed, starts the workflow at step 1.2
	 */
	public async openSetLoanTypeConfirmDialog(): Promise<void> {
		try {
			const activeMenu = this.dashboardService.activeSecondaryMenu();
			if (!activeMenu) {
				throw new Error("ไม่ได้เลือกประเภทสินเชื่อ");
			}

			this.appUtilsService.showAlertDialog({
				header: "ยืนยันการเลือกผลิตภัณฑ์",
				message: `ท่านได้เลือกประเภทลูกค้า "<span class="font-bold">${activeMenu.label}</span>" <br /> หลังจากยืนยันจะไม่สามารถกลับมาแก้ไขได้อีก`,
				subMessage: 'หากต้องการดำเนินการต่อ กรุณากดปุ่ม "ยืนยัน"',
				acceptLabel: "ยืนยัน",
				acceptCallback: () => this.runWithLoading(this.startWorkflowWithForceComplete.bind(this)),
				rejectLabel: "ย้อนกลับ",
				rejectCallback: () => console.log("ย้อนกลับแล้ว"),
			});
		} catch (error) {
			await this.handleError(error);
		}
	}

	/**
	 * Fetches loan terms for the specified loan ID
	 * @param loanId The ID of the loan type
	 */
	private async fetchLoanTerms(loanId: number): Promise<void> {
		await this.appUtilsService.withLoading(async () => {
			const txnId = this.getTxnIdOrError();
			const mappingCode = this.loanTermCodeMap[loanId];
			if (!mappingCode) {
				throw new Error("รหัสประเภทสินเชื่อไม่ถูกต้อง");
			}

			const response = await this.starmoneyService.getLoanTerms({
				txn_id: txnId,
				lang: "th",
				code: mappingCode,
			});

			if (response.isSuccess()) {
				this.loanTermState.set(response.data);
			} else {
				throw new Error("ไม่สามารถดึงข้อมูลเงื่อนไขสินเชื่อได้");
			}
		}, "กำลังโหลดข้อกำหนดและเงื่อนไข...");
	}

	// ===== Workflow Methods =====

	/**
	 * Starts the workflow directly at step 1.2, skipping step 1.1
	 * This method:
	 * 1. Saves the loan type selection to the backend
	 * 2. Initializes the workflow at step 1.2 with step 1.1 marked as completed
	 */
	private async startWorkflowWithForceComplete(): Promise<void> {
		try {
			// Get the active menu selection
			const activeMenu = this.dashboardService.activeSecondaryMenu();
			if (!activeMenu) {
				throw new Error("ไม่ได้เลือกประเภทสินเชื่อ");
			}

			// Save the loan type selection to the backend
			const txnId = this.getTxnIdOrError();
			const apiResponse = await this.starmoneyService.setLoanType({
				txn_id: txnId,
				loantype_id: activeMenu.id,
			});
			if (!apiResponse.isSuccess() || !apiResponse.isTrue()) {
				throw new Error("ไม่สามารถบันทึกข้อมูลได้");
			}

			await this.appUtilsService.showSuccess("บันทึกข้อมูลสำเร็จ");

			// Initialize with completed KYC steps
			await this.workflowModalService.initializeWorkflow(
				"loan-application-workflow",
				{},
				["1.1"], // Skip KYC steps
			);

			// Start at loan details step
			await this.workflowModalService.startWorkflow("1.2");
		} catch (error) {
			await this.handleError(error);
		}
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage ? `${errorMessage} : ${apiMessage}` : errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
