:host {
	@apply block h-full;
}

.select-card {
	@apply min-h-[120px] w-full rounded-xl p-4;
	@apply bg-white;
	@apply border-2 border-solid border-gray-200;
	@apply transition-all duration-200;
	@apply hover:bg-gray-50;

	&.active {
		@apply border-2 border-solid border-primary;
		@apply bg-primary/10;
	}

	&.disabled {
		@apply cursor-not-allowed opacity-50;
		@apply hover:bg-white;
	}
}

.select-card-content {
	@apply flex items-start space-x-4;
}

.select-card-icon {
	@apply h-auto w-20 shrink-0 rounded-lg;
	@apply flex items-center justify-center;
}

.select-card-body {
	@apply flex-1 text-left;
	@apply transition-colors duration-200;
}

.select-card-title {
	@apply text-base font-normal text-gray-900;
	@apply transition-colors duration-200;
}

.select-card-description {
	@apply mt-1 text-sm font-light text-gray-600;
	@apply transition-colors duration-200;
}

.grid-layout {
	@apply grid grid-cols-1 gap-4;
}
