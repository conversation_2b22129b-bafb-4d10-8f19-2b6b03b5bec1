import { CommonModule } from "@angular/common";
import { Component, computed, inject, signal } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { addIcons } from "ionicons";
import { globeOutline, personOutline } from "ionicons/icons";
import { ButtonModule } from "primeng/button";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

interface CustomerType {
	id: string;
	title: string;
	description: string;
	icon: string;
	disabled?: boolean;
}

@Component({
	selector: "app-customer-type",
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		FooterComponent,
		ButtonModule,
		HeaderMiniComponent,
	],
	styleUrls: ["./customer-type.component.scss"],
	template: `
		<ion-content>
			<div class="app-layout">
				<!-- Header -->
				<app-header-mini mode="workflow"></app-header-mini>

				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper">
						<p class="page-title">เลือกประเภทลูกค้า</p>
						<div class="grid-layout cols-1">
							<ng-container *ngFor="let type of customerTypes">
								<button
									class="select-card"
									[class.active]="type === selectedType()"
									[class.disabled]="type.disabled"
									[disabled]="type.disabled"
									(click)="selectCustomerType(type)">
									<div class="select-card-content">
										<div class="select-card-icon">
											<img [src]="type.icon" [alt]="type.title" />
										</div>
										<div
											class="select-card-body"
											[class.active]="type === selectedType()">
											<p
												class="select-card-title"
												[class.active]="type === selectedType()">
												{{ type.title }}
											</p>
											<p
												class="select-card-description"
												[class.active]="type === selectedType()">
												{{ type.description }}
											</p>
										</div>
									</div>
								</button>
							</ng-container>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper">
						<p-button
							label="ดำเนินการต่อ"
							[disabled]="!selectedType()"
							(onClick)="confirmSetCustomerType()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class CustomerTypeComponent extends WorkflowStepBaseComponent {
	constructor() {
		super();
		addIcons({ personOutline, globeOutline });
	}

	private readonly selectedTypeState = signal<CustomerType | null>(null);
	protected readonly selectedType = computed(() => this.selectedTypeState());
	private appUtilsService = inject(AppUtilsService);

	customerTypes: CustomerType[] = [
		{
			id: "thai",
			title: "บุคคลธรรมดาสัญชาติไทย",
			description: "โปรดเตรียมบัตรประชาชนไว้พร้อมทำการสมัคร",
			icon: "assets/svgs/id-card.svg",
		},
		{
			id: "foreign",
			title: "บุคคลธรรมดาต่างสัญชาติ",
			description:
				"โปรดเตรียมสำเนาหนังสือเดินทาง/บัตรต่างด้าว/ใบต่างด้าวที่เป็นหลักฐาน",
			icon: "assets/svgs/passport.svg",
		},
		{
			id: "corporate",
			title: "นิติบุคคล บริษัท องค์กร",
			description: "โปรดเตรียมสำเนาหนังสือรับรองเป็นหลักฐาน",
			icon: "assets/svgs/company.svg",
			disabled: true,
		},
	];

	ngOnInit() {
		// Initialize from existing data if available
		if (this.workflowData && this.workflowData["customerType"]) {
			const savedType = this.customerTypes.find(
				(type) => type.id === this.workflowData["customerType"],
			);
			if (savedType) {
				this.selectedTypeState.set(savedType);
			}
		}
	}

	selectCustomerType(type: CustomerType) {
		if (type.disabled) return;
		this.selectedTypeState.set(type);
	}

	/**
	 * Shows confirmation dialog for loan type selection
	 */
	public confirmSetCustomerType(): void {
		this.appUtilsService.showAlertDialog({
			header: "โปรดยืนยันการเลือก",
			message: `ท่านได้เลือกลงทะเบียนลูกค้าแบบ <br /> "<span class="font-bold">${
				this.selectedType()?.title
			}</span>" <br /> หลังจากยืนยันจะไม่สามารถกลับมาแก้ไขได้อีก`,
			subMessage: 'หากต้องการดำเนินการต่อ กรุณากดปุ่ม "ยืนยัน"',
			acceptLabel: "ยืนยัน",
			rejectLabel: "ย้อนกลับ",
			acceptCallback: () => this.runWithLoading(this.onSetLoanType.bind(this)),
			rejectCallback: () => console.log("ย้อนกลับแล้ว"),
		});
	}

	/**
	 * Handles customer type selection confirmation
	 */
	private async onSetLoanType(): Promise<void> {
		try {
			if (!this.selectedType()) {
				await this.appUtilsService.showError(
					"กรุณาเลือกประเภทลูกค้า",
					"กรุณาเลือกประเภทลูกค้า",
				);
				return;
			}

			// Show success message
			await this.appUtilsService.showSuccess("บันทึกข้อมูลสำเร็จ");

			// Navigate to next step
			this.handleNext();
		} catch (error) {
			// Handle errors with proper error message
			const errorMessage =
				error instanceof Error ? error.message : "ไม่สามารถบันทึกข้อมูลได้";
			await this.appUtilsService.showError("เกิดข้อผิดพลาด", errorMessage);

			// Report error to workflow service
			this.reportError({ message: errorMessage });
		}
	}

	/**
	 * Go to next step
	 */
	private handleNext(): void {
		if (!this.selectedType()) {
			return;
		}

		// Save to localStorage for demo purposes
		this.saveWorkflowState();

		// Use the inherited next method with the selected customer type
		this.next({ customerType: this.selectedType()?.id });
	}
}
