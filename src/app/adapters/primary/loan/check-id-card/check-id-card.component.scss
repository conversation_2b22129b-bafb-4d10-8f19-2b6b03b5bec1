/* Layout */
.app-content {
	@apply flex h-dvh flex-col justify-between p-4;
}

.content-container {
	@apply flex h-full w-full flex-col items-center;
}

.main-content {
	@apply flex w-full flex-1 flex-col items-center justify-center;
}

/* Card Reader */
.card-reader-image {
	@apply mb-8 h-48 w-48;
}

.card-reader-image img {
	@apply h-full w-full object-contain;
}

/* Status Messages */
.status-message {
	@apply space-y-4 text-center;
}

.primary-message {
	@apply text-lg font-medium text-gray-600;
}

.secondary-message {
	@apply text-sm text-gray-500;
}

/* Loading */
.loading-container {
	@apply flex flex-col items-center justify-center space-y-8;
}

.loader {
	@apply h-56 w-56 animate-spin rounded-full border-[18px] border-blue-200 border-t-primary;
}

/* Action Buttons */
.action-buttons {
	@apply mt-8 flex justify-center gap-4;
}

.action-buttons p-button {
	@apply flex-1;
}