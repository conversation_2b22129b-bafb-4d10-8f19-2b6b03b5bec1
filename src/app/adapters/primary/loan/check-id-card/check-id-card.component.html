<ion-content>
	<div class="app-layout-dialog">
		<!-- Overlay Modals -->
		<app-circular-progress-loading-modal
			*ngIf="isLoadingProcessing() && !isShowStatusNotification()"
			class="overlay-modal"
			title="ตรวจสอบข้อมูลบัตรประชาชน"
			statusMessage="กำลังตรวจสอบสถานะ"
			[description]="'ระบบกำลังทำการประมวลผลกรุณารอ\nจนกว่าระบบจะดำเนินการเสร็จสิ้น'"
			[indeterminateProgress]="true"
			[autoProgress]="false" />
		<app-status-notification
			*ngIf="isShowStatusNotification() && !isLoadingProcessing()"
			class="overlay-modal"
			[config]="statusNotificationOptions()!" />

		<!-- Content -->
		<div class="app-content">
			<!-- Header Section -->
			<div class="content-container">
				<!-- Title -->
				<h1 class="page-title">ตรวจสอบข้อมูลประชาชน</h1>

				<!-- Main Content -->
				<div class="main-content">
					<ng-container *ngIf="!isLoading()">
						<!-- Card Reader Illustration -->
						<div class="card-reader-image">
							<img src="assets/images/read-id-card.png" alt="ID Card Reader" />
						</div>

						<!-- Status Message -->
						<div class="status-message">
							<p class="primary-message">อยู่ระหว่างการตรวจสอบข้อมูล</p>
							<p class="secondary-message">
								หากท่านอ่านบัตรประชาชนแล้ว
								<br />
								กรุณากดปุ่ม "ตรวจสอบสถานะ" เพื่อดำเนินการต่อ
							</p>
							<p class="secondary-message">
								หากระบบไม่ตอบสนอง กรุณากดปุ่ม "ย้อนกลับ"
								<br />
								แล้วกดปุ่ม "อ่านบัตรประชาชน" อีกครั้ง
							</p>
						</div>
					</ng-container>

					<ng-container *ngIf="isLoading()">
						<div class="loading-container">
							<div class="loader"></div>
							<div class="status-message">
								<p class="primary-message">ตรวจสอบสถานะ</p>
								<p class="secondary-message">
									อยู่ระหว่างการตรวจสอบสถานะ
									<br />
									กรุณารอจนกว่าระบบจะทำงานเสร็จสิ้น
								</p>
							</div>
						</div>
					</ng-container>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="action-buttons">
				<p-button
					label="ย้อนกลับ"
					styleClass="w-full"
					[outlined]="true"
					[disabled]="isLoading()"
					(onClick)="modalDismiss('cancelled')" />
				<p-button label="ตรวจสอบสถานะ" styleClass="w-full" (onClick)="onCheckStatus()" />
			</div>
		</div>
	</div>
</ion-content>