import { CommonModule } from "@angular/common";
import { ChangeDetectionStrategy, Component, computed, inject, signal } from "@angular/core";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { ModalService } from "src/app/core/application/workflow/modal.service";
import { CircularProgressLoadingModalComponent } from "../../shared/components/circular-progress-loading-modal/circular-progress-loading-modal.component";
import {
	StatusNotificationComponent,
	StatusNotificationOptions,
} from "../../shared/components/status-notification/status-notification.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

export enum CheckResultType {
	Success = "success",
	Warning = "warning",
	Failure = "failure",
}

@Component({
	selector: "app-check-id-card",
	standalone: true,
	imports: [
		CommonModule,
		IonicModule,
		ButtonModule,
		CircularProgressLoadingModalComponent,
		StatusNotificationComponent,
	],
	styleUrls: ["./check-id-card.component.scss"],
	templateUrl: "./check-id-card.component.html",
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CheckIdCardComponent extends WorkflowStepBaseComponent {
	private readonly appUtilsService = inject(AppUtilsService);
	private readonly modalService = inject(ModalService);

	// Reactive state with signals
	private readonly _isShowStatusNotification = signal<boolean>(false);
	private readonly _statusNotificationOptions = signal<StatusNotificationOptions | null>(null);
	private readonly _isLoadingProcessing = signal<boolean>(false);

	// Computed properties for template binding
	public readonly isShowStatusNotification = computed(() => this._isShowStatusNotification());
	public readonly statusNotificationOptions = computed(() => this._statusNotificationOptions());
	public readonly isLoadingProcessing = computed(() => this._isLoadingProcessing());

	async onCheckStatus(): Promise<void> {
		try {
			await this.showGlobalCheckingDialog();
		} catch (error) {
			this.appUtilsService.showError("ข้อผิดพลาด", "ไม่สามารถตรวจสอบข้อมูลได้");
		}
	}

	private async showGlobalCheckingDialog(): Promise<void> {
		this._isLoadingProcessing.set(true);

		try {
			const apiResult = await this.mockApiCall();
			this._isLoadingProcessing.set(false);
			this.handleProcessingResult(apiResult);
		} catch (error) {
			this._isLoadingProcessing.set(false);
			this.appUtilsService.showError("ข้อผิดพลาด", "ไม่สามารถตรวจสอบข้อมูลได้");
		}
	}

	private handleProcessingResult(result: CheckResultType): void {
		const options = this.getOptions(result);
		this._statusNotificationOptions.set(options);
		this._isShowStatusNotification.set(true);
	}

	private getOptions(result: CheckResultType): StatusNotificationOptions {
		switch (result) {
			case CheckResultType.Success:
				return {
					statusType: "success",
					title: "อ่านข้อมูลสำเร็จ",
					message: "ระบบได้รับข้อมูลจากการอ่านแล้ว",
					description: "สามารถดำเนินการสมัครสินเชื่อต่อได้",
					primaryButtonLabel: "สมัครสินเชื่อต่อ",
					onPrimaryAction: async () => {
						this._isShowStatusNotification.set(false);
						await this.modalDismiss("completed");
					},
				};
			case CheckResultType.Warning:
				return {
					statusType: "warning",
					title: "ไม่ได้รับข้อมูลจากการอ่าน",
					message: "กรุณาตรวจสอบและลองใหม่อีกครั้ง",
					description: "หากทำการอ่านสำเร็จแล้ว กรุณากด 'ตรวจสอบข้อมูล' อีกครั้ง",
					primaryButtonLabel: "ตรวจสอบอีกครั้ง",
					secondaryButtonLabel: "ย้อนกลับ",
					onPrimaryAction: async () => {
						this._isShowStatusNotification.set(false);
						await this.onCheckStatus();
					},
					onSecondaryAction: async () => {
						this._isShowStatusNotification.set(false);
					},
				};
			case CheckResultType.Failure:
			default:
				return {
					statusType: "error",
					message: "ไม่สามารถตรวจสอบได้",
					description: "ไม่พบข้อมูลในระบบ",
					errorCode: "E12345",
					primaryButtonLabel: "ตกลง",
					onPrimaryAction: async () => {
						this._isShowStatusNotification.set(false);
						this.modalDismiss("cancelled");
					},
				};
		}
	}

	private mockApiCall(): Promise<CheckResultType> {
		return new Promise((resolve) => {
			const responseTime = Math.random() * 1000 + 1000;

			setTimeout(() => {
				const random = Math.random();
				let result: CheckResultType;

				if (random < 0.5) {
					result = CheckResultType.Success;
				} else if (random < 0.8) {
					result = CheckResultType.Warning;
				} else {
					result = CheckResultType.Failure;
				}

				resolve(result);
			}, responseTime);
		});
	}

	public async modalDismiss(status: "completed" | "cancelled" = "cancelled"): Promise<void> {
		await this.modalService.closeGeneralModal({}, status);
	}
}
