import { CommonModule } from "@angular/common";
import { Component } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { addIcons } from "ionicons";
import {
	flashlightOutline,
	personCircleOutline,
	sunnyOutline,
} from "ionicons/icons";
import { ButtonModule } from "primeng/button";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

export interface GuidelineItem {
	level: number;
	description: string;
	image?: string;
}

@Component({
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		FooterComponent,
		ButtonModule,
	],
	selector: "app-take-face-photo-guide",
	styleUrl: "./take-face-photo-guideline.component.scss",
	template: `
		<ion-content>
			<div class="app-layout-dialog">
				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper">
						<!-- Header Section -->
						<p class="page-title">ถ่ายรูปใบหน้า</p>

						<!-- Webcam Image -->
						<div class="my-8 flex flex-col items-center justify-center">
							<img
								class="h-auto max-w-[300px]"
								src="assets/svgs/face-guideline.svg" />
						</div>

						<p class="page-title-description">คำแนะนำการถ่ายรูปใบหน้า</p>

						<!-- Steps Section -->
						<div
							*ngFor="let item of guideline"
							class="bg-white transition-all duration-300">
							<div class="flex flex-row py-2">
								<div
									class="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-[#D4EDFF] text-xs text-[#0192FB]">
									{{ item.level }}
								</div>
								<!-- Content -->
								<div class="ml-4 flex-1">
									<p class="mt-1 text-sm text-gray-500">
										{{ item.description }}
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer styleClass="!border-none">
					<div class="footer-wrapper !justify-center">
						<p-button
							label="เริ่มต้นถ่ายรูป"
							styleClass="w-full"
							class="w-full max-w-md"
							[loading]="isLoading()"
							(onClick)="next()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class TakeFacePhotoGuideComponent extends WorkflowStepBaseComponent {
	constructor() {
		super();
		addIcons({ personCircleOutline, sunnyOutline, flashlightOutline });
	}

	public guideline: GuidelineItem[] = [
		{
			level: 1,
			description: "ไม่สวมสิ่งปิดบังใบหน้า เช่น หมวก หรือ แว่นตา",
		},
		{
			level: 2,
			description: "ไม่ถ่ายรูปย้อนแสง และอยู่ในที่ที่มีแสงสว่างเพียงพอ",
		},
		{
			level: 3,
			description: "ไม่ถ่ายรูปโดยมีบุคคลอื่นอยู่ด้านหลัง",
		},
		{
			level: 4,
			description:
				"จัดตำแหน่งใบหน้าให้อยู่ในกรอบที่กำหนด และทำตามข้อความทปรากฎ",
		},
	];
}
