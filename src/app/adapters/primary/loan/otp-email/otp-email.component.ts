import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject, OnInit } from "@angular/core";
import {
	FormBuilder,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { ModalController } from "@ionic/angular/standalone";
import { ButtonModule } from "primeng/button";
import { InputMaskModule } from "primeng/inputmask";
import { InputTextModule } from "primeng/inputtext";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";
import { OtpEmailVerifyComponent } from "../../shared/components/otp-email-verify/otp-email-verify.component";

@Component({
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		FooterComponent,
		InputMaskModule,
		InputTextModule,
		ButtonModule,
		HeaderMiniComponent,
	],
	selector: "app-otp-email",
	styleUrls: ["./otp-email.component.scss"],
	template: `
		<div class="app-layout">
			<!-- Header -->
			<app-header-mini mode="workflow" />

			<!-- Content -->
			<div class="app-content">
				<div class="content-wrapper">
					<!-- Header Section -->
					<div class="text-center">
						<p class="page-title">ลงทะเบียนอีเมลแอดเดรส</p>
						<p class="page-description-center">
							<span>ลูกค้าจะได้รับชุดสัญญาและใบเสร็จรับเงิน/ใบกำกับภาษี</span>
							<span>ในรูปแบบอิเล็กทรอนิกส์ ผ่านอีเมลที่ท่านลงทะเบียน OTP</span>
						</p>
					</div>

					<form *ngIf="form" [formGroup]="form" class="flex flex-col gap-4">
						<div class="my-3 flex justify-center">
							<input
								pInputText
								formControlName="email"
								type="email"
								pSize="large"
								placeholder="โปรดกรอกอีเมลแอดเดรสของท่าน"
								class="h-12 max-w-[430px] text-center !text-sm"
								[ngClass]="{
									'ng-invalid ng-dirty': form.get('email')?.invalid,
								}"
								[fluid]="true" />
						</div>
					</form>
				</div>
			</div>

			<!-- Footer -->
			<app-footer>
				<div class="footer-wrapper !justify-between">
					<p-button
						label="ข้ามขั้นตอน"
						fluid="true"
						[outlined]="true"
						[loading]="isLoading()"
						(onClick)="next()" />
					<p-button
						label="ต่อไป"
						styleClass="w-full"
						[disabled]="form.invalid"
						[loading]="isLoading()"
						(onClick)="handleNextButtonClick()" />
				</div>
			</app-footer>
		</div>
	`,
})
export class OtpEmailComponent
	extends WorkflowStepBaseComponent
	implements OnInit
{
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);
	private fb = inject(FormBuilder);
	public form = this.fb.group({
		email: ["", [Validators.required, Validators.email]],
	});

	get email() {
		return this.form.get("email")?.value || "";
	}

	ngOnInit(): void {}

	/**
	 * Updates the phone number and opens the OTP verification modal if successful
	 */
	async handleNextButtonClick() {
		await this.runWithLoading(async () => {
			try {
				const email = this.email;
				if (!email) {
					throw new Error("อีเมลไม่ถูกต้อง");
				}

				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.updateContact({
					txn_id: txnId,
					email: email,
				});

				if (apiResponse.isSuccess() && apiResponse.isTrue()) {
					await this.openOtpVerificationModal();
				} else {
					throw new Error(
						apiResponse.message || "ไม่สามารถอัพเดทเบอร์โทรศัพท์ได้",
					);
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	/**
	 * Opens the OTP verification modal
	 */
	private async openOtpVerificationModal() {
		const modal = await this.modalController.create({
			component: OtpEmailVerifyComponent,
			cssClass: "global-dialog-modal modal-blur-backdrop",
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		if (data && data.success) {
			this.next();
		}
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
