import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject, OnInit } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { LoanType } from "@domain/models/loan.model";
import { DownRangePrice, Period, Product } from "@domain/models/product.model";
import { IonicModule, ModalController } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { InputTextModule } from "primeng/inputtext";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { InstallmentPlanInfoComponent } from "../../shared/components/installment-plan-info/installment-plan-info.component";
import { InstallmentSummaryComponent } from "../../shared/components/installment-summary/installment-summary.component";
import { ProductDetailsComponent } from "../../shared/components/product-details/product-details.component";
import { SelectInstallmentProductComponent } from "../../shared/components/select-installment-product/select-installment-product.component";
import { StepCounterComponent } from "../../shared/components/step-counter/step-counter.component";
import { StepCounterService } from "../../shared/services/step-counter.service";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-select-installment",
	templateUrl: "./select-installment.component.html",
	styleUrls: ["./select-installment.component.scss"],
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		InputTextModule,
		ButtonModule,
		HeaderMiniComponent,
		FooterComponent,
		IonicModule,
		StepCounterComponent,
	],
})
export class SelectInstallmentComponent extends WorkflowStepBaseComponent implements OnInit {
	public stepCounterService = inject(StepCounterService);
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);

	activeStepId: string = "2.3";
	loanInfo: LoanType | null = null;
	productInfo: Product | null = null;
	downPayment: DownRangePrice | null = null;
	installmentPlan: Period | null = null;

	async ngOnInit() {
		await this.getLoanType();
		await this.getLoanInfo();
	}

	async getLoanInfo() {
		if (this.loanInfo?.isHirePurchase()) {
			await this.getProduct();
		} else if (this.loanInfo?.isPersonalLoan()) {
			// await this.getPersonalLoan();
		} else {
			await this.appUtilsService.showError("เกิดข้อผิดพลาด", "ไม่พบข้อมูล");
		}
	}

	async getLoanType() {
		await this.runWithLoading(async () => {
			try {
				const txnId = this.getTxnIdOrError();
				const response = await this.starmoneyService.getLoanType({
					txn_id: txnId,
				});
				if (response.isSuccess()) {
					console.log("Response", response.data);
					this.loanInfo = response.data;
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	async getProduct(code?: string) {
		await this.runWithLoading(async () => {
			try {
				const txnId = this.getTxnIdOrError();
				const response = await this.starmoneyService.getProduct({
					txn_id: txnId,
					...(code ? { promotion_code: code } : {}),
				});
				if (response.isSuccess() && response.hasData()) {
					// set response
					this.productInfo = response.data;
					if (this.productInfo?.down && this.productInfo?.period) {
						this.initializeInstallmentData();
					}
					// if (!this.isValidPromotionCode() && code && code.length > 0) {
					//   // todo: display toast invalid promotion code

					//   // clear promotion code and input
					//   this.product.promotion_code = '';
					//   this.promotion_code = '';

					//   // show toast invalid promotion code
					//   await this.appUtilsService.showToast({
					//     message: 'รหัสโปรโมชั่นไม่ถูกต้อง',
					//   });
					// }
				} else {
					this.appUtilsService.showInfo("ไม่พบข้อมูลสินค้า");
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	private initializeInstallmentData(): void {
		if (!this.productInfo?.downRangePrice) {
			console.error("Product or down_range_price is missing");
			return;
		}

		this.setProductDefaultValues();
	}

	private setProductDefaultValues(): void {
		if (this.productInfo?.downRangePrice.length === 0) {
			console.error("No down payment options available");
			return;
		}

		const down = this.productInfo?.down;
		const period = this.productInfo?.period;
		if (!down) {
			this.appUtilsService.showError("เกิดข้อผิดพลาด", "ไม่พบข้อมูลดาวน์");
			return;
		}

		const selectedDown =
			this.productInfo?.downRangePrice.find((item: DownRangePrice) => item.percent === down) ||
			null;

		this.downPayment = selectedDown;
		const periods = this.getPeriodByPercentDown(down);
		console.log("Periods", periods);
		if (!periods) {
			this.appUtilsService.showError("เกิดข้อผิดพลาด", "ไม่พบข้อมูลงวด");
			return;
		}

		const selectedPeriod = periods.find((p) => p.period === period);
		if (!selectedPeriod) {
			this.appUtilsService.showError("เกิดข้อผิดพลาด", "ไม่พบข้อมูลงวด");
			return;
		}

		console.log("Selected Period", selectedPeriod);
		this.installmentPlan = selectedPeriod;
	}

	private getPeriodByPercentDown(percent: number): Period[] | null {
		if (!this.productInfo?.periodRangePrice) {
			return null;
		}

		const result = this.productInfo.periodRangePrice.find((item: any) => item.percent === percent);
		return result?.period || null;
	}

	async selectInstallmentProduct() {
		const modal = await this.modalController.create({
			component: SelectInstallmentProductComponent,
			cssClass: "fullscreen-modal",
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		if (data && data?.refresh) {
			this.appUtilsService.showInfo("กำลังรีเฟรชข้อมูลสินค้า");
			await this.getLoanInfo();
		}
	}

	async openProductDetails() {
		const modal = await this.modalController.create({
			component: ProductDetailsComponent,
			cssClass: "global-dialog-modal modal-blur-backdrop",
			componentProps: {
				product: this.productInfo,
				canChoose: false,
			},
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		if (data && data.isSelected) {
			await this.appUtilsService.showSuccess(
				"สินค้าเลือกที่สำเร็จ คือ " + data.product?.getProductModelName(),
			);
		} else {
			console.warn("ไม่ได้เลือกสินค้า");
		}
	}

	async selectInstallmentPlan() {
		const modal = await this.modalController.create({
			component: InstallmentPlanInfoComponent,
			cssClass: "installment-plan-modal modal-blur-backdrop",
			componentProps: {
				loanInfo: this.loanInfo,
				productInfo: this.productInfo,
			},
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		if (data) {
			this.openInstallmentSummary(data);
		} else {
			console.warn("ไม่ได้เลือกสินค้า");
		}
	}

	async openInstallmentSummary(payload: { downPayment?: any; installmentPlan?: any }) {
		const modal = await this.modalController.create({
			component: InstallmentSummaryComponent,
			cssClass: "installment-plan-modal modal-blur-backdrop",
			componentProps: {
				loanInfo: this.loanInfo,
				productInfo: this.productInfo,
				downPayment: payload.downPayment,
				installmentPlan: payload.installmentPlan,
			},
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		if (data) {
			this.appUtilsService.showInfo("กำลังรีเฟรชข้อมูลสินค้า");
			this.downPayment = data.downPayment;
			this.installmentPlan = data.installmentPlan;
			if (data.refresh) {
				this.appUtilsService.showInfo("กำลังรีเฟรชข้อมูลสินค้า");
				await this.getLoanInfo();
			}
		}
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage ? `${errorMessage} : ${apiMessage}` : errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}

	handleDeleteProduct() {
		this.productInfo = null;
	}

	handleNext() {
		// Mark current step as completed in the step counter service
		this.stepCounterService.markStepCompleted(this.activeStepId);

		// Dispatch next event with employment data
		super.next();
	}
}
