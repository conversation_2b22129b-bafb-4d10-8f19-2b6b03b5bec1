/* Installment Plan Component Styles */
.installment-section {
    @apply flex w-full flex-row gap-8;
}

.installment-section-half {
    @apply flex w-1/2 flex-col gap-2;
}

.section-title {
    @apply mb-3 text-lg font-medium;
}

.section-subtitle {
    @apply text-lg font-medium;
}

.detail-row {
    @apply flex justify-between gap-4 py-1.5 last:border-b-0;
}

.detail-label {
    @apply w-1/3 text-left text-sm text-gray-500;
}

.detail-value {
    @apply flex-1 text-right text-sm text-gray-500;
}

.detail-label-bold {
    @apply w-1/3 text-left text-sm font-bold text-gray-900;
}

.detail-value-bold {
    @apply flex-1 text-right text-sm font-bold text-gray-900;
}

.disclaimer-text {
    @apply mt-3 flex flex-col rounded text-right text-xs text-gray-500;
}

.content-max {
    @apply max-w-full;
}

.section-header {
    @apply bg-primary/10 p-3;
}

.section-title-text {
    @apply text-primary_dark text-lg font-medium;
}

.page-container {
    @apply flex h-full flex-col justify-start gap-4 rounded-b-md bg-white;
}

.product-image-container {
    @apply mx-auto mb-4 max-h-[200px] w-auto rounded-md p-2;

    img {
        @apply max-h-[200px] w-full;
    }
}

.small-image-container {
    @apply mx-auto my-4 max-h-[200px] w-auto rounded-md p-2;
}

.product-info-row {
    @apply mb-2 flex flex-row items-center justify-between gap-6 rounded-md;
}

.product-text {
    @apply flex flex-col justify-between space-y-2 !text-left;
}

.product-name {
    @apply text-sm font-normal text-gray-900;
}

.product-price {
    @apply text-base font-medium;
}

.action-row {
    @apply mt-4 flex flex-row !justify-between gap-4;
}

.page-title-description {
    @apply pb-2 pt-2 text-lg font-medium;
}

.page-title-description-center {
    @apply text-pretty pt-4 text-center text-lg font-medium;
}

.page-description-center {
    @apply mb-6 flex flex-col px-4 text-center text-sm font-light text-gray-500;
}

.details-section {
    @apply rounded-md bg-white pt-4;
}

.payment-terms-section {
    @apply rounded-md bg-white;
}

.panel-container {
    @apply flex flex-1 flex-col overflow-hidden rounded-md;
}