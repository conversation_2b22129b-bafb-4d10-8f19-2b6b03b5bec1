<ion-content>
	<div class="app-layout">
		<app-header-mini mode="workflow" />

		<!-- Content -->
		<div class="app-content">
			<div class="flex h-full flex-row">
				<!-- Step Counter -->
				<div class="flex h-full">
					<app-step-counter [activeStep]="activeStepId"></app-step-counter>
				</div>
				<div class="content-wrapper !max-w-full">
					<!-- Content Spliter -->
					<ng-container *ngIf="this.loanInfo?.isHirePurchase()">
						<div class="flex gap-6">
							<ng-container *ngIf="productInfo; else noProduct">
								<div class="panel-container">
									<!-- Consent Title -->
									<div class="section-header">
										<h2 class="section-title-text">
											เลือกสินค้าและการผ่อนชำระ
										</h2>
									</div>

									<div class="page-container">
										<p class="page-title-description">สินค้าที่ท่านเลือก</p>
										<div class="product-image-container">
											<img
												[src]="productInfo.image"
												alt="person-with-product" />
										</div>

										<div class="product-info-row">
											<p class="product-text">
												<span class="product-name">
													{{ productInfo.modelName || "-" }}
												</span>
												<span class="product-price">
													{{
														productInfo.netPrice
															| currency: "ราคา " : "symbol" : "1.0-0"
													}}
													บาท
												</span>
											</p>
											<p-button
												label="ดูรายละเอียด"
												severity="primary"
												class="!w-[200px]"
												[outlined]="true"
												[fluid]="true"
												(click)="openProductDetails()" />
										</div>

										<div class="action-row">
											<p-button
												label="ลบสินค้า"
												severity="danger"
												class="w-full"
												[outlined]="true"
												[fluid]="true"
												[loading]="isLoading()"
												(onClick)="handleDeleteProduct()" />
											<p-button
												label="เปลี่ยนสินค้า"
												class="w-full"
												[outlined]="true"
												[fluid]="true"
												[loading]="isLoading()"
												(onClick)="selectInstallmentProduct()" />
										</div>
									</div>
								</div>
								<div class="panel-container">
									<!-- Consent Title -->
									<div class="section-header">
										<h2 class="section-title-text">
											เลือกสินค้าและการผ่อนชำระ
										</h2>
									</div>

									<ng-container
										*ngIf="
											!downPayment && !installmentPlan;
											else installmentPlanSelected
										">
										<div class="page-container">
											<p class="page-title-description-center">
												การผ่อนชำระสินค้า
											</p>

											<div class="small-image-container">
												<img
													src="assets/svgs/person-with-product.svg"
													alt="person-with-product"
													class="h-auto w-full" />
											</div>

											<p class="page-description-center">
												<span>เลือกนโยบายการขายที่บริษัทฯ กำหนด</span>
												<span>และสอบถามการผ่อนชำระที่ลูกค้าต้องการ</span>
											</p>

											<p-button
												label="เลือกการผ่อนชำระ"
												[fluid]="true"
												(click)="selectInstallmentPlan()" />
										</div>
									</ng-container>

									<ng-template #installmentPlanSelected>
										<div class="page-container">
											<div class="details-section">
												<h2 class="section-title">รายละเอียดสินค้า</h2>

												<p class="detail-row">
													<span class="detail-label">สินค้าที่เลือก</span>
													<span class="detail-value">
														{{ productInfo.modelName || "" }}
													</span>
												</p>

												<p class="detail-row">
													<span class="detail-label">ราคาสินค้า</span>
													<span class="detail-value">
														{{ productInfo.price || 0 | number }} บาท
													</span>
												</p>

												<p class="detail-row">
													<span class="detail-label">ส่วนลด:</span>
													<span class="detail-value">
														{{ productInfo.discount() || 0 | number }} บาท
													</span>
												</p>

												<p class="detail-row">
													<span class="detail-label-bold">ราคาสุทธิ:</span>
													<span class="detail-value-bold">
														{{ productInfo.normalPrice || 0 | number }} บาท
													</span>
												</p>
											</div>

											<div class="payment-terms-section">
												<h2 class="section-title">เงื่อนไขการผ่อนชำระ</h2>

												<p class="detail-row">
													<span class="detail-label">นโยบายการขาย</span>
													<span class="detail-value">
														ขายสินค้าหมวด U ทุกชนิดทุกช่องทางขาย
													</span>
												</p>

												<p class="detail-row">
													<span class="detail-label-bold">ดาวน์:</span>
													<span class="detail-value-bold">
														({{ downPayment?.percent || "-" }}%)
														{{ downPayment?.price || 0 | number }} บาท
													</span>
												</p>

												<p class="detail-row">
													<span class="detail-label-bold">ดอกเบี้ย:</span>
													<span class="detail-value-bold">
														{{ productInfo.interestRate || 0 | number }}%
														ต่อเดือน
													</span>
												</p>

												<p class="detail-row">
													<span class="detail-label-bold">ผ่อนต่องวด:</span>
													<span class="detail-value-bold">
														{{ installmentPlan?.price || 0 | number }} บาท
													</span>
												</p>

												<p class="detail-row">
													<span class="detail-label-bold">จำนวนงวด:</span>
													<span class="detail-value-bold">
														{{ installmentPlan?.period || 0 | number }} งวด
													</span>
												</p>
											</div>

											<p class="disclaimer-text">
												<span>
													* กรุณาแจ้งให้ลูกค้าทราบ
													ข้อมูลดังกล่าวเป็นการประเมินเบื้องต้นจากระบบ
												</span>
												<span>อาจมีการเปลี่ยนแปลงตามผลการพิจารณาสินเชื่อ</span>
											</p>

											<p-button
												label="แก้ไขรายละเอียดการผ่อนชำระ"
												styleClass="w-full"
												[outlined]="true"
												(click)="selectInstallmentPlan()" />
										</div>
									</ng-template>
								</div>
							</ng-container>

							<ng-template #noProduct>
								<div class="panel-container w-1/2 !flex-none">
									<!-- Consent Title -->
									<div class="section-header">
										<h2 class="section-title-text">
											เลือกสินค้าและการผ่อนชำระ
										</h2>
									</div>

									<div class="page-container items-center">
										<p class="page-title-description-center !mt-12">
											สินค้าที่ท่านเลือก
										</p>
										<div class="small-image-container">
											<img
												src="assets/svgs/person-with-product.svg"
												alt="person-with-product"
												class="h-auto w-full" />
										</div>
										<p class="page-description-center">
											<span>ท่านยังไม่ได้เลือกสินค้า</span>
											<span>กรุณากดปุ่ม "เพิ่มสินค้า" เพื่อดำเนินการต่อ</span>
										</p>
										<p-button
											label="เพิ่มสินค้า"
											styleClass="w-64"
											(click)="selectInstallmentProduct()" />
									</div>
								</div>
							</ng-template>
						</div>
					</ng-container>
				</div>
			</div>
		</div>

		<!-- Footer -->
		<app-footer>
			<div class="footer-wrapper !justify-between">
				<p-button
					label="ย้อนกลับ"
					[outlined]="true"
					[fluid]="true"
					(onClick)="back()" />
				<p-button
					label="ต่อไป"
					[fluid]="true"
					[loading]="isLoading()"
					(onClick)="handleNext()" />
			</div>
		</app-footer>
	</div>
</ion-content>
