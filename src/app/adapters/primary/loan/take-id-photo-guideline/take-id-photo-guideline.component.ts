import { CommonModule } from "@angular/common";
import { Component } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { addIcons } from "ionicons";
import {
	flashlightOutline,
	personCircleOutline,
	sunnyOutline,
} from "ionicons/icons";
import { ButtonModule } from "primeng/button";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

export interface GuidelineItem {
	level: number;
	description: string;
	image?: string;
}

@Component({
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		FooterComponent,
		ButtonModule,
	],
	selector: "app-photo-guide-modal",
	styleUrl: "./take-id-photo-guideline.component.scss",
	template: `
		<ion-content>
			<div class="app-layout-dialog">
				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper">
						<!-- Header Section -->
						<div class="text-center">
							<p class="page-title">ถ่ายรูปเอกสารยืนยันตัวตน</p>
							<p class="page-title-description">
								คำแนะนำในการถ่ายรูปเอกสารยืนยันตัวตน
							</p>
						</div>

						<!-- Steps Section -->
						<div class="guideline-list">
							<!-- Step 1 -->
							@for (item of guideline; track item.level) {
								<div class="guideline-item">
									<div class="guideline-item-image">
										<img [src]="item.image" alt="" />
									</div>
									<div class="guideline-item-content">
										<h3 class="guideline-item-title">
											{{ item.description }}
										</h3>
									</div>
								</div>
							}
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer styleClass="!border-none">
					<div class="footer-wrapper !justify-center">
						<p-button
							label="เริ่มต้นถ่ายรูป"
							styleClass="w-full"
							class="w-full max-w-md"
							[loading]="isLoading()"
							(onClick)="next()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class PhotoGuideModalComponent extends WorkflowStepBaseComponent {
	constructor() {
		super();
		addIcons({ personCircleOutline, sunnyOutline, flashlightOutline });
	}

	public guideline: GuidelineItem[] = [
		{
			level: 1,
			description: "ถ่ายรูปเอกสารโดยให้เห็นรายละเอียดบนหน้าเอกสารชัดเจน",
			image: "assets/svgs/id-guideline/id-guideline-1.svg",
		},
		{
			level: 2,
			description: "ไม่ปิดบังข้อมูลบนหน้าเอกสาร",
			image: "assets/svgs/id-guideline/id-guideline-2.svg",
		},
		{
			level: 3,
			description: "ระวังไม่ให้มีแสงสะท้อนบนหน้าเอกสาร",
			image: "assets/svgs/id-guideline/id-guideline-3.svg",
		},
	];
}
