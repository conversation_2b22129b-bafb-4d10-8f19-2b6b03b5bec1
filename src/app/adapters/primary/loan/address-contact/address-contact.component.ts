import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, OnInit, inject } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { AddressInfo } from "@domain/models/address.model";
import { IonicModule, ModalController } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { AddressInfoFormComponent } from "../../shared/components/forms/address-info-form/address-info-form.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { StepCounterComponent } from "../../shared/components/step-counter/step-counter.component";
import { StepCounterService } from "../../shared/services/step-counter.service";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

export interface AddressItem {
	title: string;
	type: string;
	value: string;
	require: boolean;
}

@Component({
	selector: "app-personal-info-modal",
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		ButtonModule,
		HeaderMiniComponent,
		FooterComponent,
		StepCounterComponent,
	],
	styleUrl: "./address-contact.component.scss",
	template: `
		<ion-content>
			<div class="app-layout">
				<!-- Header -->
				<app-header-mini [mode]="'workflow'"></app-header-mini>

				<!-- Content -->
				<div class="app-content">
					<div class="flex h-full flex-row">
						<!-- Step Counter -->
						<div class="flex h-full">
							<app-step-counter [activeStep]="activeStepId"></app-step-counter>
						</div>

						<div class="content-wrapper">
							<!-- Title -->
							<div class="title-container">
								<h2>ข้อมูลที่อยู่และการติดต่อ</h2>
							</div>

							<div *ngFor="let item of address; index" class="address-item">
								<div class="address-header">
									<p class="address-title">
										{{ item.title }}
										<span *ngIf="item.require" class="required">*</span>
									</p>
									<p class="edit-button" (click)="editAddress(item)">
										{{ item?.value ? "แก้ไขข้อมูล" : "เพิ่มข้อมูล" }}
									</p>
								</div>

								<p class="address-value">{{ item?.value || "-" }}</p>
								@if (item.title === "ที่อยู่ปัจจุบัน") {
									<hr class="divider" />
								}
							</div>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper">
						<p-button
							label="ต่อไป"
							[fluid]="true"
							[loading]="isLoading()"
							[disabled]="!isFormValid"
							(onClick)="handleNext()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class AddressContactComponent
	extends WorkflowStepBaseComponent
	implements OnInit
{
	// Inject StepCounterService
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);
	public stepCounterService = inject(StepCounterService);

	// Active step ID (current step)
	activeStepId: string = "2.1";
	addressInfo: AddressInfo[] = [];
	address: AddressItem[] = [
		{
			title: "ที่อยู่ปัจจุบัน",
			type: "CONTACT",
			value: "",
			require: true,
		},
		{
			title: "ลักษณะที่อยู่อาศัย",
			type: "CATEGORY",
			value: "",
			require: true,
		},
	];

	get isFormValid(): boolean {
		return this.address.every((item) => item.value !== "");
	}

	async ngOnInit(): Promise<void> {
		// Get address data from API
		await this.getAddressInfo();

		// Initialize step counter service with default loan application steps
		this.stepCounterService.initializeLoanApplicationSteps(this.activeStepId);
	}

	async getAddressInfo() {
		await this.runWithLoading(async () => {
			try {
				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.getAddressInfo({
					txn_id: txnId,
				});
				if (apiResponse.isSuccess() && apiResponse.hasData()) {
					this.addressInfo = apiResponse.data;
					this.mapAddressValue();
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	private mapAddressValue() {
		if (!this.addressInfo || !Array.isArray(this.addressInfo)) {
			console.warn("Address Info is not valid");
			return;
		}

		this.addressInfo.forEach((item: AddressInfo) => {
			const addressItem = this.address.find(
				(addr) => addr.type === item.addressType,
			);
			if (!addressItem) return;

			if (item.addressType === "CONTACT") {
				// จัดการที่อยู่ปัจจุบัน
				const addressParts = [
					item.houseNo,
					item.villageNo ? `หมู่ ${item.villageNo}` : "",
					item.alley ? `ซอย ${item.alley}` : "",
					item.street ? `ถนน ${item.street}` : "",
					item.subDistrict,
					item.district,
					item.province,
					item.postalCode,
				];

				addressItem.value = addressParts
					.filter((part) => part && part.trim()) // กรองค่าว่างและ whitespace
					.join(" ");

				// จัดการลักษณะที่อยู่อาศัย
				if (item.housingType) {
					const categoryItem = this.address.find(
						(addr) => addr.type === "CATEGORY",
					);
					if (categoryItem) {
						categoryItem.value = item.housingType;
					}
				}
			}
		});
	}

	/**
	 * Edit address item
	 * @param item Address item to edit
	 */
	async editAddress(item: AddressItem) {
		const modal = await this.modalController.create({
			component: AddressInfoFormComponent,
			componentProps: { item, addressInfo: this.addressInfo },
			cssClass: "global-dialog-modal modal-blur-backdrop",
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		console.log("on close data:", data);
		if (data && data.refresh) {
			await this.getAddressInfo();
		}
	}

	/**
	 * Override next method to include address data
	 */
	handleNext(): void {
		// Mark current step as completed in the step counter service
		this.stepCounterService.markStepCompleted(this.activeStepId);

		// Dispatch next event with address data
		super.next({
			addressData: this.workflowData?.["addressData"] || {},
		});
	}

	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || error.statusText || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} <br> ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
