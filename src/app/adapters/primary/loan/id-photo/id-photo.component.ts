import { CommonModule } from "@angular/common";
import { Component, computed, inject } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { DomSanitizer, SafeUrl } from "@angular/platform-browser";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";
import { IdCardDetectionService } from "../take-id-photo/id-card-detection.service";
import { FaceDetectionService } from "../take-face-photo/face-detection.service";

@Component({
	selector: "app-id-photo-modal",
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		ButtonModule,
		HeaderMiniComponent,
		FooterComponent,
	],
	styleUrl: "./id-photo.component.scss",
	template: `
		<ion-content>
			<div class="app-layout">
				<!-- Header -->
				<app-header-mini mode="workflow"></app-header-mini>

				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper">
						<!-- Header Section -->
						<div class="text-center">
							<h1 class="page-title">ถ่ายรูปบัตรประชาชนลูกค้า</h1>
						</div>

						<div class="rounded-lg bg-white p-4">
							<div class="flex flex-col items-center">
								<!-- Dotted Border Area -->
								<div
									class="w-full rounded-lg border-2 border-dashed border-blue-500 bg-blue-50 p-4">
									<div
										class="relative flex min-h-[300px] flex-col items-center justify-center overflow-hidden rounded-lg">
										<!-- Avatar Placeholder -->
										<div
											class="mb-6 flex h-auto w-[300px] items-center justify-center rounded-full">
											<img
												[src]="getPathImage()"
												alt="Avatar"
												class="h-auto w-full object-cover" />
										</div>

										<!-- Take Photo Button -->
										<p-button
											label="ถ่ายรูปบัตรประชาชน"
											severity="primary"
											styleClass="!bg-white w-56"
											[outlined]="true"
											(click)="startPhotoSubflow()" />
									</div>
								</div>
							</div>
						</div>

						<!-- Info Section -->
						<div class="rounded-lg bg-white p-4">
							<h2 class="mb-4 text-base font-normal text-black">
								ข้อมูลหน้าบัตรประชาชน
							</h2>
							<div class="space-y-4">
								<p class="flex flex-col">
									<span class="mb-2 text-sm font-normal text-gray-900">
										หมายเลขบัตรประชาชน
									</span>
									<span class="ml-4 text-sm font-light text-gray-500">
										{{ getIdCardNumber() }}
									</span>
								</p>
							</div>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper">
						<p-button
							label="ดำเนินการต่อ"
							styleClass="w-full"
							[loading]="isLoading()"
							(onClick)="handleNext()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class IdPhotoModalComponent extends WorkflowStepBaseComponent {
	private readonly idCardDetectionService = inject(IdCardDetectionService);
	private readonly faceDetection = inject(FaceDetectionService);
	private readonly domSanitizer = inject(DomSanitizer);

	constructor() {
		super();
	}

	ngOnInit(): void {}

	public getPathImage(): string | SafeUrl {
		const data = this.idCardDetectionService.thaiFront();
		if (data?.images.original) {
			return this.domSanitizer.bypassSecurityTrustUrl(data.images.original);
		} else {
			return "assets/svgs/id-card.svg";
		}
	}

	public getIdCardNumber(): string {
		return (
			this.idCardDetectionService.thaiFront()?.recognition?.idNumber ||
			"กรุณาถ่ายรูปบัตรประชาชนลูกค้า"
		);
	}

	public faceImageUrl = computed(() => {
		const base64Image = this.faceDetection.getFaceImageUrl() ?? "";
		return this.domSanitizer.bypassSecurityTrustUrl(base64Image);
	});

	getFaceImageUrl(): SafeUrl | null {
		if (this.faceDetection.getFaceImageUrl() !== null) {
			return this.faceImageUrl();
		} else {
			return null;
		}
	}

	/**
	 * Start the photo capture subflow
	 */
	startPhotoSubflow() {
		// Start the subflow with the first step of the selfie subflow
		this.startSubflow();
	}

	/**
	 * Override next method to check if both photos are taken
	 */
	handleNext(): void {
		// Update workflow data with photos
		super.next();
	}

	/**
	 * Override back method
	 */
	handleBack(): void {
		console.log("Back button clicked");
		super.back();
	}
}
