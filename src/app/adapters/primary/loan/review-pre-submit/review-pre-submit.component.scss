.panel-container {
	@apply flex flex-1 flex-col overflow-hidden;
}

.section-header {
	@apply mb-4 bg-primary/10 p-3;
}

.section-title-text {
	@apply text-base font-medium text-[#002E60];
}

.page-container {
	@apply flex h-full flex-col justify-start gap-4 rounded-b-md bg-white shadow-sm;
}

.small-image-container {
	@apply mx-auto my-4 max-h-[200px] w-auto rounded-md p-2;
}

.field-container {
	@apply flex justify-between space-y-2;
}

.field-label {
	@apply text-sm font-light text-gray-500;
}

.field-value {
	@apply text-sm font-light text-gray-900;
}

.disclaimer-text {
	@apply mt-3 flex flex-col rounded text-right text-xs text-gray-500;
}