import { CommonModule } from "@angular/common";
import { Component } from "@angular/core";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-customer-confirm-guideline",
	styleUrls: ["./customer-confirm-guideline.component.scss"],
	imports: [
		CommonModule,
		IonicModule,
		HeaderMiniComponent,
		FooterComponent,
		ButtonModule,
	],
	template: `
		<ion-content>
			<div class="app-layout">
				<!-- Header -->
				<app-header-mini mode="workflow"></app-header-mini>

				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper !justify-center">
						<!-- Header Section -->
						<div class="text-center">
							<h1 class="page-title">ยืนยันตัวตนเพื่อสมัครสินเชื่อ</h1>
						</div>

						<div class="rounded-lg bg-white p-4">
							<div class="flex flex-col items-center">
								<!-- Dotted Border Area -->
								<img
									src="assets/images/customer-starmoney.png"
									alt="Customer Confirm Guideline"
									class="mb-12 h-auto w-full max-w-56 object-contain" />

								<p class="mb-4 text-center text-base text-gray-900">
									โปรดเตรียมโทรศัพท์มือถือให้พร้อมสำหรับการรับรหัส OTP
									<br />
									การสมัครสินเชื่อนี้มีเพียงไม่กี่ขั้นตอนเท่านั้น
								</p>
								<p class="text-center text-base text-gray-500">
									ได้แก่การยืนยันรหัส OTP การถ่ายภาพใบหน้าและการให้ความยินยอม
									<br />
									โดยท่านสามารถขอรับคำแนะนำจากพนักงานได้ตลอดเวลา
								</p>
							</div>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper">
						<p-button
							label="เริ่มต้นยืนยันตัวตน"
							styleClass="w-full"
							[loading]="isLoading()"
							(onClick)="next()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class CustomerConfirmGuidelineComponent extends WorkflowStepBaseComponent {}
