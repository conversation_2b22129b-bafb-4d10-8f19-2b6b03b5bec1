import { CommonModule } from "@angular/common";
import { Component } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-confirm-face-photo",
	standalone: true,
	imports: [
		CommonModule,
		IonicModule,
		FormsModule,
		FooterComponent,
		ButtonModule,
	],
	template: `
		<ion-content>
			<div class="app-layout-dialog">
				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper">
						<!-- Header Section -->
						<div class="text-center">
							<h1 class="page-title">ถ่ายรูปใบหน้า</h1>
						</div>

						<div class="rounded-lg bg-white p-4">
							<div class="flex flex-col items-center">
								<!-- Dotted Border Area -->
								<div
									class="w-full rounded-lg border-2 border-dashed border-blue-500 bg-blue-50 p-4">
									<div
										class="relative flex min-h-[300px] max-w-[380px] flex-col items-center justify-center overflow-hidden rounded-lg">
										<!-- Avatar Placeholder -->
										<div
											class="mb-6 flex items-center justify-center rounded-full">
											<img
												src="assets/images/person-face.png"
												alt="Avatar"
												class="h-auto w-full object-cover" />
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Info Section -->
						<div class="rounded-lg bg-white p-4">
							<h2 class="mb-4 text-base font-normal text-black">
								ข้อมูลใบหน้า
							</h2>
							<span class="ml-4 text-sm font-light text-gray-500">ผ่าน</span>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer styleClass="!border-none !justify-center">
					<div class="footer-wrapper max-w-md !justify-center">
						<p-button
							label="ถ่ายรูปใหม่"
							styleClass="w-full"
							[outlined]="true"
							[loading]="isLoading()"
							(onClick)="back()" />
						<p-button
							label="ใช้รูปนี้"
							styleClass="w-full"
							[loading]="isLoading()"
							(onClick)="next()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class ConfirmFacePhotoComponent extends WorkflowStepBaseComponent {
	constructor() {
		super();
	}

	/**
	 * Retake selfie
	 */
	retakeSelfie(): void {
		console.log("Going back to take selfie step");

		// Go back to the take selfie step
		this.workflowModalService.dispatch({
			command: "back_subflow",
			fromStepId: this.stepId,
		});
	}

	/**
	 * Confirm selfie and close subflow
	 */
	confirmSelfie(): void {
		console.log("Confirming selfie and closing subflow");

		// Update parent workflow data with selfie confirmation
		super.closeSubflow({
			selfiePhoto: "base64_encoded_selfie_image",
			selfieConfirmed: true,
		});
	}

	/**
	 * Override goNext method to close subflow
	 */
	goNext(): void {
		this.confirmSelfie();
	}
}
