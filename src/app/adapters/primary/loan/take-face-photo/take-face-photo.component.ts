import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	computed,
	ElementRef,
	inject,
	On<PERSON><PERSON>roy,
	Renderer2,
	signal,
	ViewChild,
} from "@angular/core";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { Subject } from "rxjs";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { StatusNotificationOptions } from "src/app/core/application/status-notification.service";
import { WebcamError } from "ts-webcam";
import { CircularProgressLoadingModalComponent } from "../../shared/components/circular-progress-loading-modal/circular-progress-loading-modal.component";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import {
	StatusNotificationModalComponent,
	StatusType,
} from "../../shared/components/status-notification-modal/status-notification-modal.component";
import { CameraHandlerOptions, WebcamService } from "../../shared/services/webcam.service";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";
import { CheckResultType } from "../check-id-card/check-id-card.component";
import {
	ANIMATION_DURATIONS,
	CaptureMode,
	ID_PHOTO_RESOLUTIONS,
	IdCardOverlayConfig,
	OverlayDimensions,
	PERFORMANCE_CONFIG,
	ScanningState,
	STATUS_COLORS,
} from "../take-id-photo/take-id-photo.component";
import { FaceDetectionResult, FaceDetectionService } from "./face-detection.service";

// Face photo specific constants
export const FACE_CARD_CONFIG: IdCardOverlayConfig = {
	aspectRatio: 0.765,
	baseWidth: 215,
	baseHeight: 281,
	overlayWidthPercentage: 1,
};

@Component({
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		FooterComponent,
		ButtonModule,
		CircularProgressLoadingModalComponent,
		StatusNotificationModalComponent,
	],
	selector: "app-take-face-photo",
	styleUrl: "./take-face-photo.component.scss",
	templateUrl: "./take-face-photo.component.html",
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TakeFacePhotoComponent
	extends WorkflowStepBaseComponent
	implements AfterViewInit, OnDestroy
{
	// ViewChild references
	@ViewChild("videoElement", { static: false })
	private readonly videoElement!: ElementRef<HTMLVideoElement>;
	@ViewChild("svgOverlay", { static: false })
	private readonly svgOverlay!: ElementRef<SVGElement>;

	// Services
	private readonly webcamService = inject(WebcamService);
	private readonly faceDetectionService = inject(FaceDetectionService);
	private readonly appUtilsService = inject(AppUtilsService);
	private readonly renderer = inject(Renderer2);
	private readonly cdr = inject(ChangeDetectorRef);

	// Reactive state with signals
	private readonly _scanningState = signal<ScanningState>(ScanningState.IDLE);
	private readonly _overlayDimensions = signal<OverlayDimensions | null>(null);
	private readonly _processingMode = signal<CaptureMode>(CaptureMode.AUTO);
	private readonly _guidelineText = signal<string>("");
	private readonly _webcamStatus = signal<string>("initializing");
	private readonly _isLoadingProcessing = signal<boolean>(false);
	private readonly _isShowStatusNotification = signal<boolean>(false);
	private readonly _statusNotificationOptions = signal<
		(StatusNotificationOptions & { statusType: StatusType }) | null
	>(null);

	// Computed properties for template binding
	public readonly currentWebcamStatus = computed(() => this._webcamStatus());
	public readonly currentScanningState = computed(() => this._scanningState());
	public readonly overlayDimensions = computed(() => this._overlayDimensions());
	public readonly captureMode = computed(() => this._processingMode());
	public readonly buttonLabel = computed(() => this._getButtonLabel());
	public readonly isLoadingProcessing = computed(() => this._isLoadingProcessing());
	public readonly isShowStatusNotification = computed(() => this._isShowStatusNotification());
	public readonly statusNotificationOptions = computed(() => this._statusNotificationOptions());

	// Face detection computed properties
	public readonly faceInstructionState = computed(() =>
		this.faceDetectionService.getInstructionState(),
	);

	public readonly faceInstructionStyle = computed(() => {
		const instructionState = this.faceInstructionState();
		const baseStyle = "instruction";

		switch (instructionState) {
			case "no-face":
				return "text-red-500";
			case "detection":
				return `${baseStyle} text-primary`;
			case "success":
				return `${baseStyle} text-green-500`;
			default:
				return `${baseStyle} text-gray-900`;
		}
	});

	public readonly faceInstructionText = computed(() => {
		const instructionState = this.faceInstructionState();
		const instructionsWording = this.faceDetectionService.instructionsWording();
		const faceDetectionState = this.faceDetectionService.faceDetectionState();

		switch (instructionState) {
			case "no-face":
				return "ไม่พบใบหน้า";
			case "initial":
			case "too-close":
			case "too-far":
			case "hold-on":
			case "loading":
			case "obstruction":
			case "success":
				return instructionsWording[instructionState] || "กรุณาถ่ายรูปใบหน้าของท่าน";
			case "adjust-head":
				return faceDetectionState.instruction;
			case "detection":
				const detections = this.faceDetectionService.detections();
				const currentIndex = faceDetectionState.currentDetectionIndex;
				const actionKey = faceDetectionState.detectionActions[currentIndex];
				return detections[actionKey]?.instruction || "กรุณาถ่ายรูปใบหน้าของท่าน";
			default:
				return "กรุณาถ่ายรูปใบหน้าของท่าน";
		}
	});

	public readonly isProcessingActive = computed(
		() => this.currentScanningState() === ScanningState.SCANNING,
	);
	public readonly isReadyForCapture = computed(
		() =>
			this.currentWebcamStatus() === "ready" && this.currentScanningState() !== ScanningState.ERROR,
	);
	public readonly shouldShowOverlay = computed(() => this.overlayDimensions() !== null);

	// Cleanup and performance optimization properties
	private readonly destroy$ = new Subject<void>();
	private resizeObserver?: ResizeObserver;
	private animationFrameId?: number;
	private resizeTimeoutId?: ReturnType<typeof setTimeout>;
	private webcamStatusSyncInterval?: ReturnType<typeof setInterval>;

	// Performance tracking
	private changeDetectionCycles = 0;
	private lastOcrUpdateTime = 0;
	private isDetached = false;

	constructor() {
		super();
		this.setupPerformanceOptimizations();
	}

	/**
	 * Setup performance optimizations for OnPush change detection
	 */
	private setupPerformanceOptimizations(): void {
		// Detach change detector initially to prevent unnecessary cycles
		this.detachChangeDetector();
	}

	/**
	 * Detach change detector for manual control
	 */
	private detachChangeDetector(): void {
		if (!this.isDetached) {
			this.cdr.detach();
			this.isDetached = true;
		}
	}

	/**
	 * Reattach change detector when needed
	 */
	private reattachChangeDetector(): void {
		if (this.isDetached) {
			this.cdr.reattach();
			this.isDetached = false;
		}
	}

	/**
	 * Manually trigger change detection with cycle limit
	 */
	private triggerChangeDetection(): void {
		if (this.changeDetectionCycles < PERFORMANCE_CONFIG.maxChangeDetectionCycles) {
			this.changeDetectionCycles++;
			this.cdr.markForCheck();

			// Reset cycle counter after a delay
			setTimeout(() => {
				this.changeDetectionCycles = 0;
			}, PERFORMANCE_CONFIG.debounceTime);
		}
	}

	/**
	 * Optimized method to update UI state with throttling
	 */
	private updateUIState(): void {
		const now = performance.now();
		if (now - this.lastOcrUpdateTime > PERFORMANCE_CONFIG.ocrUpdateThrottle) {
			this.lastOcrUpdateTime = now;
			this.triggerChangeDetection();
		}
	}

	/**
	 * Sync webcam status from service to signal
	 */
	private syncWebcamStatus(): void {
		const currentStatus = this.webcamService.getStatus();
		if (this._webcamStatus() !== currentStatus) {
			this._webcamStatus.set(currentStatus);
			this.triggerChangeDetection();
		}
	}

	/**
	 * Start periodic webcam status synchronization
	 */
	private startWebcamStatusSync(): void {
		// Initial sync
		this.syncWebcamStatus();

		// Set up periodic sync every 100ms for responsive status updates
		this.webcamStatusSyncInterval = setInterval(() => {
			this.syncWebcamStatus();
		}, 100);
	}

	/**
	 * Stop webcam status synchronization
	 */
	private stopWebcamStatusSync(): void {
		if (this.webcamStatusSyncInterval) {
			clearInterval(this.webcamStatusSyncInterval);
			this.webcamStatusSyncInterval = undefined;
		}
	}

	/**
	 * Check if ViewChild elements are available
	 */
	private areViewChildElementsReady(): boolean {
		return !!(this.videoElement?.nativeElement && this.svgOverlay?.nativeElement);
	}

	/**
	 * Wait for ViewChild elements to be ready with retry mechanism
	 */
	private async waitForViewChildElements(maxRetries: number = 5): Promise<boolean> {
		for (let i = 0; i < maxRetries; i++) {
			if (this.areViewChildElementsReady()) {
				return true;
			}
			await this.delay(100);
		}
		return false;
	}

	async ngAfterViewInit(): Promise<void> {
		try {
			// Reattach change detector for initialization
			this.reattachChangeDetector();

			// Wait for ViewChild elements to be ready
			const elementsReady = await this.waitForViewChildElements();
			if (!elementsReady) {
				throw new Error("ViewChild elements failed to initialize after multiple retries");
			}

			this.setupResizeObserver();
			await this.initializeCamera();

			// Start periodic webcam status sync
			this.startWebcamStatusSync();

			// Trigger initial change detection
			this.triggerChangeDetection();
		} catch (error) {
			await this.handleError(error, "Component initialization failed");
		}
	}

	ngOnDestroy(): void {
		this.cleanup();
	}

	/**
	 * Initialize camera and start webcam
	 */
	private async initializeCamera(): Promise<void> {
		try {
			// Check if video element is available
			if (!this.videoElement?.nativeElement) {
				throw new Error("Video element not available for camera initialization");
			}

			// Attach video element for camera preview
			this.webcamService.attachPreviewElement(this.videoElement.nativeElement);

			// Camera handler options
			const options: CameraHandlerOptions = {
				onStart: () => this.onCameraStart(),
				onError: (error: any) => this.onCameraError(error),
			};

			// Create camera configuration
			const configuration = await this.webcamService.createCameraConfiguration(
				"user",
				ID_PHOTO_RESOLUTIONS,
				options,
			);
			if (configuration) {
				await this.webcamService.startCamera(configuration, options);
			}
		} catch (error) {
			await this.handleError(error, "Camera initialization failed");
		}
	}

	/**
	 * Handle camera start event
	 */
	private async onCameraStart(): Promise<void> {
		this.syncWebcamStatus();
		this.updateOverlayDimensions();
		await this.startProcessing();
		this.triggerChangeDetection();
	}

	private onCameraError(error: WebcamError): void {
		this.syncWebcamStatus();
		this._scanningState.set(ScanningState.ERROR);
		this.handleError(error, "Camera initialization failed");
	}

	private async startProcessing(): Promise<void> {
		this._scanningState.set(ScanningState.IDLE);

		// Start processing only in auto mode
		if (this._processingMode() === CaptureMode.AUTO) {
			this._scanningState.set(ScanningState.SCANNING);
			await this.faceDetectionService.startProcessing(
				this.handleScanSuccess.bind(this),
				this.handleFaceDetectionResult.bind(this),
			);
		}
	}

	private async handleScanSuccess(result?: FaceDetectionResult): Promise<void> {
		if (!result) return;

		this._guidelineText.set("");
		await this.delay(ANIMATION_DURATIONS.success);

		this._isLoadingProcessing.set(true);
		this.triggerChangeDetection();

		try {
			const apiResult = await this.mockApiCall();
			this._isLoadingProcessing.set(false);
			this.triggerChangeDetection();

			if (apiResult === CheckResultType.Success) {
				await this.handleSuccessfulCapture();
			} else {
				await this.handleFailedCapture();
			}
		} catch (error) {
			this._isLoadingProcessing.set(false);
			this.triggerChangeDetection();
			await this.handleError(error, "Face photo processing failed");
		}
	}

	private async handleSuccessfulCapture(): Promise<void> {
		this._scanningState.set(ScanningState.COMPLETED);
		this.triggerChangeDetection();
		await this.delay(ANIMATION_DURATIONS.success);
		this.next();
	}

	private async handleFailedCapture(): Promise<void> {
		this._statusNotificationOptions.set({
			statusType: StatusType.WARNING,
			title: "ถ่ายรูปไม่สำเร็จ",
			message: "ขออภัย! ท่านถ่ายรูปไม่สำเร็จในเวลาที่กำหนด",
			description: "ท่านสามารถเริ่มต้นกระบวนการถ่ายรูปใหม่ได้อีกครั้ง",
			errorCode: "FACE_CAPTURE_FAILED",
			primaryButtonLabel: "ตกลง",
			fullscreen: false,
			onPrimaryAction: async () => {
				this._isShowStatusNotification.set(false);
				await this.retryCapture();
			},
		});

		this._isShowStatusNotification.set(true);
		this.triggerChangeDetection();
	}

	private mockApiCall(): Promise<CheckResultType> {
		return new Promise((resolve) => {
			const responseTime = Math.random() * 1000 + 2000;
			setTimeout(() => {
				const random = Math.random();
				const result = random < 0.7 ? CheckResultType.Success : CheckResultType.Warning;
				resolve(result);
			}, responseTime);
		});
	}

	/**
	 * Handle face detection result from processing
	 * Updates overlay dimensions and UI state
	 */
	public handleFaceDetectionResult(_result: FaceDetectionResult): void {
		// Update overlay dimensions to trigger color change
		this.updateOverlayDimensions();

		// Use throttled UI update for performance
		this.updateUIState();
	}

	/**
	 * Setup resize observer for responsive overlay with performance optimization
	 */
	private setupResizeObserver(): void {
		if (!this.videoElement?.nativeElement) {
			console.warn("Video element not available for resize observer");
			return;
		}

		this.resizeObserver = new ResizeObserver(() => {
			// Clear existing timeouts
			if (this.resizeTimeoutId) {
				clearTimeout(this.resizeTimeoutId);
			}
			if (this.animationFrameId) {
				cancelAnimationFrame(this.animationFrameId);
			}

			// Debounce resize events for better performance
			this.resizeTimeoutId = setTimeout(() => {
				this.animationFrameId = requestAnimationFrame(() => {
					// Double-check elements are still available
					if (this.areViewChildElementsReady()) {
						this.updateOverlayDimensions();
						// Only trigger change detection if necessary
						this.updateUIState();
					}
				});
			}, PERFORMANCE_CONFIG.resizeDebounceTime);
		});

		this.resizeObserver.observe(this.videoElement.nativeElement);
	}

	public setOverlayWidthPercentage(percentage: number): void {
		if (percentage < 0.1 || percentage > 1.0) {
			return;
		}

		FACE_CARD_CONFIG.overlayWidthPercentage = percentage;
		this.updateOverlayDimensions();
	}

	public getOverlayWidthPercentage(): number {
		return FACE_CARD_CONFIG.overlayWidthPercentage;
	}

	public getOverlayStatus(): string {
		const scanningState = this.currentScanningState();
		const isHoldOn = this.faceDetectionService.getInstructionState() === "hold-on";

		if (scanningState === ScanningState.SCANNING && isHoldOn) {
			return "green";
		}

		if (scanningState === ScanningState.SCANNING) {
			return "white";
		}

		return scanningState;
	}

	/**
	 * Get overlay color based on current state
	 */
	private _getOverlayColor(): string {
		const scanningState = this.currentScanningState();

		// For scanning state, start with white
		if (scanningState === ScanningState.SCANNING) {
			return STATUS_COLORS.white;
		}

		// Map scanning states to colors
		switch (scanningState) {
			case ScanningState.IDLE:
				return STATUS_COLORS.initializing;
			case ScanningState.COMPLETED:
				return STATUS_COLORS.success;
			case ScanningState.ERROR:
				return STATUS_COLORS.error;
			default:
				return STATUS_COLORS.white;
		}
	}

	/**
	 * Update overlay dimensions based on video size
	 */
	private updateOverlayDimensions(): void {
		const videoElement = this.videoElement?.nativeElement;
		if (!videoElement) {
			console.warn("Video element not available for overlay dimension update");
			return;
		}

		const { videoWidth, videoHeight } = videoElement;
		const { width: containerWidth, height: containerHeight } = videoElement.getBoundingClientRect();

		if (!videoWidth || !videoHeight || !containerWidth || !containerHeight) {
			console.warn("Video dimensions not available yet");
			return;
		}

		// Calculate actual video display size (object-contain)
		const videoAspectRatio = videoWidth / videoHeight;
		const containerAspectRatio = containerWidth / containerHeight;

		let actualWidth: number;
		let actualHeight: number;

		if (containerAspectRatio > videoAspectRatio) {
			actualHeight = containerHeight;
			actualWidth = actualHeight * videoAspectRatio;
		} else {
			actualWidth = containerWidth;
			actualHeight = actualWidth / videoAspectRatio;
		}

		// Calculate overlay dimensions based on face card aspect ratio
		const overlayWidth = actualWidth * FACE_CARD_CONFIG.overlayWidthPercentage;
		const overlayHeight = overlayWidth / FACE_CARD_CONFIG.aspectRatio;

		// Ensure overlay doesn't exceed container height
		const maxOverlayHeight = actualHeight * 0.9; // Max 90% of video height
		const finalOverlayWidth =
			overlayHeight > maxOverlayHeight
				? maxOverlayHeight * FACE_CARD_CONFIG.aspectRatio
				: overlayWidth;
		const finalOverlayHeight = finalOverlayWidth / FACE_CARD_CONFIG.aspectRatio;

		const scale = finalOverlayWidth / FACE_CARD_CONFIG.baseWidth;

		const offsetX = (containerWidth - finalOverlayWidth) / 2;
		const offsetY = (containerHeight - finalOverlayHeight) / 2;

		this._overlayDimensions.set({
			width: finalOverlayWidth,
			height: finalOverlayHeight,
			scale,
			offsetX,
			offsetY,
		});

		this.updateOverlayStyles();
	}

	/**
	 * Update SVG overlay styles
	 */
	private updateOverlayStyles(): void {
		const dimensions = this._overlayDimensions();
		const svgElement = this.svgOverlay?.nativeElement;

		if (!dimensions || !svgElement) {
			console.warn("Overlay dimensions or SVG element not available for style update");
			return;
		}

		const { width, height, offsetX, offsetY } = dimensions;
		const color = this._getOverlayColor();

		// Update SVG container position and size
		this.renderer.setStyle(svgElement, "position", "absolute");
		this.renderer.setStyle(svgElement, "left", `${offsetX}px`);
		this.renderer.setStyle(svgElement, "top", `${offsetY}px`);
		this.renderer.setStyle(svgElement, "width", `${width}px`);
		this.renderer.setStyle(svgElement, "height", `${height}px`);
		this.renderer.setStyle(svgElement, "pointer-events", "none");
		this.renderer.setStyle(svgElement, "z-index", "10");

		// Update path color based on OCR results or status
		this.renderer.setStyle(svgElement, "color", color);

		// Add transition for smooth color changes
		this.renderer.setStyle(svgElement, "transition", "color 0.3s ease-out");
	}

	private async cleanup(): Promise<void> {
		this.destroy$.next();
		this.destroy$.complete();

		// Stop webcam status synchronization
		this.stopWebcamStatusSync();

		// Clean up performance optimization timers
		if (this.resizeTimeoutId) {
			clearTimeout(this.resizeTimeoutId);
			this.resizeTimeoutId = undefined;
		}

		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
			this.resizeObserver = undefined;
		}

		if (this.animationFrameId) {
			cancelAnimationFrame(this.animationFrameId);
			this.animationFrameId = undefined;
		}

		// Reattach change detector before cleanup to ensure proper cleanup
		this.reattachChangeDetector();

		// Stop processing
		await this.faceDetectionService.stopProcessing();
		this.webcamService.instance.stop();
		this._scanningState.set(ScanningState.IDLE);
	}

	/**
	 * Handle photo capture with performance optimization
	 */
	public async handleTakePhoto(): Promise<void> {
		if (this.currentWebcamStatus() !== "ready") return;

		try {
			this._scanningState.set(ScanningState.SCANNING);
			this.triggerChangeDetection();

			// Take actual photo
			const photoData = await this.webcamService.takePhoto();
			if (photoData) {
				this._scanningState.set(ScanningState.COMPLETED);
				this.triggerChangeDetection();
				await this.delay(ANIMATION_DURATIONS.success);
				this.next();
			} else {
				throw new Error("Failed to capture photo");
			}
		} catch (error) {
			this._scanningState.set(ScanningState.ERROR);
			this.triggerChangeDetection();
			await this.handleError(error, "Photo capture failed");
		}
	}

	public async handleMirrorClick(): Promise<void> {
		this.webcamService.instance.toggleMirror();
	}

	/**
	 * Retry capture process - reset state and restart scanning with performance optimization
	 */
	public async retryCapture(): Promise<void> {
		try {
			// Reset all state using utility method
			this.resetScanningState();
			this.triggerChangeDetection();

			// Restart processing if in auto mode
			setTimeout(async () => {
				await this.startProcessing();
			}, 1000);
		} catch (error) {
			this._scanningState.set(ScanningState.ERROR);
			this.triggerChangeDetection();
			await this.handleError(error, "Retry capture failed");
		}
	}

	/**
	 * Utility delay function
	 */
	private delay(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * Check if scanning is in progress
	 */
	public isScanningInProgress(): boolean {
		return this.currentScanningState() === ScanningState.SCANNING;
	}

	/**
	 * Check if scanning is completed
	 */
	public isScanningCompleted(): boolean {
		return this.currentScanningState() === ScanningState.COMPLETED;
	}

	/**
	 * Check if there's an error state
	 */
	public hasError(): boolean {
		return this.currentScanningState() === ScanningState.ERROR;
	}

	/**
	 * Reset scanning state to idle
	 */
	public resetScanningState(): void {
		this._scanningState.set(ScanningState.IDLE);
		this._isLoadingProcessing.set(false);
		this._isShowStatusNotification.set(false);
		this._statusNotificationOptions.set(null);
		this._guidelineText.set("");
		this.faceDetectionService.resetFaceDetectionState();
	}

	/**
	 * Set capture mode (AUTO or MANUAL)
	 */
	public setCaptureMode(mode: CaptureMode): void {
		this._processingMode.set(mode);
	}

	public getCaptureMode(): CaptureMode {
		return this._processingMode();
	}

	private _getButtonLabel(): string {
		const status = this.currentScanningState();
		switch (status) {
			case ScanningState.SCANNING:
				return "กำลังถ่าย...";
			case ScanningState.IDLE:
				return "รอสักครู่...";
			case ScanningState.COMPLETED:
				return "เสร็จสิ้น";
			case ScanningState.ERROR:
				return "ลองใหม่";
			default:
				return "ถ่ายรูป";
		}
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage ? `${errorMessage} : ${apiMessage}` : errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => {},
		});

		return false;
	}
}
