<ion-content>
	<div class="app-layout-dialog">
		<!-- Overlay Modals -->
		<app-circular-progress-loading-modal
			*ngIf="isLoadingProcessing() && !isShowStatusNotification()"
			class="overlay-modal"
			[title]="''"
			[statusMessage]="''"
			[description]="'ระบบกำลังทำการประมวลผล\nกรุณารอจนกว่าระบบจะดำเนินการเสร็จสิ้น'"
			[indeterminateProgress]="true"
			[autoProgress]="false"></app-circular-progress-loading-modal>

		<app-status-notification-modal
			*ngIf="isShowStatusNotification()"
			class="overlay-modal"
			[title]="statusNotificationOptions()?.title || ''"
			[statusType]="statusNotificationOptions()?.statusType!"
			[message]="statusNotificationOptions()?.message || ''"
			[description]="statusNotificationOptions()?.description || ''"
			[errorCode]="statusNotificationOptions()?.errorCode || ''"
			[errorLabel]="statusNotificationOptions()?.errorLabel || 'Code:'"
			[primaryButtonLabel]="statusNotificationOptions()?.primaryButtonLabel || 'ตกลง'"
			[secondaryButtonLabel]="statusNotificationOptions()?.secondaryButtonLabel || ''"
			[onPrimaryAction]="statusNotificationOptions()?.onPrimaryAction ?? null"
			[onSecondaryAction]="
				statusNotificationOptions()?.onSecondaryAction ?? null
			"></app-status-notification-modal>

		<div class="app-content">
			<div class="content-wrapper">
				<div class="header-section">
					<h1 class="page-title">ปฏิบัติตามคำอธิบายด้านล่าง</h1>
					<h1
						class="page-subtitle !text-base !font-medium !text-gray-900"
						[ngClass]="faceInstructionStyle()">
						{{ faceInstructionText() }}
					</h1>
					<p class="page-subtitle">หลีกเลี่ยงการใส่แว่นตา และขยับใบหน้าให้ตรงกรอบ</p>
				</div>
				<div class="camera-container">
					<div class="mirror-button-container">
						<p-button
							[rounded]="true"
							[outlined]="true"
							[loading]="isLoading()"
							(onClick)="handleMirrorClick()">
							<img src="assets/svgs/switch-camera.svg" alt="Switch Camera" class="h-5 w-5" />
						</p-button>
					</div>
					<video #videoElement autoplay playsinline muted class="camera-video"></video>
					<svg
						#svgOverlay
						class="id-card-svg-overlay"
						[attr.data-status]="getOverlayStatus()"
						width="215"
						height="281"
						viewBox="0 0 215 281"
						fill="none"
						xmlns="http://www.w3.org/2000/svg">
						<path
							d="M107.5 2.35156C165.443 2.35156 212.773 54.7471 212.773 119.846C212.773 152.43 200.892 192.276 181.644 223.957C162.334 255.74 136.037 278.65 107.5 278.65C78.962 278.65 52.6661 255.74 33.356 223.957C14.108 192.276 2.22705 152.43 2.22705 119.846C2.22709 54.7472 49.5566 2.35172 107.5 2.35156Z"
							stroke="currentColor"
							stroke-width="4"
							fill="none" />
					</svg>
					<div *ngIf="currentWebcamStatus() === 'initializing'" class="loading-overlay">
						<ion-spinner name="circular" class="loading-spinner"></ion-spinner>
						<p class="loading-text">กำลังเปิดกล้อง...</p>
					</div>
					<div *ngIf="isScanningCompleted()" class="success-overlay"></div>
					<div *ngIf="isScanningCompleted()" class="success-content">
						<div class="circle-green">
							<div class="check-icon"></div>
						</div>
						<p class="success-text">ถ่ายรูปสำเร็จ!</p>
					</div>
					<div *ngIf="currentWebcamStatus() === 'error' || hasError()" class="error-overlay">
						<div class="error-icon">⚠️</div>
						<p class="error-text">เกิดข้อผิดพลาด</p>
						<button class="retry-button" (click)="retryCapture()">ลองใหม่</button>
					</div>
				</div>
			</div>
		</div>

		<app-footer styleClass="!border-none">
			<div class="footer-wrapper !justify-center">
				<p-button
					[label]="buttonLabel()"
					styleClass="w-full"
					class="w-full max-w-md"
					[loading]="isLoading()"
					[disabled]="!isReadyForCapture() || captureMode() === 'auto'"
					(click)="handleTakePhoto()" />
			</div>
		</app-footer>
	</div>
</ion-content>
