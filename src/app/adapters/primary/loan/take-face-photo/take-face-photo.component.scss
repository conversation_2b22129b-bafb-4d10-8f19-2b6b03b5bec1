:host {
    @apply flex flex-col h-full w-full;
}

// Header Section
.header-section {
    @apply text-center px-4 mb-2;

    .page-title {
        @apply text-lg font-medium text-gray-900 mb-2;
    }

    .page-subtitle {
        @apply text-sm font-light text-gray-600 mb-4 leading-relaxed;
    }

    .progress-container {
        @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;

        .progress-bar {
            @apply h-full w-full rounded-full transition-all duration-300 ease-out;

            &.progress-success {
                @apply bg-green-500;
            }

            &.progress-default {
                @apply bg-gray-300;
            }
        }
    }
}

// Camera Container
.camera-container {
    @apply relative bg-black rounded-2xl overflow-hidden h-auto;


    .camera-video {
        @apply w-full h-auto object-contain;
        display: block;
    }
}

.mirror-button-container {
    @apply absolute top-4 right-4 z-10;
}

// ID Card SVG Overlay
.id-card-overlay-container {
    @apply absolute inset-0 pointer-events-none;
}

.id-card-svg-overlay {
    @apply absolute pointer-events-none;
    transition: color 0.3s ease-out;
    z-index: 10;

    // Status-based colors using currentColor
    &[data-status="white"],
    &[data-status="idle"] {
        color: white !important; // gray-400
    }

    &[data-status="green"],
    &[data-status="completed"] {
        color: green !important; // emerald-500 - สีเขียวที่เด่นชัด
    }

    &[data-status="error"] {
        color: #EF4444 !important; // red-500
    }

    &[data-status="scanning"] {
        color: #9CA3AF !important; // gray-400
    }

    // Ensure path inherits the color
    path {
        stroke: currentColor !important;
        transition: stroke 0.3s ease-out;
    }
}

// Center guide overlay (separate from SVG)
.center-guide-overlay {
    @apply absolute inset-0 flex items-center justify-center pointer-events-none;
    z-index: 11;

    .guide-text {
        @apply bg-black/70 text-white text-xs px-3 py-1 rounded-full backdrop-blur-sm;
        font-weight: 500;
    }
}

// Loading Overlay
.loading-overlay {
    @apply absolute inset-0 flex flex-col items-center justify-center bg-black/50 backdrop-blur-sm rounded-2xl;

    .loading-spinner {
        @apply text-white mb-4;
        --color: #10B981;
    }

    .loading-text {
        @apply text-white text-sm font-medium;
    }
}

// Success Overlay Background
.success-overlay {
    @apply absolute inset-0 bg-emerald-500/20 backdrop-blur-sm rounded-2xl;
    z-index: 15;
}

// Success Content (icon + text with gap)
.success-content {
    @apply absolute inset-0 flex flex-col items-center justify-center gap-4;
    z-index: 16;

    .circle-green {
        width: 40px;
        height: 40px;
        background-color: #47cf5d;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .check-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -55%) scaleX(-1) rotate(135deg);
        transform-origin: center;
        border-right: 2px solid #fff;
        border-top: 2px solid #fff;
        width: 9px;
        height: 18px;
        animation: check-icon 1.2s ease-in-out;
    }

    .success-text {
        @apply text-white font-semibold text-lg;
        color: white !important;
    }
}

// Error Overlay
.error-overlay {
    @apply absolute inset-0 flex flex-col items-center justify-center bg-red-500/20 backdrop-blur-sm rounded-2xl;

    .error-icon {
        @apply text-4xl mb-4;
    }

    .error-text {
        @apply text-red-600 font-semibold text-lg mb-4;
    }

    .retry-button {
        @apply bg-red-500 text-white px-6 py-2 rounded-lg font-medium;
        @apply hover:bg-red-600 transition-colors duration-200;
    }
}



// Check icon animation
@keyframes check-icon {
    0% {
        height: 0;
        width: 0;
        opacity: 1;
    }

    20% {
        height: 0;
        width: 9px;
        opacity: 1;
    }

    40% {
        height: 18px;
        width: 9px;
        opacity: 1;
    }

    100% {
        height: 18px;
        width: 9px;
        opacity: 1;
    }
}

.instruction {
    color: #000;
    font-weight: 500;
    margin-bottom: 1rem;
    animation:
        fadeIn 0.3s ease-in-out,
        slideUp 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

.overlay-modal {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    /* สูงกว่ากล้อง */
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.85);
    /* หรือ transparent ถ้า modal มีพื้นหลังเอง */
}