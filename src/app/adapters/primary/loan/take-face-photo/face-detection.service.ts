import { computed, inject, Injectable, signal } from "@angular/core";
import { BoundingBox, Category, Detection } from "@mediapipe/tasks-vision";
import { MediaPipeService } from "src/app/core/application/media-pipe.service";
import { ImageProcessorUtils } from "src/app/core/utils/image-processor-utils";
import { Resolution } from "ts-webcam";
import { WebcamService } from "../../shared/services/webcam.service";

export enum FaceScanStatus {
	Success = "success",
	Failed = "failed",
	Error = "error",
	NoImageDetected = "no-image-detected",
	DocumentNotFound = "document-not-found",
	LowConfidence = "low-confidence",
	InvalidDocument = "invalid-document",
	InvalidFormat = "invalid-format",
	InvalidType = "invalid-type",
	InvalidSize = "invalid-size",
}

export interface FaceDetectionState {
	// ใบหน้าได้ต้อง ไม่
	isFaceDetected: boolean;
	// ใบหน้าตรง ไม่
	isFacingForward: boolean;
	// ขนาดใบหน้า
	sizeStatus: FaceSizeStatus;
	// จมูกอยู่ในตำแหน่ง กำหนดไว้ ไม่
	isNoseInTarget: boolean;
	// ข้อความแนะนำ ไม่
	hasInstruction: boolean;
	// ข้อความแนะนำ
	instruction: string | null;
	// ภาพใบหน้าตรง ถ่ายไว้
	frameWithoutBlinking: FaceDetectionResult | null;
	// detection details
	detectionActions: DetectionAction[];
	currentDetectionIndex: number;
	processingProgress: number;
	isSuccessful: boolean;
	isFacePoseProcessing: boolean;
	isLoading: boolean;
}

export type DetectionAction = keyof typeof Detections;

export enum ActionsType {
	StraightFace = "straight-face",
	// Blink = 'blink',
}

export const Detections: Record<ActionsType, { instruction: string; minProbability?: number }> = {
	[ActionsType.StraightFace]: {
		instruction: "ถ่ายใบหน้าตรง",
	},
	// [ActionsType.Blink]: {
	//   instruction: 'กระพับตาช้าๆ',
	//   minProbability: 0.4,
	// },
};

export const detectionsList: DetectionAction[] = [
	ActionsType.StraightFace,
	//  ActionsType.Blink
];

export interface FaceDetectionResult {
	// Basic information
	timestamp: string;
	processingTimeMs: number;
	status: FaceScanStatus;

	// Face Detection Result
	faceData: FaceDetectionData | null;

	// Processed Images
	images: {
		original: string;
		cropped: string | null;
	};

	// Errors (if any)
	errors?: string[];
}

export interface FaceDetectionData {
	sizeStatus: FaceSizeStatus;
	blinkStatus: EyesStatus;
	eyesVisibility: EyesStatus;
	headPose: HeadPose;
	isNoseInTarget: boolean;
	isSizeInRange: boolean;
	lookDirection: string;
	forwardSuggestion: string;
	isFaceDetected: boolean;
	isFacingForward: boolean;
	processingTimeMs: number;
}

export enum FaceSizeStatus {
	Small = "small",
	Normal = "normal",
	Large = "large",
	None = "none",
}

export interface EyesStatus {
	right: boolean;
	left: boolean;
}

export interface HeadPose {
	pitch: number;
	yaw: number;
	roll: number;
}

export interface Vector {
	x: number;
	y: number;
	z: number;
}

export interface InverseScaleFactor {
	scaleFactor: number;
	inverseScaleFactor: number;
	scaledWidth: number;
	scaledHeight: number;
}

export interface NosePosition {
	x: { min: number; max: number };
	y: { min: number; max: number };
}

export interface FaceBoundingBox {
	minWidth: number;
	maxWidth: number;
	minHeight: number;
	maxHeight: number;
}

export const FACE_DETECTION_CONFIG = {
	// eyes blink
	minProbability: 0.5,

	// face rectangle size
	maxHeightRatio: 0.8 * 0.8,
	minHeightRatio: 0.6 * 0.8,
	minWidthRatioLandscape: 0.5,
	maxWidthRatioLandscape: 1,
	maxWidthRatioPortrait: 0.95,
	minWidthRatioPortrait: 0.5,
	maxWidthRatioSquare: 0.95,
	minWidthRatioSquare: 0.25,

	// current look direction
	lookDirection: {
		// ระยะการก้ม/เงยหน้า
		pitchThreshold: 30,
		// ระยะการซ้าย/ขวา
		yewThreshold: 30,
	},

	// head pose suggestions for facing forward
	headPoseSuggestions: {
		// ตรวจสอบแนวตั้ง
		pitchThreshold: 30,
		// ตรวจสอบแนวนอน
		yewThreshold: 30,
	},

	// normalized bounding box
	normalizedBoundingBox: {
		x: { min: 0.35, max: 0.6 },
		y: { min: 0.45, max: 0.7 },
	},
};

export enum InstructionsType {
	/**
	 * ไม่พบใบหน้า
	 */
	NoFace = "no-face",
	/**
	 * คำสั่งเริ่มต้น
	 */
	Initial = "initial",
	/**
	 * ปรับตำแหน่งศีรษะ
	 */
	AdjustHead = "adjust-head",
	/**
	 * ใบหน้าใกล้เกินไป
	 */
	TooClose = "too-close",
	/**
	 * ใบหน้าไกลเกินไป
	 */
	TooFar = "too-far",
	/**
	 * มีสิ่งกีดขวาง
	 */
	Obstruction = "obstruction",
	/**
	 * กำลังตรวจสอบใบหน้า
	 */
	Detection = "detection",
	/**
	 * รอสักครู่
	 */
	HoldOn = "hold-on",
	/**
	 * กำลังประมวลผล
	 */
	Loading = "loading",
	/**
	 * ประมวลผลสำเร็จ
	 */
	Success = "success",
}

export const instructionsText: Partial<Record<InstructionsType, string>> = {
	[InstructionsType.NoFace]: "ไม่พบใบหน้า",
	[InstructionsType.Initial]: "วางใบหน้าของคุณในวงกลม",
	[InstructionsType.HoldOn]: "กรุณาอยู่นิ่งๆ",
	[InstructionsType.TooClose]: "ใบหน้าใกล้เกินไป",
	[InstructionsType.TooFar]: "ใบหน้าไกลเกินไป",
	[InstructionsType.Loading]: "กำลังประมวลผล โปรดรอสักครู่",
	[InstructionsType.Obstruction]: "กรุณานำสิ่งกีดขวาง ออกจากใบหน้า",
	[InstructionsType.Success]: "ประมวลผลสำเร็จ",
};

@Injectable({
	providedIn: "root",
})
export class FaceDetectionService {
	// Services
	private webcamService = inject(WebcamService);
	private mediaPipeService = inject(MediaPipeService);

	// Define the initial state with strong typing
	private readonly initialState: FaceDetectionState = {
		isFaceDetected: false,
		isFacingForward: false,
		sizeStatus: FaceSizeStatus.None,
		isNoseInTarget: false,
		hasInstruction: false,
		instruction: "",
		frameWithoutBlinking: null,
		detectionActions: detectionsList,
		currentDetectionIndex: 0,
		processingProgress: 0,
		isSuccessful: false,
		isFacePoseProcessing: false,
		isLoading: false,
	};

	// Define the default face detection data with strong typing
	private readonly defaultFaceDetectionData: FaceDetectionData = {
		sizeStatus: FaceSizeStatus.None,
		blinkStatus: { left: false, right: false },
		eyesVisibility: { left: false, right: false },
		headPose: { pitch: 0, yaw: 0, roll: 0 },
		isNoseInTarget: false,
		isSizeInRange: false,
		lookDirection: "",
		forwardSuggestion: "",
		isFaceDetected: false,
		isFacingForward: false,
		processingTimeMs: 0,
	};

	// Reactive state with signals
	private faceImageState = signal<string | null>(null);
	private _isProcessing = signal<boolean>(false);
	private _frameCount = signal<number>(0);
	private _targetInverseScaleFactor = signal<InverseScaleFactor | null>(null);
	private _targetNosePosition = signal<NosePosition | null>(null);
	private _targetFaceBoundingBox = signal<FaceBoundingBox | null>(null);
	private _faceDetectionResult = signal<FaceDetectionResult | null>(null);
	private _faceDetectionState = signal<FaceDetectionState>(this.initialState);
	private _captureStartTime = signal<number | null>(null);
	private _capturedFrames = signal<FaceDetectionResult[]>([]);

	// Computed properties
	public faceDetectionState = computed(() => this._faceDetectionState());
	public instructionsWording = computed(() => instructionsText);
	public detections = computed(() => Detections);

	// Callbacks
	private handleSuccessfulScan: ((result?: FaceDetectionResult) => Promise<void>) | null = null;
	private handleFaceDetectionResult: ((ocrResult: FaceDetectionResult) => void) | null = null;

	constructor() {}

	setFaceImage(image: string | null): void {
		this.faceImageState.set(image);
	}

	getFaceImageUrl(): string | null {
		return this.faceImageState();
	}

	async startProcessing(
		handleSuccessfulScan: (result?: FaceDetectionResult) => Promise<void>,
		handleFaceDetectionResult?: (result: FaceDetectionResult) => void,
	): Promise<void> {
		if (this._isProcessing()) {
			return;
		}

		try {
			// Setup configuration
			const currentResolution = this.webcamService.instance.getCurrentResolution();
			await this.setupFaceDetection(currentResolution);

			// update handle success function
			this.handleSuccessfulScan = handleSuccessfulScan;
			this.handleFaceDetectionResult = handleFaceDetectionResult || null;

			// Start processing
			this._isProcessing.set(true);

			// Start processing
			await this.runProcessing();
		} catch (error) {
			throw error;
		}
	}

	async stopProcessing(): Promise<void> {
		// clear all results
		this._isProcessing.set(false);
		this._faceDetectionState.set(this.initialState);
		this._frameCount.set(0);
	}

	private async setupFaceDetection(activeResolution: Resolution | null): Promise<void> {
		if (!activeResolution) {
			throw new Error("Active resolution is null");
		}

		const { height, width } = activeResolution;
		if (!height || !width) {
			throw new Error("Invalid resolution properties");
		}

		try {
			return await new Promise<void>((resolve, reject) => {
				try {
					// Setup target inverse scale factor
					const scaleFactor = 0.5;
					const inverseScaleFactor = this.calculateInverseScaleFactor(
						width,
						height,
						scaleFactor, // 50%
					);

					this._targetInverseScaleFactor.set(inverseScaleFactor);

					const nosePosition = this.getNormalizedBoundingBox();
					if (!nosePosition) {
						throw new Error("Target nose position is null");
					}

					this._targetNosePosition.set(nosePosition);

					const faceBoundingBox = this.calculateFaceRectangular(height, width);
					if (!faceBoundingBox) {
						throw new Error("Target face rectangle is null");
					}

					this._targetFaceBoundingBox.set(faceBoundingBox);

					resolve();
				} catch (error) {
					reject(error);
				}
			});
		} catch (error) {
			throw new Error(`Error setting up face detection: ${error}`);
		}
	}

	public getNormalizedBoundingBox(): NosePosition {
		return {
			x: FACE_DETECTION_CONFIG.normalizedBoundingBox.x,
			y: FACE_DETECTION_CONFIG.normalizedBoundingBox.y,
		};
	}

	private calculateInverseScaleFactor(
		originalWidth: number,
		originalHeight: number,
		scaleFactor: number,
	): {
		scaleFactor: number;
		inverseScaleFactor: number;
		scaledWidth: number;
		scaledHeight: number;
	} {
		if (scaleFactor === 0) {
			throw new Error("Scale factor cannot be zero.");
		}

		const scaledWidth = originalWidth * scaleFactor;
		const scaledHeight = originalHeight * scaleFactor;
		const inverseScaleFactor = 1 / scaleFactor;

		return {
			scaleFactor,
			inverseScaleFactor,
			scaledWidth,
			scaledHeight,
		};
	}

	public calculateFaceRectangular(
		// aspectRatio: AspectRatioType,
		imageHeight: number,
		imageWidth: number = 0,
	): FaceBoundingBox {
		// let calculatedRectangle: FaceBoundingBox;
		// switch (aspectRatio) {
		//     case AspectRatioType.Landscape:
		//         calculatedRectangle = this.calculateFaceRectangularInLandscape(imageHeight);
		//         break;
		//     case AspectRatioType.Portrait:
		//         calculatedRectangle = this.calculateFaceRectangularInPortrait(imageHeight, imageWidth);
		//         break;
		//     case AspectRatioType.Square:
		//         calculatedRectangle = this.calculateFaceRectangularInSquare(imageHeight);
		//         break;
		//     default:
		//         throw new Error('Invalid orientation type');
		// }
		// return calculatedRectangle;

		const maxHeight = Math.floor(imageHeight * FACE_DETECTION_CONFIG.maxHeightRatio);
		const minHeight = Math.floor(imageHeight * FACE_DETECTION_CONFIG.minHeightRatio);
		const maxWidth = Math.floor(imageHeight * FACE_DETECTION_CONFIG.maxWidthRatioSquare);
		const minWidth = Math.floor(imageHeight * FACE_DETECTION_CONFIG.minWidthRatioSquare);
		return { maxHeight, minHeight, maxWidth, minWidth };
	}

	//#region Run Process MediaPipe
	private async runProcessing(): Promise<void> {
		if (!this._isProcessing()) {
			return; // exit function
		}

		try {
			// Increment frame count
			this._frameCount.update((count) => count + 1);

			// Check frame interval
			if (!this.isFrameIntervalMet()) {
				return await this.scheduleNextFrame();
			}

			// Capture and validate frame
			const frame = await this.captureFrame();
			// Validate frame uri
			if (!frame) {
				return await this.scheduleNextFrame();
			}

			// Process frame
			const result = await this.processNextFrame(frame, performance.now());
			this._faceDetectionResult.set(result);
			console.log("Result:", result);

			// Call OCR result handler for real-time feedback (both success and failure)
			if (this.handleFaceDetectionResult && result) {
				this.handleFaceDetectionResult(result);
			}

			// Check outage conditions
			if (await this.shouldStopProcessing(result)) {
				return; // exit function
			}

			// Handle detected face and current action
			if (!this.handleFaceDetectionMediaPipe(result)) {
				return await this.scheduleNextFrame();
			}

			// Handle current action
			await this.handleCurrentAction(result);
			return await this.scheduleNextFrame();
		} catch (error) {
			console.error("Error:", error);
			return await this.scheduleNextFrame();
		}
	}

	/**
	 * Check if the frame interval is met.
	 */
	private isFrameIntervalMet(): boolean {
		return this._frameCount() % 5 === 0;
	}

	/**
	 * Check if the frame is valid.
	 */
	private isFrameValid(frame: string | null): boolean {
		if (!frame || frame === null) {
			console.warn("Invalid or missing frame");
			return false;
		}
		return true;
	}

	/**
	 * Capture frame from webcam
	 */
	private async captureFrame(): Promise<string | null> {
		try {
			return await this.webcamService.takePhoto({
				imageType: "image/jpeg",
				quality: 0.92,
				scale: 1,
			});
		} catch (error) {
			console.error("Error capturing frame:", error);
			return null;
		}
	}

	/**
	 * Schedules the next frame for processing, given the current stage.
	 */
	private async scheduleNextFrame(): Promise<void> {
		if (!this._isProcessing()) {
			return;
		}

		await this.runProcessing();
	}

	private async processNextFrame(
		imageDataUrl: string,
		startTime: number,
	): Promise<FaceDetectionResult> {
		try {
			// Process face image
			const scale = this._targetInverseScaleFactor()?.scaleFactor || 1;
			const optimizedImage = await ImageProcessorUtils.optimizeImage(imageDataUrl, scale);
			if (!optimizedImage) {
				return this.createEmptyResult(
					startTime,
					FaceScanStatus.NoImageDetected,
					imageDataUrl,
					null,
				);
			}

			// Process with MediaPipe in parallel
			const [faceDetection, faceLandmarks] = await Promise.all([
				this.mediaPipeService.detectFaces(optimizedImage),
				this.mediaPipeService.detectLandmarks(optimizedImage),
			]);

			// ถ้าไม่ตรวจใบหน้า
			if (faceDetection.length === 0 || !faceLandmarks) {
				return this.createResult(startTime, FaceScanStatus.Success, imageDataUrl, {
					...this.defaultFaceDetectionData,
					processingTimeMs: this.calculateProcessingTime(startTime),
				});
			}

			const firstFace = faceDetection[0];
			const boundingBox = firstFace.boundingBox;
			const faceLandmarksData = faceLandmarks.faceLandmarks[0];
			if (!this._targetNosePosition() || !this._targetFaceBoundingBox() || !boundingBox) {
				return this.createResult(startTime, FaceScanStatus.Success, imageDataUrl, {
					...this.defaultFaceDetectionData,
					processingTimeMs: this.calculateProcessingTime(startTime),
				});
			}

			// Calculate inverse scale factor
			const inverseScaleFactor = this._targetInverseScaleFactor()?.inverseScaleFactor || 1;

			// Check about eye
			const faceBlendshapes = faceLandmarks.faceBlendshapes[0]?.categories;
			const eyesBlink = faceBlendshapes
				? this.detectEyeBlink(faceBlendshapes, inverseScaleFactor)
				: { left: false, right: false };
			const eyesVisible = faceBlendshapes
				? this.detectEyesVisibility(faceBlendshapes)
				: { left: false, right: false };

			// Check is correct target
			const isNoseInTargetArea = faceBlendshapes
				? this.isNoseInTargetArea(firstFace, this._targetNosePosition())
				: false;
			const isFaceSizeInRange = this.isFaceSizeInRange(
				firstFace,
				this._targetFaceBoundingBox(),
				inverseScaleFactor,
			);

			// Check face size
			const faceSize = this.evaluateFaceSize(
				boundingBox,
				this._targetFaceBoundingBox(),
				inverseScaleFactor,
			);

			// Calculate about head direction
			let adjustedHeadPose = this.estimateHeadPose(faceLandmarksData);
			if (this.webcamService.instance.isMirrorEnabled()) {
				adjustedHeadPose = {
					...adjustedHeadPose,
					yaw: -adjustedHeadPose.yaw, // ค่าซ้าย-ขวา
				};
			}
			const faceForwardSuggestion = this.getHeadPoseSuggestions(adjustedHeadPose);
			const lookDirection = this.getLookDirection(adjustedHeadPose);

			// Create scan result
			const faceDetectionData: FaceDetectionData = {
				sizeStatus: faceSize,
				blinkStatus: eyesBlink,
				eyesVisibility: eyesVisible,
				headPose: adjustedHeadPose,
				isNoseInTarget: isNoseInTargetArea,
				isSizeInRange: isFaceSizeInRange,
				lookDirection: lookDirection,
				forwardSuggestion: faceForwardSuggestion,
				isFaceDetected: true,
				isFacingForward: faceForwardSuggestion === "หน้าตรง",
				processingTimeMs: this.calculateProcessingTime(startTime),
			};

			return this.createResult(startTime, FaceScanStatus.Success, imageDataUrl, faceDetectionData);
		} catch (error) {
			console.error("Processing error:", error);
			return this.createEmptyResult(startTime, FaceScanStatus.Error, imageDataUrl, null);
		}
	}

	//# region Face Detection
	public detectEyeBlink(faceBlendshapes: Category[], inverseScaleFactor: number = 1): EyesStatus {
		const rightScore =
			faceBlendshapes?.find((shape) => shape.categoryName === "eyeBlinkRight")?.score ?? 0;
		const leftScore =
			faceBlendshapes?.find((shape) => shape.categoryName === "eyeBlinkLeft")?.score ?? 0;

		const scaleRightScore = rightScore * inverseScaleFactor;
		const scaleLeftScore = leftScore * inverseScaleFactor;

		return {
			right: scaleRightScore > FACE_DETECTION_CONFIG.minProbability,
			left: scaleLeftScore > FACE_DETECTION_CONFIG.minProbability,
		};
	}

	public detectEyesVisibility(faceBlendshapes: Category[] | undefined): EyesStatus {
		const rightEyeVisible =
			faceBlendshapes?.some(
				(shape) => shape.categoryName === "eyeBlinkRight" && shape.score !== undefined,
			) ?? false;
		const leftEyeVisible =
			faceBlendshapes?.some(
				(shape) => shape.categoryName === "eyeBlinkLeft" && shape.score !== undefined,
			) ?? false;

		return {
			right: rightEyeVisible,
			left: leftEyeVisible,
		};
	}

	public isNoseInTargetArea(
		detection: Detection,
		targetNosePosition: NosePosition | null,
		scale: number = 1,
	): boolean {
		if (!targetNosePosition) {
			return false;
		}

		// ตรวจสอบว่าจมูกอยู่ในช่วงกําหนดไหม
		const { keypoints } = detection;
		if (keypoints.length < 3) {
			return false;
		}

		const nose = {
			x: keypoints[2].x * scale,
			y: keypoints[2].y * scale,
		};

		return (
			nose.x > targetNosePosition.x.min &&
			nose.x < targetNosePosition.x.max &&
			nose.y > targetNosePosition.y.min &&
			nose.y < targetNosePosition.y.max
		);
	}

	public isFaceSizeInRange(
		detection: Detection,
		targetFaceRectangle: FaceBoundingBox | null,
		inverseScaleFactor: number = 1,
	): boolean {
		if (!targetFaceRectangle) {
			return false;
		}

		// ตรวจสอบว่าขนาดใบหน้าอยู่ในช่วงกําหนดไหม
		const { boundingBox } = detection;
		const { width, height } = boundingBox || {};
		if (!width || !height) {
			return false;
		}

		// Inverse scale factor
		const scaledWidth = width * inverseScaleFactor;
		const scaledHeight = height * inverseScaleFactor;

		return (
			scaledWidth > targetFaceRectangle.minWidth &&
			scaledWidth < targetFaceRectangle.maxWidth &&
			scaledHeight > targetFaceRectangle.minHeight &&
			scaledHeight < targetFaceRectangle.maxHeight
		);
	}

	public evaluateFaceSize(
		boundingBox: BoundingBox,
		targetFaceRectangle: FaceBoundingBox | null,
		inverseScaleFactor: number = 1,
	): FaceSizeStatus {
		// ตรวจสอบขนาดใบหน้า
		if (!boundingBox || !targetFaceRectangle) {
			return FaceSizeStatus.None;
		}

		// Inverse scale factor
		const scaledFaceWidth = boundingBox.width * inverseScaleFactor;
		const scaledFaceHeight = boundingBox.height * inverseScaleFactor;

		if (
			scaledFaceWidth < targetFaceRectangle.minWidth ||
			scaledFaceHeight < targetFaceRectangle.minHeight
		) {
			return FaceSizeStatus.Small;
		} else if (
			scaledFaceWidth > targetFaceRectangle.maxWidth ||
			scaledFaceHeight > targetFaceRectangle.maxHeight
		) {
			return FaceSizeStatus.Large;
		} else {
			return FaceSizeStatus.Normal;
		}
	}

	public estimateHeadPose(
		keyPoints: Array<{
			x: number;
			y: number;
			z: number;
			visibility: number;
		}>,
		scale: number = 1, // Default to 1 (no scaling)
	): HeadPose {
		if (!keyPoints || keyPoints.length < 468) {
			console.warn("Invalid keypoints provided");
			return { pitch: 0, yaw: 0, roll: 0 };
		}

		// Apply scaling to all keypoints
		const scaledKeyPoints = keyPoints.map((point) => ({
			x: point.x * scale,
			y: point.y * scale,
			z: point.z * scale,
			visibility: point.visibility, // Preserve visibility
		}));

		const leftEye = scaledKeyPoints[33];
		const rightEye = scaledKeyPoints[263];
		const leftMouth = scaledKeyPoints[61];
		const rightMouth = scaledKeyPoints[291];
		const forehead = scaledKeyPoints[10];
		const chin = scaledKeyPoints[152];

		// Calculate face normal using cross product of two face plane vectors
		const faceVector1 = this.subtractPoints(rightEye, leftEye);
		const faceVector2 = this.subtractPoints(chin, forehead);
		const faceNormal = this.crossProduct(faceVector1, faceVector2);

		// Calculate mouth vector for roll
		const mouthVector = this.subtractPoints(rightMouth, leftMouth);

		// Normalize vectors
		const normalizedFaceNormal = this.normalizeVector(faceNormal);
		const normalizedMouthVector = this.normalizeVector(mouthVector);

		// Calculate pitch (rotation around x-axis)
		const pitch = Math.atan2(faceNormal.y, faceNormal.z);

		// Calculate yaw (rotation around y-axis)
		const yaw = Math.atan2(
			-normalizedFaceNormal.x,
			Math.sqrt(
				normalizedFaceNormal.y * normalizedFaceNormal.y +
					normalizedFaceNormal.z * normalizedFaceNormal.z,
			),
		);

		// Calculate roll (rotation around z-axis)
		const roll = Math.atan2(normalizedMouthVector.y, normalizedMouthVector.x);

		// Convert to degrees and adjust ranges
		const pitchRadians = this.radiansToDegrees(pitch);
		const yawRadians = this.radiansToDegrees(yaw);
		const rollRadians = this.radiansToDegrees(roll);

		const pitchDegrees = this.normalizeDegrees(pitchRadians);
		const yawDegrees = this.normalizeDegrees(yawRadians);
		const rollDegrees = this.normalizeDegrees(rollRadians);

		return {
			pitch: pitchDegrees,
			yaw: yawDegrees,
			roll: rollDegrees,
		};
	}

	public getHeadPoseSuggestions(headPose: HeadPose): string {
		let verticalDirection = "";
		let horizontalDirection = "";

		// ตรวจสอบแนวตั้ง
		if (headPose.pitch > FACE_DETECTION_CONFIG.headPoseSuggestions.pitchThreshold) {
			verticalDirection = "ก้มหน้าเล็กน้อย";
		} else if (headPose.pitch < -FACE_DETECTION_CONFIG.headPoseSuggestions.pitchThreshold) {
			verticalDirection = "เงยหน้าเล็กน้อย";
		}

		// ตรวจสอบแนวนอน
		if (headPose.yaw > FACE_DETECTION_CONFIG.headPoseSuggestions.yewThreshold) {
			horizontalDirection = "ขวาเล็กน้อย";
		} else if (headPose.yaw < -FACE_DETECTION_CONFIG.headPoseSuggestions.yewThreshold) {
			horizontalDirection = "ซ้ายเล็กน้อย";
		}

		// สร้างข้อความแนะนำ
		if (verticalDirection && horizontalDirection) {
			return `${verticalDirection} และ ${horizontalDirection}`;
		} else if (verticalDirection) {
			return verticalDirection;
		} else if (horizontalDirection) {
			return horizontalDirection;
		} else {
			return "หน้าตรง";
		}
	}

	public getLookDirection(headPose: HeadPose): string {
		let vertical = "";
		let horizontal = "";

		if (headPose.pitch > FACE_DETECTION_CONFIG.lookDirection.pitchThreshold) vertical = "Up";
		else if (headPose.pitch < -FACE_DETECTION_CONFIG.lookDirection.pitchThreshold)
			vertical = "Down";

		// เปลี่ยนเงื่อนไขการตรวจสอบ ทางซ้าย-ขวา
		if (headPose.yaw > FACE_DETECTION_CONFIG.lookDirection.yewThreshold)
			horizontal = "Right"; // เปลี่ยนเป็น Right
		else if (headPose.yaw < -FACE_DETECTION_CONFIG.lookDirection.yewThreshold) horizontal = "Left"; // เปลี่ยนเป็น Left

		if (vertical && horizontal) {
			return `${vertical}-${horizontal}`;
		} else {
			return vertical || horizontal || "Forward";
		}
	}
	//#endregion

	//region HeadPose Calculation
	private subtractPoints(p1: Vector, p2: Vector): Vector {
		return {
			x: p1.x - p2.x,
			y: p1.y - p2.y,
			z: p1.z - p2.z,
		};
	}

	private crossProduct(v1: Vector, v2: Vector): Vector {
		return {
			x: v1.y * v2.z - v1.z * v2.y,
			y: v1.z * v2.x - v1.x * v2.z,
			z: v1.x * v2.y - v1.y * v2.x,
		};
	}

	private normalizeVector(vector: Vector): Vector {
		const magnitude = Math.sqrt(vector.x ** 2 + vector.y ** 2 + vector.z ** 2);
		return {
			x: vector.x / magnitude,
			y: vector.y / magnitude,
			z: vector.z / magnitude,
		};
	}

	private normalizeDegrees(degrees: number): number {
		return ((degrees + 180) % 360) - 180;
	}

	private radiansToDegrees(radians: number): number {
		return radians * (180 / Math.PI);
	}
	//#endregion

	//region Face Detection State And Handling

	private async shouldStopProcessing(result: FaceDetectionResult): Promise<boolean> {
		if (
			this._faceDetectionState()?.isSuccessful &&
			this._faceDetectionState()?.frameWithoutBlinking
		) {
			console.log("Processing complete. Stopping...");
			this._isProcessing.set(false);
			// Trigger the callback once processing is complete
			if (this.handleSuccessfulScan) {
				console.log("Processing complete. Triggering callback...");
				await this.handleSuccessfulScan(result);
			}
			return true;
		}
		return false;
	}

	private async handleFaceDetectionMediaPipe(result: FaceDetectionResult): Promise<boolean> {
		// If face is not detected, update state and return early
		if (!result.faceData?.isFaceDetected) {
			this.updateIsFaceDetected(false);
			this.resetFrameWithoutBlinking();
			return false;
		}

		// Check about face size
		if (result.faceData.sizeStatus !== FaceSizeStatus.Normal) {
			this.resetFrameWithoutBlinking();
		}

		// Update face size status
		this.updateFaceSize(result.faceData.sizeStatus);

		// Check about nose in target area
		if (result.faceData.isNoseInTarget === false) {
			this.resetFrameWithoutBlinking();
		}

		// Update is nose in target
		this.updateIsNoseInTarget(result.faceData.isNoseInTarget);

		// Handle face forward suggestion
		const faceForwardSuggestion = result.faceData.forwardSuggestion;
		if (faceForwardSuggestion !== "หน้าตรง") {
			this.updateIsFacingForward(false);
			this.updateHasInstruction(true);
			this.updateInstruction(faceForwardSuggestion);
			this.resetFrameWithoutBlinking();
			return false;
		}

		// Face is forward; reset related states
		this.updateIsFacingForward(true);
		this.updateHasInstruction(false);
		this.resetInstruction();
		this.updateIsFaceDetected(true);

		return true; // Face detection is successful
	}

	private async handleCurrentAction(result: FaceDetectionResult): Promise<void> {
		const state = this._faceDetectionState();
		const currentAction = state.detectionActions[state.currentDetectionIndex];
		switch (currentAction) {
			case "straight-face":
				await this.handleFaceForwardAction(state);
				break;
			default:
				console.warn("Unknown action:", currentAction);
				break;
		}
	}

	private async handleFaceForwardAction(faceDetectionState: FaceDetectionState): Promise<void> {
		if (
			faceDetectionState.frameWithoutBlinking === null &&
			this.isFaceInOptimalState(faceDetectionState) &&
			this._faceDetectionResult()?.faceData?.blinkStatus?.left === false &&
			this._faceDetectionResult()?.faceData?.blinkStatus?.right === false
		) {
			if (this._captureStartTime() === null) {
				this._captureStartTime.set(performance.now());
				this._capturedFrames.set([]);
				this.resetFrameWithoutBlinking();
				this.updateIsFacePoseProcessing(true);
			}

			const elapsedTime = performance.now() - (this._captureStartTime() ?? 0);
			if (elapsedTime <= 3000) {
				if (this._faceDetectionResult() !== null) {
					this._capturedFrames.update((frames) => {
						frames.push(this._faceDetectionResult()!);
						return frames;
					});
				}
				return await this.scheduleNextFrame();
			} else {
				if (this._capturedFrames().length > 0) {
					this.updateIsLoading(true);
					const lastFrame = this._capturedFrames()[this._capturedFrames().length - 1];
					if (this._faceDetectionResult() !== null) {
						this.updateFrameWithoutBlinking(lastFrame);
					} else {
						this.resetFrameWithoutBlinking();
					}

					console.log("Captured frames:", this._capturedFrames());
					console.log("Last frame:", lastFrame);
					console.log("Frame without blinking:", this._faceDetectionState().frameWithoutBlinking);
					console.log("Processing complete. Triggering callback...");
					this.advanceToNextDetection();
					this._isProcessing.set(true);
					this.updateIsLoading(false);
				}

				// Reset capture state
				this._captureStartTime.set(null);
				this._capturedFrames.set([]);
				this.updateIsFacePoseProcessing(false);
			}
		} else {
			// Reset capture state
			this._captureStartTime.set(null);
			this._capturedFrames.set([]);
			this.updateIsFacePoseProcessing(false);
		}
	}

	/**
	 * Check if the face is in the optimal state
	 */
	public isFaceInOptimalState(faceDetectionState: FaceDetectionState): boolean {
		return (
			faceDetectionState.isFaceDetected &&
			faceDetectionState.isNoseInTarget &&
			!faceDetectionState.hasInstruction &&
			faceDetectionState.sizeStatus === FaceSizeStatus.Normal
		);
	}

	/**
	 * Update is face detected
	 */
	public updateIsFaceDetected(isFaceDetected: boolean): void {
		const currentState = this._faceDetectionState(); // Get the current state
		let updatedState: FaceDetectionState;

		if (isFaceDetected) {
			updatedState = {
				...currentState, // Override with current values
				isFaceDetected: true, // Explicitly set this property
				processingProgress: currentState.isFaceDetected
					? currentState.processingProgress
					: 100 / (currentState.detectionActions.length + 1),
			};
		} else {
			updatedState = {
				...this.initialState, // Reset to the initial state
				currentDetectionIndex: currentState?.frameWithoutBlinking ? 1 : 0,
				frameWithoutBlinking: currentState?.frameWithoutBlinking,
			};
		}

		this._faceDetectionState.set(updatedState); // Update the state signal
	}

	/**
	 * Update frame without blinking
	 */
	public updateFrameWithoutBlinking(frame: FaceDetectionResult): void {
		this._faceDetectionState.update((state) => ({
			...state,
			frameWithoutBlinking: frame,
		}));
	}

	/**
	 * Reset frame without blinking
	 */
	public resetFrameWithoutBlinking(): void {
		this._faceDetectionState.update((state) => ({
			...state,
			frameWithoutBlinking: null,
		}));
	}

	/**
	 * Update face size
	 */
	public updateFaceSize(size: FaceSizeStatus): void {
		this._faceDetectionState.update((state) => ({
			...state,
			sizeStatus: size,
		}));
	}

	/**
	 * Update is nose in target
	 */
	public updateIsNoseInTarget(isNoseInTarget: boolean): void {
		this._faceDetectionState.update((state) => ({
			...state,
			isNoseInTarget: isNoseInTarget,
		}));
	}

	/**
	 * Update is facing forward
	 */
	public updateIsFacingForward(isFacingForward: boolean): void {
		this._faceDetectionState.update((state) => ({
			...state,
			isFacingForward: isFacingForward,
		}));
	}

	/**
	 * Update is face pose processing
	 */
	public updateIsFacePoseProcessing(isFacePoseProcessing: boolean): void {
		this._faceDetectionState.update((state) => ({
			...state,
			isFacePoseProcessing: isFacePoseProcessing,
		}));
	}

	/**
	 * Update has instruction
	 */
	public updateHasInstruction(hasInstruction: boolean): void {
		this._faceDetectionState.update((state) => ({
			...state,
			hasInstruction: hasInstruction,
		}));
	}

	/**
	 * Update instruction
	 */
	public updateInstruction(instructions: string): void {
		this._faceDetectionState.update((state) => ({
			...state,
			instruction: instructions,
		}));
	}

	/**
	 * Reset instruction
	 */
	public resetInstruction(): void {
		this._faceDetectionState.update((state) => ({
			...state,
			instruction: "",
		}));
	}

	/**
	 * Update is loading
	 */
	public updateIsLoading(isLoading: boolean): void {
		this._faceDetectionState.update((state) => ({
			...state,
			isLoading: isLoading,
		}));
	}

	/**
	 * Reset face detection state
	 */
	public resetFaceDetectionState(): void {
		this._faceDetectionState.set(this.initialState);
	}

	public advanceToNextDetection(): void {
		const { currentDetectionIndex } = this._faceDetectionState();
		const newIndex = currentDetectionIndex + 1;
		const newProgress = (100 / (detectionsList.length + 1)) * (newIndex + 1);
		const isComplete = newIndex === detectionsList.length;

		this._faceDetectionState.update((state) => ({
			...state,
			currentDetectionIndex: newIndex,
			processingProgress: newProgress,
			isSuccessful: isComplete,
		}));
	}
	//#endregion

	//#region Face Detection Instruction

	/**
	 * Get instruction state
	 */
	public getInstructionState(): InstructionsType {
		const state = this._faceDetectionState();
		if (this.isCompleteOrLoading(state)) {
			return state.isSuccessful ? InstructionsType.Success : InstructionsType.Loading;
		}

		if (this.isNoFace(state)) {
			return InstructionsType.NoFace;
		}

		if (!state.isFacingForward) {
			return this.getStateForNonForwardFace(state);
		}

		return this.getStateForForwardFace(state);
	}

	private isCompleteOrLoading(state: FaceDetectionState): boolean {
		return (state.isSuccessful && state.frameWithoutBlinking !== null) || state.isLoading;
	}

	private isNoFace(state: FaceDetectionState): boolean {
		return (
			!state.isFaceDetected &&
			!state.isFacingForward &&
			!state.isNoseInTarget &&
			state.sizeStatus === FaceSizeStatus.None
		);
	}

	private getStateForNonForwardFace(state: FaceDetectionState): InstructionsType {
		const { sizeStatus, isNoseInTarget, hasInstruction } = state;
		switch (sizeStatus) {
			case FaceSizeStatus.Normal:
				return (isNoseInTarget && hasInstruction) || hasInstruction
					? InstructionsType.AdjustHead
					: InstructionsType.Initial;
			case FaceSizeStatus.Small:
				return isNoseInTarget && hasInstruction
					? InstructionsType.TooFar
					: hasInstruction
						? InstructionsType.AdjustHead
						: InstructionsType.Initial;
			case FaceSizeStatus.Large:
				return (isNoseInTarget && hasInstruction) || hasInstruction
					? InstructionsType.TooClose
					: InstructionsType.Initial;
			default:
				return hasInstruction ? InstructionsType.AdjustHead : InstructionsType.Initial;
		}
	}

	private getStateForForwardFace(state: FaceDetectionState): InstructionsType {
		// Get the state for a null frame
		if (state.frameWithoutBlinking === null) {
			return this.getStateForNullFrame(state);
		}

		// Get the state for a non-null frame
		return this.getStateForNonNullFrame(state);
	}

	private getStateForNullFrame(state: FaceDetectionState): InstructionsType {
		const { sizeStatus, isNoseInTarget, hasInstruction, isFacingForward, isFacePoseProcessing } =
			state;

		// ตรวจสอบจากขนาดใบหน้า
		switch (sizeStatus) {
			// ใบหน้าเล็กไป และใบหน้าใหญ่ไป
			case FaceSizeStatus.Small:
			case FaceSizeStatus.Large:
				return !isNoseInTarget // จมูกอยู่ในตำแหน่งที่ต้องการหรือไม่?
					? InstructionsType.Initial // ยังไม่ตรงตามที่ต้องการ
					: hasInstruction // มีคำแนะนำไหม
						? InstructionsType.AdjustHead // หากมีให้แสดง
						: sizeStatus === FaceSizeStatus.Small // หากไม่มีคำแนะนำ ตรวจสอบขนาดใบหน้า
							? InstructionsType.TooFar // แสดงว่าไกลไป
							: InstructionsType.TooClose; // แสดงว่าใกล้ไป
			case FaceSizeStatus.Normal: // ใบหน้าปกติ
				// ใบหน้ายังไม่ตรงตามที่ต้องการ ให้ปรับทิศทาง
				if (!isFacingForward && !isNoseInTarget) {
					return InstructionsType.AdjustHead;
				}

				// หากใบหน้าตรงตามตำแหน่งที่ต้องการแล้ว และมีสถานะกำลังประมวลผล
				if (isFacePoseProcessing && !hasInstruction && isNoseInTarget) {
					return InstructionsType.HoldOn;
				}

				// คืนค่าเริ่มต้น
				return InstructionsType.Initial;
			default:
				// คืนค่าเริ่มต้น
				return InstructionsType.Initial;
		}
	}

	private getStateForNonNullFrame(state: FaceDetectionState): InstructionsType {
		const { sizeStatus, isNoseInTarget, hasInstruction } = state;

		// ตรวจสอบจากขนาดใบหน้า
		switch (sizeStatus) {
			// ใบหน้าเล็กไป และใบหน้าใหญ่ไป
			case FaceSizeStatus.Small:
			case FaceSizeStatus.Large:
				return !isNoseInTarget // จมูกอยู่ในตำแหน่งที่ต้องการหรือไม่?
					? InstructionsType.Initial // ยังไม่ตรงตามที่ต้องการ
					: hasInstruction // มีคำแนะนำไหม
						? InstructionsType.AdjustHead // หากมีให้แสดง
						: sizeStatus === FaceSizeStatus.Small // หากไม่มีคำแนะนำ ตรวจสอบขนาดใบหน้า
							? InstructionsType.TooFar // แสดงว่าไกลไป
							: InstructionsType.TooClose; // แสดงว่าใกล้ไป
			case FaceSizeStatus.Normal:
				// ตรวจสอบว่าจมูกอยู่ในตำแหน่งที่ต้องการ และไม่มีคำแนะนำในการขยับใบหน้า
				return isNoseInTarget && !hasInstruction
					? InstructionsType.Detection
					: InstructionsType.Initial;
			default:
				// คืนค่าเริ่มต้น
				return InstructionsType.Initial;
		}
	}
	//#endregion

	//region ScanResult
	/**
	 * Create a ScanResult object
	 */
	private createResult(
		startTime: number,
		status: FaceScanStatus,
		imageDataUrl: string,
		result: FaceDetectionData | null,
	): FaceDetectionResult {
		return {
			timestamp: new Date().toISOString(),
			processingTimeMs: performance.now() - startTime,
			status: status,
			faceData: result,
			images: {
				original: imageDataUrl,
				cropped: "",
			},
			errors: [],
		};
	}

	/**
	 * Create empty scan result
	 */
	private createEmptyResult(
		startTime: number,
		status: FaceScanStatus,
		imageDataUrl: string,
		result: FaceDetectionData | null,
		errors: string[] = [],
	): FaceDetectionResult {
		return {
			timestamp: new Date().toISOString(),
			processingTimeMs: performance.now() - startTime,
			status: status,
			faceData: result,
			images: {
				original: imageDataUrl,
				cropped: "",
			},
			errors,
		};
	}
	//endregion

	//region Utility
	/**
	 * Calculate processing time
	 */
	private calculateProcessingTime(startTime: number): number {
		const endTime = performance.now();
		const processingTime = endTime - startTime;
		return processingTime;
	}
	//endregion
}
