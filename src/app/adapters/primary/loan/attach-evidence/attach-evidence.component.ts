import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject, OnInit } from "@angular/core";
import { IonicModule, ModalController } from "@ionic/angular";
import { AccordionModule } from "primeng/accordion";
import { ButtonModule } from "primeng/button";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { AttachDocumentsComponent } from "../../shared/components/attach-documents/attach-documents.component";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { StepCounterComponent } from "../../shared/components/step-counter/step-counter.component";
import { StepCounterService } from "../../shared/services/step-counter.service";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-upload-docs-modal",
	standalone: true,
	imports: [
		CommonModule,
		IonicModule,
		HeaderMiniComponent,
		FooterComponent,
		ButtonModule,
		AccordionModule,
		StepCounterComponent,
	],
	styleUrl: "./attach-evidence.component.scss",
	template: `
		<ion-content>
			<div class="app-layout">
				<!-- Header -->
				<app-header-mini [mode]="'workflow'"></app-header-mini>

				<!-- Content -->
				<div class="app-content">
					<div class="flex h-full flex-row">
						<!-- Step Counter -->
						<div class="flex h-full">
							<app-step-counter [activeStep]="activeStepId"></app-step-counter>
						</div>
						<div class="content-wrapper !max-w-full">
							<!-- Content Spliter -->
							<div class="flex gap-6">
								<div class="panel-container">
									<!-- Consent Title -->
									<div class="section-header">
										<h2 class="section-title-text">
											เอกสารประกอบการพิจารณาสินเชื่อ
										</h2>
									</div>

									<div class="page-container">
										<p-accordion [value]="0" [multiple]="true">
											@for (tab of documentSections; track tab.key) {
												<p-accordion-panel [value]="tab.key">
													<p-accordion-header>
														<div class="text-sm !font-medium">
															{{ tab.title }}
														</div>
														<div
															class="h-[6px] w-[6px] rounded-full text-green-500"
															*ngIf="tab.isCompleted"></div>
														<div
															class="h-[6px] w-[6px] rounded-full text-amber-500"
															*ngIf="!tab.isCompleted"></div>
													</p-accordion-header>
													<p-accordion-content>
														<p class="mb-3 text-sm font-bold text-gray-900">
															{{ tab.title }}
														</p>
														<div class="flex flex-col gap-3">
															<ng-container>
																<div class="flex flex-col gap-2">
																	<ng-container
																		*ngFor="
																			let doc of tab.documentTypes;
																			trackBy: trackByDocumentType
																		">
																		<div class="flex items-center gap-3">
																			<div
																				class="h-[6px] w-[6px] rounded-full"
																				[ngClass]="{
																					'bg-green-500': doc.documentUpload,
																					'bg-gray-400': !doc.documentUpload,
																				}"></div>
																			<span class="text-sm">
																				{{ doc.documentName }}
																			</span>
																		</div>
																	</ng-container>
																</div>
															</ng-container>
														</div>
													</p-accordion-content>
												</p-accordion-panel>
											}
										</p-accordion>
									</div>
								</div>

								<div class="panel-container">
									<!-- Consent Title -->
									<div class="section-header mb-4">
										<h2 class="section-title-text">
											เอกสารที่แนบมา ({{ stats?.totalCount || 0 }} รายการ)
										</h2>
									</div>

									<div class="page-container !gap-2">
										<div class="flex justify-between text-sm font-normal">
											<p>ขนาดไฟล์รวม</p>
											<p>
												<span
													class="text-primary"
													[ngClass]="{
														'text-red-500': (stats?.totalSizeMB || 0) >= 50,
													}">
													{{ stats?.totalSizeMB || 0 }}
												</span>
												/ 50 MB
											</p>
										</div>

										<ng-container
											*ngIf="allDocumentUploaded.length > 0; else noFile">
											<ng-container
												*ngFor="
													let file of allDocumentUploaded;
													trackBy: trackByIndex
												">
												<ng-container
													*ngFor="let item of file; trackBy: trackByDocumentId">
													<div
														class="mb-2 h-14 min-h-fit rounded-lg border border-[#E7E7E7] px-4 py-2">
														<div
															class="flex items-center justify-between gap-4">
															<div class="w-6 flex-shrink-0">
																<img
																	[src]="
																		isImage(item.metadata.mimetype)
																			? 'assets/svgs/img-file.svg'
																			: 'assets/svgs/pdf-file.svg'
																	"
																	class="h-6 w-6 object-contain" />
															</div>
															<div class="min-w-0 flex-1">
																<p class="truncate text-sm">
																	{{ item?.name || "unknow" }}
																</p>
																<p class="mt-1 text-xs text-gray-500">
																	{{ item.documentType }}
																</p>
															</div>
															<div
																class="flex h-full flex-shrink-0 items-center">
																<div class="flex items-center justify-end">
																	<p-button
																		label="ลบ"
																		variant="text"
																		[outlined]="true"
																		(onClick)="removeFile(item.id)"></p-button>
																</div>
															</div>
														</div>
													</div>
												</ng-container>
											</ng-container>
										</ng-container>

										<ng-template #noFile>
											<p class="my-12 text-center text-base text-gray-500">
												ท่านยังไม่ได้แนบเอกสาร
												<br />
												กรุณากดปุ่ม "แนบเอกสาร" เพื่อดำเนินการต่อ
											</p>
										</ng-template>

										<p-button
											label="แนบเอกสาร"
											[fluid]="true"
											[outlined]="true"
											(click)="openAttachDocumentsDialog()" />
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper !justify-between">
						<p-button
							label="ย้อนกลับ"
							[outlined]="true"
							[fluid]="true"
							(onClick)="back()" />
						<p-button
							label="ยืนยันการสมัคร"
							[fluid]="true"
							[loading]="isLoading()"
							(onClick)="handleNext()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class AttachEvidenceComponent
	extends WorkflowStepBaseComponent
	implements OnInit
{
	public stepCounterService = inject(StepCounterService);
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);

	activeStepId: string = "2.4";
	documentSections: any[] = [];
	stats: any | null = null;

	get allDocumentUploaded() {
		return this.documentSections.map((doc) => doc.uploadedDocuments);
	}

	getUploadedDocumentsStats(groups: any[]): any {
		let allDocuments: any[] = [];

		groups.forEach((group) => {
			// รวมเอกสารจาก uploadedDocuments ปกติ
			if (group.uploadedDocuments && Array.isArray(group.uploadedDocuments)) {
				allDocuments = [...allDocuments, ...group.uploadedDocuments];
			}

			// รวมเอกสารจาก key เป็นช่องว่าง
			// if (group[" "] && Array.isArray(group[" "])) {
			//   allDocuments = [...allDocuments, ...group[" "]];
			// }
		});

		// คำนวณขนาดรวมทั้งหมดเป็นไบต์
		const totalSizeBytes = allDocuments.reduce(
			(total, doc) => total + (doc.metadata?.size || 0),
			0,
		);

		// แปลงขนาดเป็นเมกะไบต์ (หารด้วย 1,048,576)
		const totalSizeMB = totalSizeBytes / (1024 * 1024);

		return {
			totalCount: allDocuments.length,
			totalSizeBytes: totalSizeBytes,
			totalSizeMB: Number(totalSizeMB.toFixed(2)),
			documents: allDocuments,
		};
	}

	async ngOnInit() {
		try {
			await this.fetchUploadEvidence();
		} catch (error) {
			await this.handleError(error);
		}
	}

	async fetchUploadEvidence() {
		await this.runWithLoading(async () => {
			try {
				const txnId = this.getTxnIdOrError();
				const response = await this.starmoneyService.getUploadDocuments({
					txn_id: txnId,
				});
				if (response.isSuccess() && response.hasData()) {
					this.mappingDocumentData(response.data);
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	private mappingDocumentData(responseData: any) {
		this.documentSections = [];

		const sections: { [key: string]: string } = {
			required: "เอกสารที่ต้องแนบ",
			group1: "กรุณาแนบเอกสารต่อไปนี้อย่างน้อย 1 อย่าง",
		};

		Object.entries(sections).forEach(([sectionKey, sectionTitle]) => {
			const sectionData = responseData[sectionKey as keyof any];
			if (!sectionData) return;

			const documentTypes = sectionData.documentType || [];
			const uploadedDocuments = sectionData.documentUploaded || [];

			let isCompleted = false;

			if (sectionKey === "group1") {
				isCompleted = documentTypes.some((doc: any) => doc.documentUpload);
			} else {
				isCompleted = documentTypes.every(
					(doc: any) =>
						doc.documentUpload ||
						uploadedDocuments.some(
							(uploaded: any) =>
								uploaded.name === doc.documentName ||
								uploaded.id === doc.documentId,
						),
				);
			}

			this.documentSections.push({
				title: sectionTitle,
				key: sectionKey,
				isOpen: true,
				isCompleted,
				documentTypes,
				uploadedDocuments,
			});
		});

		this.stats = this.getUploadedDocumentsStats(this.documentSections);
	}

	public isImage(file_type: string): boolean {
		return file_type && file_type !== ""
			? file_type.startsWith("image/")
			: false;
	}

	/**
	 * Track function for outer ngFor to improve performance
	 * @param index Index of the item
	 * @returns The index as a unique identifier
	 */
	trackByIndex(index: number): number {
		return index;
	}

	/**
	 * Track function for inner ngFor to improve performance
	 * @param index Index of the item
	 * @param item Document item
	 * @returns Unique identifier for the document
	 */
	trackByDocumentId(index: number, item: any): string {
		return item.id || item.name || index.toString();
	}

	/**
	 * Track function for document types ngFor
	 * @param index Index of the item
	 * @param doc Document type item
	 * @returns Unique identifier for the document type
	 */
	trackByDocumentType(index: number, doc: any): string {
		return doc.documentId || doc.documentName || index.toString();
	}

	async openAttachDocumentsDialog() {
		const modal = await this.modalController.create({
			component: AttachDocumentsComponent,
			cssClass: "installment-plan-modal modal-blur-backdrop",
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		if (data && data?.refresh) {
			await this.appUtilsService.showInfo("กำลังรีเฟรชข้อมูลเอกสาร");
			await this.fetchUploadEvidence();
		}
	}

	/**
	 * Remove a file by its ID
	 * @param id Document ID to remove
	 */
	public async removeFile(id: string) {
		try {
			// Uncomment when API is ready
			// const txnId = this.getTxnIdOrError();
			// await this.starmoneyService.deleteDocument({
			//   txn_id: txnId,
			//   document_id: id,
			// });
			// await this.fetchUploadEvidence(); // refresh data

			// For now, just show a message
			await this.appUtilsService.showInfo(`กำลังลบเอกสาร ID: ${id}`);
		} catch (error) {
			await this.handleError(error, "Failed to remove document");
		}
	}

	handleNext() {
		// Mark current step as completed in the step counter service
		this.stepCounterService.markStepCompleted(this.activeStepId);

		// Dispatch next event with employment data
		super.next();
	}

	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
