<ion-content>
	<div class="app-layout">
		<!-- Header -->
		<app-header-mini mode="workflow"></app-header-mini>

		<!-- Content -->
		<div class="app-content">
			<div class="content-wrapper">
				<!-- Header Section -->
				<div class="text-center">
					<p class="page-title">กรอกข้อมูลผู้สมัครสินเชื่อ</p>
				</div>

				<form [formGroup]="form">
					<div class="section-header">
						<span class="section-header-title">ข้อมูลผู้สมัครสินเชื่อ</span>
					</div>

					<!-- ข้อมูลผู้สมัครสินเชื่อ -->
					<div class="form-section">
						<h2 class="section-title">
							ผู้ขอสินเชื่อ
							<span class="required">*</span>
						</h2>
						<!-- ชื่อผู้ขอสินเชื่อ -->
						<div class="form-field-row">
							<label class="field-label">ชื่อผู้ขอสินเชื่อ</label>
							<div class="field-value">{{ idCardInfo.fullName }}</div>
						</div>

						<!-- เพศ -->
						<div class="form-field-row">
							<label class="field-label">เพศ</label>
							<div class="field-value">{{ idCardInfo.gender }}</div>
						</div>
					</div>

					<!-- เลขที่เอกสาร -->
					<div class="form-section">
						<h2 class="section-title">
							เลขที่เอกสาร
							<span class="required">*</span>
						</h2>

						<!-- ประเภทเอกสาร -->
						<div class="form-field-row">
							<label class="field-label">ประเภทเอกสาร</label>
							<div class="field-value">บัตรประชาชน</div>
						</div>

						<!-- สัญชาติ -->
						<div class="form-field-row">
							<label class="field-label">สัญชาติ</label>
							<div class="field-value">{{ idCardInfo.nationality }}</div>
						</div>

						<!-- เลขบัตรประชาชน -->
						<div class="form-field-row">
							<label class="field-label">เลขบัตรประชาชน</label>
							<div class="field-value">{{ idCardInfo.idNumber }}</div>
						</div>
					</div>

					<!-- วันเกิด -->
					<div class="form-section">
						<h2 class="section-title">
							วันเกิด
							<span class="required">*</span>
						</h2>

						<!-- วันเกิด -->
						<div class="form-field-row">
							<label class="field-label">วันเกิด</label>
							<div class="field-value">{{ idCardInfo.birthDate }}</div>
						</div>

						<!-- อายุ -->
						<div class="form-field-row">
							<label class="field-label">อายุ</label>
							<div class="field-value">{{ idCardInfo.age }}</div>
						</div>
					</div>

					<!-- วันที่เอกสาร -->
					<div class="form-section">
						<h2 class="section-title">
							วันที่เอกสาร
							<span class="required">*</span>
						</h2>

						<!-- วันออกบัตร -->
						<div class="form-field-row">
							<label class="field-label">วันออกบัตร</label>
							<div class="field-value">{{ idCardInfo.issueDate }}</div>
						</div>

						<!-- วันหมดอายุบัตร -->
						<div class="form-field-row">
							<label class="field-label">วันหมดอายุบัตร</label>
							<div class="field-value">{{ idCardInfo.expiryDate }}</div>
						</div>
					</div>

					<!-- ที่อยู่ตามหน้าบัตรประชาชน -->
					<div class="form-section">
						<h2 class="section-title">
							ที่อยู่ตามหน้าบัตรประชาชน
							<span class="required">*</span>
						</h2>

						<!-- ที่อยู่ -->
						<div class="form-field-row">
							<label class="field-label">ที่อยู่</label>
							<div class="field-value">{{ idCardInfo.address }}</div>
						</div>
					</div>
				</form>
			</div>
		</div>
		<!-- Footer -->
		<app-footer>
			<div class="footer-wrapper">
				<p-button label="ย้อนกลับ" class="invisible" (onClick)="back()" />
				<p-button label="ดำเนินการต่อ" (onClick)="onCheckStatus()" />
			</div>
		</app-footer>
	</div>
</ion-content>
