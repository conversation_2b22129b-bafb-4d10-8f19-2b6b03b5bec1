import { CommonModule } from "@angular/common";
import { Component, inject, OnInit } from "@angular/core";
import {
	Form<PERSON>uilder,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { ProgressStatusService } from "../../shared/components/circular-progress-status/progress-status.service";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";
import { CheckResultType } from "../check-id-card/check-id-card.component";

/**
 * ข้อมูลบัตรประชาชน
 */
export interface IdCardInfo {
	fullName: string;
	gender: string;
	idNumber: string;
	nationality: string;
	birthDate: string;
	issueDate: string;
	expiryDate: string;
	address: string;
	age: string;
}

@Component({
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		ButtonModule,
		ReactiveFormsModule,
		HeaderMiniComponent,
		FooterComponent,
	],
	selector: "app-address-contact-modal",
	styleUrl: "./personal-info.component.scss",
	templateUrl: "./personal-info.component.html",
})
export class PersonalInfoComponent extends WorkflowStepBaseComponent implements OnInit {
	private appUtilsService = inject(AppUtilsService);
	private progressStatusService = inject(ProgressStatusService);
	private readonly fb = inject(FormBuilder);
	protected form!: FormGroup;

	// Store the original options for retry
	private originalLoadingOptions: any = null;

	// ข้อมูลจากบัตรประชาชน
	protected readonly idCardInfo: IdCardInfo = {
		fullName: "นาย สมชาย สายเสมอ",
		gender: "ชาย",
		idNumber: "0 1234 56789 01 2",
		nationality: "ไทย",
		birthDate: "1 มกราคม 2540",
		issueDate: "29 ธันวาคม 2563",
		expiryDate: "29 ธันวาคม 2570",
		address: "1 บางบา บางบา กรุงเทพมหานคร 10210",
		age: "27 ปี 10 เดือน",
	};

	constructor() {
		super();
	}

	/**
	 * ทำงานเมื่อคอมโพเนนต์ถูกสร้าง
	 */
	ngOnInit(): void {
		this.initForm();
		this.loadIdCardData();
	}

	/**
	 * สร้างฟอร์มเริ่มต้น
	 */
	private initForm(): void {
		this.form = this.fb.group({
			fullName: ["", Validators.required],
			gender: [""],
			idType: ["บัตรประชาชน"],
			nationality: [""],
			idNumber: ["", Validators.required],
			birthDate: ["", Validators.required],
			age: [""],
			issueDate: ["", Validators.required],
			expiryDate: ["", Validators.required],
			address: ["", Validators.required],
		});
	}

	/**
	 * โหลดข้อมูลจากบัตรประชาชน
	 */
	private loadIdCardData(): void {
		this.form.patchValue({
			fullName: this.idCardInfo.fullName,
			gender: this.idCardInfo.gender,
			nationality: this.idCardInfo.nationality,
			idNumber: this.idCardInfo.idNumber,
			birthDate: this.idCardInfo.birthDate,
			age: this.idCardInfo.age,
			issueDate: this.idCardInfo.issueDate,
			expiryDate: this.idCardInfo.expiryDate,
			address: this.idCardInfo.address,
		});
	}

	/**
	 * ส่งข้อมูลและไปหน้าถัดไป
	 */
	async onCheckStatus(): Promise<void> {
		console.log("Starting verification process");

		try {
			// แสดงหน้า Global Checking Dialog
			await this.showGlobalCheckingDialog();
		} catch (error) {
			console.error("Error during verification process:", error);
			this.appUtilsService.showError("ข้อผิดพลาด", "ไม่สามารถตรวจสอบข้อมูลได้");
		}
	}

	private async showGlobalCheckingDialog(): Promise<void> {
		console.log("Opening global checking dialog...");

		try {
			// เปิด Circular Progress Loading Modal พร้อมทำงาน API
			// โดยใช้ indeterminateProgress เพราะไม่รู้ว่า API จะเสร็จเมื่อไหร่
			this.originalLoadingOptions = {
				title: "ตรวจสอบข้อมูล",
				statusMessage: "ตรวจสอบข้อมูล",
				description: "อยู่ระหว่างการตรวจสอบข้อมูล\nณารอจนกว่าระบบจะทำงานเสร็จ",
				showIcon: true,
				fullscreen: false,
				indeterminateProgress: true,
				autoClose: false,
				autoCloseDelay: 100,

				// ส่งก์อต้องการให้ทำงานระหว่างแสดง loading
				asyncOperation: async () => {
					// เรียก API และรอผลลัพธ์
					return await this.mockApiCall();
				},
				onSuccess: () => ({
					statusType: "success",
					title: "ตรวจสอบข้อมูลสำเร็จ",
					message: "ตรวจสอบข้อมูลสำเร็จ",
					description:
						"สามารถทำการสมัครสินเชื่อให้ลูกค้าต่อได้\nกรุณากดปุ่ม “สมัครสินเชื่อต่อ” เพื่อดำเนินการต่อ",
					primaryButtonLabel: "สมัครสินเชื่อต่อ",
					onPrimaryAction: async () => {
						console.log("Primary action clicked");
						await this.progressStatusService.closeNotification();
						super.next();
					},
				}),
				onWarning: () => ({
					statusType: "warning",
					title: "ไม่สามารถยืนยันตัวตนได้",
					message: "ขออภัย! ไม่สามารถตรวจสอบข้อมูลของท่านได้",
					description:
						"สาเหตุ: ระบบขัดข้อง โปรดลองใหม่อีกครั้ง\nCode: xxxxxxx\n\nหากมีข้อสงสัย โปรดติดต่อพนักงานของเรา",
					primaryButtonLabel: "ตรวจสอบข้อมูลอีกครั้ง",
					onPrimaryAction: async () => {
						console.log("Retry button clicked");
						await this.retryOperation();
					},
					secondaryButtonLabel: "ย้อนกลับ",
					onSecondaryAction: async () => {
						console.log("Secondary action clicked");
						await this.progressStatusService.closeNotification();
					},
				}),
				onError: () => ({
					statusType: "error",
					title: "ไม่ผ่านการตรวจสอบ",
					message: "ขออภัย! ท่านไม่ผ่านการตรวจสอบ",
					description: "สาเหตุ: ...\nCode: xxxxxxx\n\nขอข้อความแต่ละเคส",
					errorCode: "E12345",
					primaryButtonLabel: "ตกลง",
					onPrimaryAction: async () => {
						console.log("Error primary action clicked");
						await this.progressStatusService.closeNotification();
					},
				}),
			};

			// Execute the loading operation
			await this.progressStatusService.showLoadingWithOperation<CheckResultType>(this.originalLoadingOptions);
		} catch (error) {
			console.error("Error during API call:", error);
		}
	}

	/**
	 * Retry the operation when user clicks retry button
	 */
	private async retryOperation(): Promise<void> {
		if (!this.originalLoadingOptions) {
			console.error("No original loading options available for retry");
			return;
		}

		try {
			await this.progressStatusService.retryOperationWithExecution<CheckResultType>(this.originalLoadingOptions);
		} catch (error) {
			console.error("Error during retry operation:", error);
		}
	}

	/**
	 * จำลองการ API
	 * สร้างก์อจะค่า Promise ที่จะ resolve ภายในเวลาที่กำหนด
	 * และค่าความสำเร็จแบบสุ่ม (3 ค่า: สำเร็จ, เตือน, ล้มเหลว)
	 */
	private mockApiCall(): Promise<CheckResultType> {
		console.log("Calling mock API...");
		return new Promise((resolve) => {
			// สุ่มเวลาในการตอบระหว่าง 1-2 วินาที
			const responseTime = Math.random() * 1000 + 1000;

			setTimeout(() => {
				// สุ่มผลลัพธ์ (50% สำเร็จ, 30% เตือน, 20% ล้มเหลว)
				const random = Math.random();
				let result: CheckResultType;

				if (random < 0.5) {
					result = CheckResultType.Success;
				} else if (random < 0.8) {
					result = CheckResultType.Warning;
				} else {
					result = CheckResultType.Failure;
				}

				console.log(`API response (after ${responseTime.toFixed(0)}ms): ${result}`);
				resolve(result);
			}, responseTime);
		});
	}
}
