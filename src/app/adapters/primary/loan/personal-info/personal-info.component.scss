/* Form Container */
.form-container {
	@apply overflow-y-auto rounded-lg border border-gray-200 bg-white p-4;
	@apply h-[calc(100vh-200px)];
}

/* Form Sections */
.form-section {
	@apply mb-2 border-b border-gray-200 py-2 last:mb-12 last:border-b-0;
	@apply px-4;
}

.section-header {
	@apply mb-4 bg-primary/10 px-4 py-2;
	@apply flex-1;
}

.section-header-title {
	@apply text-lg font-medium text-[#01508A];
	@apply flex items-center;
}

.section-title {
	@apply text-base font-medium text-gray-900;
	@apply mb-2 flex items-center;
}

/* Form Fields */
.form-field-row {
	@apply mb-2 flex w-full flex-row items-center justify-between last:mb-0;
}

.field-label {
	@apply mb-1 text-sm text-gray-500 sm:mb-0 sm:w-1/2;
}

.field-value {
	@apply text-right text-sm font-medium text-gray-900 sm:w-1/2;
}

/* Required Field */
.required {
	@apply ml-1 text-red-500;
}