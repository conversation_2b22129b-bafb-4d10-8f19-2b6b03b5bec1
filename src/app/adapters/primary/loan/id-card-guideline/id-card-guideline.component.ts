import { CommonModule } from "@angular/common";
import { Component } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-id-card-guideline-modal",
	standalone: true,
	imports: [
		CommonModule,
		IonicModule,
		FormsModule,
		FooterComponent,
		ButtonModule,
		HeaderMiniComponent,
	],
	styleUrl: "./id-card-guideline.component.scss",
	template: `
		<ion-content>
			<div class="app-layout">
				<!-- Header -->
				<app-header-mini mode="workflow"></app-header-mini>

				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper">
						<!-- Header Section -->
						<div class="text-center">
							<p class="page-title">ลูกค้าบุคคลธรรมดาสัญชาติไทย</p>
							<p class="page-title-description">
								ขั้นตอนการอ่านบัตรประชาชนลูกค้า
							</p>
						</div>

						<!-- Steps Section -->
						<div class="guideline-list">
							<!-- Step 1 -->
							<div class="guideline-item">
								<div class="guideline-item-image">
									<img
										src="assets/svgs/person-with-id-card.svg"
										alt="ID Card Guide" />
								</div>
								<div class="guideline-item-content">
									<h3 class="guideline-item-title">
										แจ้งลูกค้าให้เตรียมบัตรประชาชน
									</h3>
									<p class="guideline-item-description">
										บัตรประชาชนจะต้องมีแถบแม่เหล็กที่อยู่ไนสภาพสมบูรณ์
										ไม่ชำรุดเสียหาย
									</p>
								</div>
							</div>

							<!-- Step 2 -->
							<div class="guideline-item">
								<div class="guideline-item-image">
									<img
										src="assets/svgs/id-card-reader.svg"
										alt="ID Card Guide" />
								</div>
								<div class="guideline-item-content">
									<h3 class="guideline-item-title">
										เปิดแอปพลิเคชัน
										<!-- {{ getFieldValue("appName", "Star Money Dip Chip") }} -->
									</h3>
									<p class="guideline-item-description">
										เปิดแอปพลิเคชัน
										<!-- {{ getFieldValue("appName", "Star Money Dip Chip") }} -->
										แล้วทำการเสียบบัตรประชาชนของลูกค้า เพื่อทำการยืนยันตัวตน
									</p>
								</div>
							</div>

							<!-- Step 3 -->
							<div class="guideline-item">
								<div class="guideline-item-image">
									<img
										src="assets/svgs/star-money-guide.svg"
										alt="Star Money Guide" />
								</div>
								<div class="guideline-item-content">
									<h3 class="guideline-item-title">
										กลับมาทำรายการต่อที่ Star Money SMART SALE
									</h3>
									<p class="guideline-item-description">
										กลับมาทำการสมัครสินเชื่อให้ลูกค้าต่อ
										เมื่อทำการยืนยันตัวตนด้วยการ
										เสียบบัตรประชาชนให้ลูกค้าเสร็จแล้ว
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper">
						<p-button
							label="ย้อนกลับ"
							class="invisible"
							(onClick)="handleBack()" />
						<p-button label="ดำเนินการต่อ" (onClick)="handleNext()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class IdCardGuidelineModalComponent extends WorkflowStepBaseComponent {
	constructor() {
		super();
	}

	handleNext(): void {
		console.log("Next button clicked in ID Card Guideline component");

		// Dispatch next event to workflow service
		this.workflowModalService.dispatch({
			command: "next",
			fromStepId: this.stepId,
		});
	}

	handleBack(): void {
		console.log("Back button clicked in ID Card Guideline component");

		// Dispatch back event to workflow service
		this.workflowModalService.dispatch({
			command: "back",
			fromStepId: this.stepId,
		});
	}
}
