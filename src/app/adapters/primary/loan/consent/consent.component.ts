import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject, OnInit } from "@angular/core";
import {
	AbstractControl,
	FormArray,
	FormBuilder,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Term } from "@domain/models/term.model";
import { ButtonModule } from "primeng/button";
import { CheckboxModule } from "primeng/checkbox";
import { SkeletonModule } from "primeng/skeleton";
import { ToggleSwitchModule } from "primeng/toggleswitch";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

// Interfaces for consent form data
export interface ConsentFormItem {
	id: number;
	title: string;
	isSelected: boolean;
}

export interface ConsentFormSection {
	title: string;
	subTitle?: string;
	list: ConsentFormItem[];
}

export interface ConsentFormData {
	consents: ConsentFormSection[];
}

// Form control interfaces
export interface ConsentItemFormControl {
	id: number;
	title: string;
	isSelected: boolean;
}

export interface ConsentSectionFormControl {
	title: string;
	sub_title?: string;
	list: ConsentItemFormControl[];
}

@Component({
	standalone: true,
	imports: [
		CommonModule,
		ReactiveFormsModule,
		FormsModule,
		HeaderMiniComponent,
		ButtonModule,
		CheckboxModule,
		SkeletonModule,
		ToggleSwitchModule,
	],
	selector: "app-consent",
	styleUrls: ["./consent.component.scss"],
	template: `
		<div class="app-layout">
			<!-- Header -->
			<app-header-mini [mode]="'workflow'"></app-header-mini>

			<!-- Content -->
			<div class="app-content">
				<div class="content-wrapper">
					<p class="page-title">การให้ความยินยอมเก็บและใช้ข้อมูลส่วนบุคคล</p>
					<p class="page-description-center">
						<span>โปรดเลือกให้หรือไม่ให้ความยินยอมเก็บและใช้ข้อมูลส่วน</span>
						<span>บุคคลเพื่อวัตถุประสงค์ทางการตลาด โดยการเลือกนี้จะ</span>
						<span>ไม่มีผลต่อการทำธุรกรรมใดๆ กับบริษัททั้งสิ้น</span>
					</p>

					<!-- Consent Form -->
					<form
						*ngIf="form && consents"
						[formGroup]="form"
						class="mt-6 space-y-6">
						<div formArrayName="consents">
							<div
								*ngFor="let consent of consentsArray.controls; let i = index"
								[formGroupName]="i"
								class="flex w-full flex-col gap-4">
								<!-- Consent Title -->
								<div class="min-h-8 bg-primary/10 p-2">
									<h2 class="text-base text-[#002E60]">
										{{ consents[i].title }}
									</h2>
								</div>

								<!-- Consent Options -->
								<div formArrayName="list">
									<div
										*ngFor="
											let listItem of getListArray(i).controls;
											let j = index
										"
										[formGroupName]="j"
										class="flex items-start py-2">
										<label class="flex w-full justify-between gap-8">
											<span class="ml-3 text-sm">
												{{ consents[i].list[j].title }}
											</span>
											<p-toggleswitch
												formControlName="isSelected"
												(onChange)="onSelectionChange()" />
										</label>
									</div>
								</div>
							</div>
						</div>
					</form>

					<p-button
						label="ยืนยัน"
						icon="pi pi-arrow-down"
						styleClass="mt-8"
						[fluid]="true"
						[disabled]="!form.valid"
						[loading]="isLoading()"
						(onClick)="next()"></p-button>
				</div>
			</div>
		</div>
	`,
})
export class ConsentComponent
	extends WorkflowStepBaseComponent
	implements OnInit
{
	private readonly starmoneyService = inject(StarmoneyService);
	private readonly appUtilsService = inject(AppUtilsService);
	private readonly fb = inject(FormBuilder);

	// Component state
	protected selectedIds: number[] = [];
	protected term: Term | null = null;
	protected consents: ConsentFormSection[] = [];
	protected termCode: string | null = null;

	// Form
	protected form = this.fb.group({
		consents: this.fb.array([]),
	});

	// Getters for form arrays
	protected get consentsArray(): FormArray {
		return this.form.get("consents") as FormArray;
	}

	protected getListArray(index: number): FormArray {
		return this.consentsArray.at(index).get("list") as FormArray;
	}

	constructor() {
		super();
	}

	ngOnInit(): void {
		this.initializeComponent();
	}

	private initializeComponent(): void {
		this.termCode = this.getTermCodeFromStepData();
		this.loadConsent();
	}

	private getTermCodeFromStepData(): string | null {
		return this.stepData.meta?.["code"] || null;
	}

	private async loadConsent(): Promise<void> {
		await this.runWithLoading(async () => {
			try {
				this.validateTermCode();
				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.getTermAndConsent({
					txn_id: txnId,
					code: this.termCode!,
					lang: "th",
				});

				if (apiResponse.isSuccess() && apiResponse.hasData()) {
					this.term = apiResponse.data;
					if (this.term) {
						this.consents = this.transformTermToConsentFormData(this.term);
						this.buildForm();
					}
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	private validateTermCode(): void {
		if (!this.termCode) {
			throw new Error("No transaction ID or term code available");
		}
	}

	private transformTermToConsentFormData(term: Term): ConsentFormSection[] {
		return term.consentItem.map((item) => ({
			title: item.context.label,
			list: item.item.map((subItem) => ({
				id: subItem.id,
				title: subItem.context.label,
				isSelected: true,
			})),
		}));
	}

	private buildForm(): void {
		const consentsFormArray = this.fb.array(
			this.consents.map((section) =>
				this.createConsentSectionFormGroup(section),
			),
		);

		// Clear existing form array and add new controls
		this.consentsArray.clear();
		consentsFormArray.controls.forEach((control) => {
			this.consentsArray.push(control);
		});
	}

	private createConsentSectionFormGroup(section: ConsentFormSection) {
		return this.fb.group({
			title: [section.title],
			sub_title: [section.subTitle || ""],
			list: this.fb.array(
				section.list.map((item) => this.createConsentItemFormGroup(item)),
			),
		});
	}

	private createConsentItemFormGroup(item: ConsentFormItem) {
		return this.fb.group({
			id: [item.id],
			title: [item.title],
			isSelected: [item.isSelected],
		});
	}

	protected onSelectionChange(): void {
		this.selectedIds = this.extractSelectedIds();
	}

	private extractSelectedIds(): number[] {
		const selectedIds: number[] = [];

		this.consentsArray.controls.forEach((consentGroup: AbstractControl) => {
			const listArray = consentGroup.get("list") as FormArray;
			listArray.controls.forEach((listItem: AbstractControl) => {
				if (listItem.get("isSelected")?.value) {
					const id = listItem.get("id")?.value;
					if (id !== null && id !== undefined) {
						selectedIds.push(id);
					}
				}
			});
		});

		return selectedIds;
	}

	protected async handleSubmit(): Promise<void> {
		await this.runWithLoading(async () => {
			try {
				this.validateTermCode();
				await this.submitConsent();
				this.next();
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	private async submitConsent(): Promise<void> {
		const txnId = this.getTxnIdOrError();
		const apiResponse = await this.starmoneyService.acceptTerm({
			txn_id: txnId,
			code: this.termCode!,
			is_accepted: true,
			consent_item: this.selectedIds,
		});

		if (!apiResponse.isSuccess() || !apiResponse.hasData()) {
			throw new Error(apiResponse.message || "Failed to submit consent");
		}
	}

	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
