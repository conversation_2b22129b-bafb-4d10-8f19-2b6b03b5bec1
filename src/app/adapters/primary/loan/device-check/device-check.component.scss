.device-check-container {
  @apply flex items-center justify-center min-h-screen bg-blue-200 bg-opacity-50 p-4;
}

.device-check-card {
  @apply bg-white rounded-lg shadow-lg p-6 w-full min-h-[80%] max-w-lg flex flex-col;
}

.device-check-title {
  @apply text-lg font-medium text-center text-gray-800 mb-2;
}

.device-check-subtitle {
  @apply text-base text-center text-gray-600 mb-6;
}

.progress-section {
  @apply mb-6;
}

.progress-label {
  @apply flex justify-between text-sm font-light text-gray-600 mb-2;
}

.check-items-container {
  @apply space-y-3 mb-6 flex-grow;
}

.check-item {
  @apply flex items-start p-3 rounded-lg border;

  &.status-pending {
    @apply border-gray-200 bg-gray-50;

    .status-icon {
      @apply text-gray-400;
    }
  }

  &.status-checking {
    @apply border-blue-200 bg-blue-50;

    .status-icon {
      @apply text-blue-500;
    }
  }

  &.status-success {
    @apply border-green-200 bg-green-50;

    .status-icon {
      @apply text-green-500;
    }
  }

  &.status-failed {
    @apply border-red-200 bg-red-50;

    .status-icon {
      @apply text-red-500;
    }
  }
}

.check-item-icon {
  @apply flex-shrink-0 w-5 h-5 flex items-center justify-center mr-3 mt-1;
}

.check-item-content {
  @apply flex-1;

  h3 {
    @apply text-base text-gray-900 mb-1;
  }

  p {
    @apply text-sm font-light text-gray-500;
  }

  .error-message {
    @apply text-sm font-light text-red-500 mt-2;
  }
}

.status-icon {
  @apply w-4 h-4;
}

.spinner {
  @apply block w-4 h-4 border-2 border-blue-500 rounded-full border-t-transparent animate-spin mx-auto;
}

.actions {
  @apply mt-auto pt-4;
}

// สไตล์สำหรับหน้าแจ้งเตือนระบบไม่รองรับ
.error-box {
  @apply flex items-start p-3 rounded-lg border border-red-200 bg-red-50 mb-6 w-full;
}

.error-icon {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center mr-3 text-red-500;
}

.error-content {
  @apply flex-1;

  h3 {
    @apply text-base text-gray-900 mb-1;
  }

  p {
    @apply text-sm font-light text-gray-500;
  }
}

.code-section {
  @apply text-center text-base text-gray-500 mb-8;
}

// Override PrimeNG styles
:host ::ng-deep {
  .p-progressbar {
    @apply h-2 bg-gray-200;

    .p-progressbar-value {
      @apply bg-green-500;
    }
  }

  .p-button.p-button-primary {
    @apply bg-blue-600 border-blue-600;

    &:enabled:hover {
      @apply bg-blue-700 border-blue-700;
    }

    &:disabled {
      @apply bg-gray-400 border-gray-400 cursor-not-allowed;
    }
  }
}