<div *ngIf="!deviceCheckService.isDeviceSupported()" class="device-check-container">
	<div class="device-check-card">
		<h1 class="device-check-title">ตรวจสอบความพร้อมอุปกรณ์สำหรับใช้งานระบบยืนยันตัวตน</h1>
		<p class="device-check-subtitle">กรุณาตรวจสอบความต้องการขั้นต่ำของระบบอุปกรณ์นี้</p>
		<div class="flex flex-grow flex-col items-center">
			<div class="error-box">
				<div class="error-icon">
					<img src="assets/svgs/incorrect.svg" class="h-4 w-4" />
				</div>
				<div class="error-content">
					<h3>ระบบปฏิบัติการนี้ไม่รองรับการใช้งาน</h3>
					<p>รองรับตั้งแต่ iOS 16+, Android 9+, Huawei</p>
				</div>
			</div>
		</div>
		<div class="code-section">
			<p>Code: {{ deviceCheckService.deviceCode() }}</p>
		</div>
		<div class="actions">
			<p-button label="ตกลง" (onClick)="proceedToLogin()" styleClass="w-full"></p-button>
		</div>
	</div>
</div>

<div *ngIf="deviceCheckService.isDeviceSupported()" class="device-check-container">
	<div class="device-check-card">
		<h1 class="device-check-title">ตรวจสอบความพร้อม</h1>
		<p class="device-check-subtitle">กรุณารอสักครู่ ระบบกำลังตรวจสอบอุปกรณ์ของท่าน</p>
		<div class="progress-section">
			<div class="progress-label">
				<span>ความคืบหน้า</span>
				<span>{{ deviceCheckService.progress() }}%</span>
			</div>
			<p-progressBar [value]="deviceCheckService.progress()" [showValue]="false"></p-progressBar>
		</div>
		<div class="check-items-container">
			<div
				*ngFor="let item of deviceCheckService.checkItems()"
				class="check-item"
				[ngClass]="getStatusClass(item.status)">
				<div class="check-item-icon">
					<span *ngIf="item.status === 'checking'" class="spinner"></span>
					<img
						*ngIf="item.status !== 'checking' && item.status !== 'pending'"
						[src]="getStatusIcon(item.status)"
						class="status-icon" />
				</div>
				<div class="check-item-content">
					<h3>{{ item.name }}</h3>
					<p>{{ item.description }}</p>
					<p *ngIf="item.status === 'failed' && item.errorMessage" class="error-message">
						{{ item.errorMessage }}
					</p>
				</div>
			</div>
			<p
				*ngIf="deviceCheckService.checkItems()[4].status === CheckStatus.Error"
				class="text-center text-sm font-normal text-[#6B707B]">
				<span>ระบบจะทำการเปิดบราวเซอร์หลักเพื่อดำเนินการต่อ</span>
				<br />
				<span>เมื่อท่านกดปุ่ม “เปิดบราวเซอร์หลัก”</span>
			</p>
			<div *ngIf="deviceCheckService.checkItems().length < 3" class="flex-grow"></div>
		</div>
		<div class="actions">
			<p-button
				label="เปิด Browser หลัก"
				styleClass="w-full"
				(onClick)="deviceCheckService.openTargetBrowser()"
				*ngIf="deviceCheckService.isStateCheckBrowserError()"></p-button>
			<p-button
				label="ต่อไป"
				styleClass="w-full"
				[disabled]="!deviceCheckService.isAllChecksPassed()"
				(onClick)="proceedToLogin()"></p-button>
		</div>
	</div>
</div>
