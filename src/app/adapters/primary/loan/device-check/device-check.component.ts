import { CommonModule } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { ButtonModule } from "primeng/button";
import { ProgressBarModule } from "primeng/progressbar";
import { CheckStatus, DeviceCheckService } from "src/app/core/application/device-check.service";

import { WebcamService } from "src/app/adapters/primary/shared/services/webcam.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";

@Component({
	selector: "app-device-check",
	standalone: true,
	imports: [CommonModule, ButtonModule, ProgressBarModule],
	templateUrl: "./device-check.component.html",
	styleUrls: ["./device-check.component.scss"],
})
export class DeviceCheckComponent implements OnInit {
	CheckStatus = CheckStatus;

	constructor(
		public deviceCheckService: DeviceCheckService,
		private webcamService: WebcamService,
		private appUtilsService: AppUtilsService,
		private router: Router,
	) {}

	async ngOnInit(): Promise<void> {
		await this.startPreChecks();
	}

	async startPreChecks() {
		if (this.deviceCheckService.isDeviceSupported()) {
			const permission: string = await this.webcamService.checkCameraPermission();
			if (permission !== "granted") {
				this.showPermissionExplanation();
			} else {
				await this.startChecks();
			}
		}
	}

	showPermissionExplanation() {
		this.appUtilsService.showAlertDialog({
			header: "การขอใช้งานกล้อง",
			message: "การเชื่อจำเป็นต้องใช้กล้องในบางขั้นตอนใช้งานจำเป็นต้องให้เข้ากล้องเพื่อตรวจสอบความพร้อมใช้งานของคุณ",
			acceptLabel: "รับทราบ",
			acceptCallback: async () => await this.startChecks(),
		});
	}

	async startChecks(): Promise<void> {
		await this.deviceCheckService.startChecks();
	}

	proceedToLogin(): void {
		this.router.navigate(["/login"]);
	}

	getStatusIcon(status: string): string {
		switch (status) {
			case CheckStatus.Success:
				return "assets/svgs/correct.svg";
			case CheckStatus.Error:
				return "assets/svgs/incorrect.svg";
			default:
				return "assets/svgs/pending.svg";
		}
	}

	getStatusClass(status: string): string {
		switch (status) {
			case CheckStatus.Success:
				return "status-success";
			case CheckStatus.Error:
				return "status-failed";
			case CheckStatus.Checking:
				return "status-checking";
			default:
				return "status-pending";
		}
	}
}
