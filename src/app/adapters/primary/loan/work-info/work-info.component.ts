import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject, OnInit } from "@angular/core";
import {
	FormBuilder,
	FormGroup,
	FormsModule,
	Validators,
} from "@angular/forms";
import { WorkingInfo } from "@domain/models/working.model";
import { IonicModule, ModalController } from "@ionic/angular";
import dayjs from "dayjs";
import { ButtonModule } from "primeng/button";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { DateUtils } from "src/app/core/utils/date-utils";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { WorkingInfoFormComponent } from "../../shared/components/forms/working-info-form/working-info-form.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { StepCounterComponent } from "../../shared/components/step-counter/step-counter.component";
import { StepCounterService } from "../../shared/services/step-counter.service";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-employment-modal",
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		IonicModule,
		ButtonModule,
		HeaderMiniComponent,
		FooterComponent,
		StepCounterComponent,
	],
	styleUrl: "./work-info.component.scss",
	template: `
		<ion-content>
			<div class="app-layout">
				<app-header-mini mode="workflow" />

				<!-- Content -->
				<div class="app-content">
					<div class="flex h-full flex-row">
						<!-- Step Counter -->
						<div class="flex h-full">
							<app-step-counter [activeStep]="activeStepId"></app-step-counter>
						</div>
						<div class="content-wrapper">
							<!-- Consent Title -->
							<div class="min-h-8 bg-primary/10 p-2">
								<h2 class="text-lg font-medium text-primary_dark">
									ข้อมูลการทำงาน
								</h2>
							</div>

							<div
								*ngFor="let item of working"
								class="m-2 text-sm text-gray-500 lg:text-base">
								<div class="flex items-center justify-between">
									<p class="my-3 text-base font-normal text-gray-900">
										{{ item.title }}
										<span *ngIf="item.require" class="text-red-500">*</span>
									</p>
									<p
										class="cursor-pointer text-base font-normal text-primary"
										(click)="editWorkingInfo(item)">
										{{
											form.get(item.fields[0].key)?.value
												? "แก้ไขข้อมูล"
												: "เพิ่มข้อมูล"
										}}
									</p>
								</div>
								<div
									*ngFor="let child of item.fields"
									class="mb-2 flex justify-between text-sm font-light">
									<p class="text-gray-500">{{ child.title }}</p>
									<p class="text-gray-900">
										{{ form.get(child.key)?.value || "-" }}
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper !justify-between">
						<p-button
							label="ย้อนกลับ"
							[outlined]="true"
							[fluid]="true"
							(onClick)="back()" />
						<p-button
							label="ต่อไป"
							[fluid]="true"
							[loading]="isLoading()"
							[disabled]="!isFormValid"
							(onClick)="handleNext()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class WorkInfoComponent
	extends WorkflowStepBaseComponent
	implements OnInit
{
	// Inject StepCounterService
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private modalController = inject(ModalController);
	private fb = inject(FormBuilder);
	public stepCounterService = inject(StepCounterService);

	// Active step ID (current step)
	activeStepId: string = "2.2";
	form: FormGroup = new FormGroup({});
	workingInfo: WorkingInfo | null = null;
	working = [
		{
			title: "ข้อมูลการทำงาน",
			fields: [
				{
					key: "occupationName",
					title: "อาชีพ",
					validators: [Validators.required],
				},
				{
					key: "occupationGroupName",
					title: "กลุ่มอาชีพ",
					validators: [Validators.required],
				},
				{
					key: "position",
					title: "ตำแหน่งงาน",
					validators: [, Validators.required],
				},
				{
					key: "startDate",
					title: "วันที่เริ่มงาน",
					validators: [Validators.required],
				},
				{
					key: "workExperience",
					title: "อายุงาน",
					validators: [Validators.required],
				},
			],
			require: true,
		},
		{
			title: "ข้อมูลรายรับรายจ่าย",
			fields: [
				{
					key: "incomeSlip",
					title: "ท่านมีสลิปเงินเดือนหรือไม่",
					validators: [Validators.required],
				},
				{
					key: "monthlyIncome",
					title: "รายได้ประจำต่อเดือน",
					validators: [Validators.required],
				},
				{
					key: "additionalIncome",
					title: "รายได้อื่นๆ ต่อเดือน",
					validators: [Validators.required],
				},
				{
					key: "monthlyExpense",
					title: "รายจ่ายต่อเดือน",
					validators: [Validators.required],
				},
			],
			require: true,
		},
	];

	constructor() {
		super();
	}

	get isFormValid(): boolean {
		return this.working.every((item) =>
			item.fields.every((field) => this.form.get(field.key)?.value !== ""),
		);
	}

	async ngOnInit(): Promise<void> {
		this.initWorkingForm();
		await this.getWorkInfo();
	}

	initWorkingForm() {
		const group: Record<string, any> = {};
		this.working.forEach((item) => {
			item.fields.forEach((field) => {
				group[field.key] = ["", field.validators];
			});
		});

		this.form = this.fb.group(group);
	}

	async getWorkInfo() {
		await this.runWithLoading(async () => {
			try {
				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.getWorkingInfo({
					txn_id: txnId,
				});
				if (apiResponse.isSuccess() && apiResponse.hasData()) {
					this.workingInfo = apiResponse.data;
					this.mapWorkInfoToForm();
				} else {
					console.error(apiResponse.message);
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	mapWorkInfoToForm() {
		if (!this.workingInfo) return;

		this.working.forEach((section) => {
			section.fields.forEach((field) => {
				const key = field.key;
				if (this.form.controls[key]) {
					let value = this.workingInfo?.[key as keyof WorkingInfo] ?? "";
					if (
						section.title === "ข้อมูลรายรับรายจ่าย" &&
						value !== null &&
						value !== ""
					) {
						if (key === "incomeSlip") {
							value = value === true ? "มี" : "ไม่มี";
						} else if (value !== 0) {
							value = `${value} บาท`;
						} else {
							value = "-";
						}
					}
					if (key === "startDate" && value !== null && value !== "") {
						value = DateUtils.convertIsoDateToFullDateBE(value as string);
					}
					if (key === "workExperience") {
						value = this.calculateWorkExperience(
							this.workingInfo?.startDate as string,
						);
					}

					this.form.controls[key].setValue(value);
				}
			});
		});
	}

	calculateWorkExperience(dateString: string): string {
		if (!dateString) return "";
		const today = dayjs();
		const startDate = dayjs(dateString);
		if (!startDate.isValid()) return "";
		const years = today.diff(startDate, "years");
		const startDateAdded = startDate.add(years, "years");
		const months = today.diff(startDateAdded, "months");
		return `${years > 0 ? `${years} ปี` : ""} ${months > 0 ? `${months} เดือน` : ""}`.trim();
	}

	async editWorkingInfo(item: any) {
		const modal = await this.modalController.create({
			component: WorkingInfoFormComponent,
			componentProps: {
				item,
				workingInfo: this.workingInfo,
			},
			cssClass: "global-dialog-modal modal-blur-backdrop",
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		console.log("on close data:", data);
		if (data && data.refresh) {
			await this.getWorkInfo();
		}
	}

	/**
	 * Override goNext method to include form data
	 */
	handleNext(): void {
		// Mark current step as completed in the step counter service
		this.stepCounterService.markStepCompleted(this.activeStepId);

		// Dispatch next event with employment data
		super.next();
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
