import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import { Component, inject } from "@angular/core";
import {
	FormBuilder,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { Modal<PERSON>ontroller } from "@ionic/angular/standalone";
import { ButtonModule } from "primeng/button";
import { InputMaskModule } from "primeng/inputmask";
import { RippleModule } from "primeng/ripple";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { MyNumpadPhoneComponent } from "../../shared/components/my-numpad-phone/my-numpad-phone.component";
import { OtpPhoneVerifyComponent } from "../../shared/components/otp-phone-verify/otp-phone-verify.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		FooterComponent,
		InputMaskModule,
		MyNumpadPhoneComponent,
		ButtonModule,
		HeaderMiniComponent,
		RippleModule,
	],
	selector: "app-otp-phone",
	styleUrls: ["./otp-phone.component.scss"],
	template: `
		<div class="app-layout">
			<!-- Header -->
			<app-header-mini mode="workflow" />

			<!-- Content -->
			<div class="app-content">
				<div class="content-wrapper">
					<!-- Header Section -->
					<div class="mb-6 text-center">
						<p class="page-title">ยืนยันรหัส OTP เบอร์โทรศัพท์</p>
						<p class="page-description-center">
							<span>จำเป็นต้องใช้เบอร์โทรศัพท์มือถือเพื่อส่ง OTP</span>
							<span>ยืนยันตัวตนและทำธุรกรรมกับบริษัท</span>
						</p>
					</div>

					<form
						*ngIf="form"
						[formGroup]="form"
						class="mx-auto flex w-full max-w-md flex-col items-center gap-4">
						<div class="my-4 w-full">
							<div
								class="mx-auto flex h-14 w-full items-center justify-center rounded-lg border border-gray-300 bg-white transition-all duration-300">
								<ng-container *ngIf="!phoneNumber; else hasPhoneNumber">
									<p class="px-4 py-2 text-center text-gray-500">
										กรอกเบอร์โทรศัพท์มือถือของลูกค้า
									</p>
								</ng-container>
								<ng-template #hasPhoneNumber>
									<p
										class="text-lg font-medium"
										[ngClass]="{
											'text-primary': !isPhoneValid(),
											'text-green-500': isPhoneValid(),
										}">
										{{ phoneNumber }}
									</p>
								</ng-template>
							</div>
						</div>

						<div class="box-details w-full">
							<div class="details-wrapper text-base md:text-lg">
								<app-numpad-phone
									class="w-full"
									(numpadChangeEvent)="
										handlePhoneChange($event)
									"></app-numpad-phone>
							</div>
						</div>
					</form>
				</div>
			</div>

			<!-- Footer -->
			<app-footer>
				<div class="footer-wrapper">
					<p-button
						label="ต่อไป"
						styleClass="w-full"
						[disabled]="!isPhoneValid()"
						[loading]="isLoading()"
						(onClick)="handleNextButtonClick()" />
				</div>
			</app-footer>
		</div>
	`,
})
export class OtpPhoneComponent extends WorkflowStepBaseComponent {
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private fb = inject(FormBuilder);
	private modalController = inject(ModalController);
	public form = this.fb.group({
		phoneNumber: ["", [Validators.required]],
	});

	public handlePhoneChange(event: string) {
		this.form.patchValue({ phoneNumber: this.formatPhoneNumber(event) });
	}

	private formatPhoneNumber(value: string): string {
		if (!value) return "";
		const numbers = value.replace(/\D/g, "");
		if (numbers.length <= 3) return numbers;
		if (numbers.length <= 6)
			return `${numbers.slice(0, 3)}-${numbers.slice(3)}`;
		return `${numbers.slice(0, 3)}-${numbers.slice(3, 6)}-${numbers.slice(6, 10)}`;
	}

	get phoneNumber() {
		return this.form.get("phoneNumber")?.value || "";
	}

	isPhoneValid(): boolean {
		const phone = this.phoneNumber.replace(/\D/g, "");
		return phone.length === 10 && phone.startsWith("0");
	}

	/**
	 * Updates the phone number and opens the OTP verification modal if successful
	 */
	async handleNextButtonClick() {
		if (!this.isPhoneValid()) {
			return;
		}

		await this.runWithLoading(async () => {
			try {
				const cleanPhone = this.phoneNumber.replace(/\D/g, "");
				const phone = `+66${cleanPhone?.slice(1)}`;
				if (!phone) {
					throw new Error("เบอร์โทรศัพท์ไม่ถูกต้อง");
				}

				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.updateContact({
					txn_id: txnId,
					phone_number: phone,
				});

				if (apiResponse.isSuccess() && apiResponse.isTrue()) {
					await this.openOtpVerificationModal();
				} else {
					throw new Error(
						apiResponse.message || "ไม่สามารถอัพเดทเบอร์โทรศัพท์ได้",
					);
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	/**
	 * Opens the OTP verification modal
	 */
	private async openOtpVerificationModal() {
		const modal = await this.modalController.create({
			component: OtpPhoneVerifyComponent,
			cssClass: "global-dialog-modal modal-blur-backdrop",
			backdropDismiss: false,
			keyboardClose: false,
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();
		if (data && data.success) {
			this.next();
		}
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage
				? `${errorMessage} : ${apiMessage}`
				: errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
