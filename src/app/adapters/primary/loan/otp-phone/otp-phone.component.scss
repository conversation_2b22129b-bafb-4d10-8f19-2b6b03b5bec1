.box-details {
    @apply rounded-xl w-full max-w-[750px] mx-auto;
}

// Responsive adjustments
@media (max-width: 768px) {
    .box-details {
        @apply max-w-full;
    }
}

// Phone number display
:host ::ng-deep {

    // Numpad styling for better mobile experience
    app-numpad-phone {
        @apply w-full;

        .numpad-container {
            @apply grid grid-cols-3 gap-2 sm:gap-3;

            .numpad-button {
                @apply h-14 sm:h-16 text-xl sm:text-2xl font-medium rounded-lg transition-all duration-200 active:scale-95;

                &:active {
                    @apply bg-blue-100;
                }
            }
        }
    }
}