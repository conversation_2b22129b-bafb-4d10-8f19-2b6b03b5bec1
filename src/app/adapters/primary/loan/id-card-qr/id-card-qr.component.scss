/* Header Styles */
.section-title {
	@apply mb-4 text-center text-2xl font-bold text-[#01508A];
}

.section-description {
	@apply mt-2 text-center text-sm text-gray-900;
}

/* QR Code Container */
.qr-container {
	@apply mx-auto mt-4 max-w-md rounded-lg border-2 border-gray-200 bg-white p-4;
}

.qr-header {
	@apply text-center;
}

.qr-header .qr-header-title {
	@apply mb-4 text-2xl font-medium text-[#01508A];
}

/* QR Code */
.qr-code-wrapper {
	@apply relative mx-auto h-48 w-48;
}

.qr-image {
	@apply h-full w-full object-contain;
}

.qr-logo-overlay {
	@apply pointer-events-none absolute inset-0 flex items-center justify-center;
}

.qr-logo {
	@apply h-12 w-12 opacity-50;
}

/* QR Info */
.qr-info {
	@apply mx-auto mt-4 grid max-w-[250px] grid-cols-2 gap-2;
}

.qr-info-label {
	@apply text-center text-base text-gray-500;
}

.qr-info-value {
	@apply text-center text-lg text-gray-900;
}

/* Timer */
.qr-timer {
	@apply mt-4 text-center text-base text-gray-900;
}
