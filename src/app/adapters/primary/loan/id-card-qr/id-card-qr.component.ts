import { CommonModule } from "@angular/common";
import { Component, computed, inject, signal } from "@angular/core";
import { IonicModule } from "@ionic/angular";
import { addIcons } from "ionicons";
import { cameraOutline, scanOutline } from "ionicons/icons";
import { ButtonModule } from "primeng/button";
import { ModalService } from "src/app/core/application/workflow/modal.service";
import { WorkflowModalService } from "src/app/core/application/workflow/workflow-modal.service";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-id-card-qr-modal",
	standalone: true,
	imports: [
		CommonModule,
		IonicModule,
		ButtonModule,
		HeaderMiniComponent,
		FooterComponent,
	],
	styleUrl: "./id-card-qr.component.scss",
	template: `
		<ion-content>
			<div class="app-layout">
				<!-- Header -->
				<app-header-mini [mode]="'workflow'"></app-header-mini>

				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper">
						<!-- Header Section -->
						<div class="text-center">
							<p class="page-title">อ่านบัตรประชาชนลูกค้า</p>
							<p
								class="page-title-description-center !text-base !font-normal !text-gray-500">
								กดปุ่ม "ดำเนินการต่อ" เพื่อเปิดแอปพลิเคชัน Star Money Dip Chip
								<!-- {{ getFieldValue("appName", "Star Money Dip Chip") }} -->
								<br />
								หรือ
								<br />
								ทำการบันทึกรูปภาพคิวอาร์โค้ดเพื่อใช้แทนในกรณีที่ Deep link
								ไม่ทำงาน
							</p>
						</div>

						<!-- QR Code Container -->
						<div class="qr-container">
							<div class="qr-header">
								<p class="qr-header-title">STAR MONEY SMART SALE</p>
							</div>

							<!-- QR Code -->
							<div class="qr-code-wrapper">
								<img
									src="assets/images/qrcode.png"
									alt="QR Code"
									class="qr-image" />
							</div>

							<!-- QR Info -->
							<div class="qr-info">
								<div class="qr-info-label">วันที่ทำรายการ:</div>
								<div class="qr-info-label">รหัสอ้างอิง:</div>
								<div class="qr-info-value">{{ currentDate }}</div>
								<div class="qr-info-value">{{ uniqueCode }}</div>
							</div>

							<!-- Timer -->
							<div class="qr-timer">
								คิวอาร์โค้ดนี้จะหมดอายุใน {{ remainingTime() }} นาที
							</div>
						</div>
					</div>
				</div>

				<!-- Footer -->
				<!-- Footer -->
				<app-footer>
					<div class="footer-wrapper">
						<p-button
							label="บันทึกคิวอาร์โค้ด"
							styleClass="w-full"
							[outlined]="true"
							(onClick)="saveQRCode()" />
						<p-button
							label="อ่านบัตรประชาชน"
							styleClass="w-full"
							(onClick)="presentCheckIdCardModal()" />
					</div>
				</app-footer>
			</div>
		</ion-content>
	`,
})
export class IdCardQrComponent extends WorkflowStepBaseComponent {
	private modalService = inject(ModalService);
	isReading: boolean = false;
	isSuccess: boolean = false;
	statusMessage: string = "อ่านประชาชน";
	statusDescription: string = 'กดปุ่ม "อ่านประชาชน" เ่่มอ่านข้อมูล';

	// State
	private readonly remainingTimeState = signal<number>(5);
	protected readonly remainingTime = computed(() => this.remainingTimeState());

	// Properties
	uniqueCode = "UNIQUECODE";
	currentDate = new Date().toLocaleDateString("th-TH");

	idCardData = {
		fullName: "",
		idNumber: "",
		birthDate: "",
		address: "",
	};

	constructor() {
		super();
		addIcons({ scanOutline, cameraOutline });
	}

	readIdCard() {
		this.isReading = true;
		this.statusMessage = "อ่านข้อมูล...";
		this.statusDescription = "โปรดรอ";

		// Simulate reading ID card
		setTimeout(() => {
			this.isReading = false;
			this.isSuccess = true;
			this.statusMessage = "อ่านข้อมูลสำเร็จ";
			this.statusDescription = "ข้อมูลประชาชนอ่านอยู่แล้ว";

			// Simulate ID card data
			this.idCardData = {
				fullName: "นายสมชาย ใจ",
				idNumber: "1-2345-67890-12-3",
				birthDate: "1 มกราคม 2530",
				address: "123 ถนนอมรท แขวงคลองเตย เขตคลองเตย มหาฯ 10110",
			};
		}, 2000);
	}

	/**
	 * Saves the QR code as an image
	 */
	saveQRCode() {
		// Implementation for saving QR code
		console.log("Saving QR code...");
	}

	async presentCheckIdCardModal() {
		console.log("Opening Check ID Card Modal");
		try {
			// Import the CheckIdCardComponent dynamically
			const { CheckIdCardComponent } = await import(
				"../check-id-card/check-id-card.component"
			);

			console.log("CheckIdCardComponent imported:", CheckIdCardComponent);

			// สร้าง event modal ตรวจสอบบัตรประชาชน
			const checkIdCardModal = await this.modalService.openGeneralModal(
				{
					component: CheckIdCardComponent,
					componentProps: { value: 123 },
				},
				"global-dialog-modal",
			);

			console.log("Modal created successfully:", checkIdCardModal);

			// รอให้ modal ปิดและผลลัพธ์
			const { data, role } = await checkIdCardModal.onDidDismiss();
			console.log("Check Id Card Result:", { data, role });

			// ถ้าการตรวจสอบบัตรประชาชนสำเร็จ ให้ไป ขั้นตอนถัดไป
			if (role === "completed") {
				// Update workflow data with ID card data
				if (data) {
					this.idCardData = data;
				}

				// Navigate to next step after a short delay
				this.onNext();
			}
		} catch (error) {
			console.error("Error opening Check ID Card Modal:", error);
		}
	}

	onNext() {
		this.next({ idCardData: this.idCardData });
	}

	/**
	 * Override handleNext to add custom behavior
	 */
	handleNext(): void {
		// You can add custom behavior here before calling super
		console.log("Next button clicked in ID Card QR component");
		super.next();
	}

	handleBack(): void {
		console.log("Back button clicked in ID Card QR component");
		super.back();
	}
}
