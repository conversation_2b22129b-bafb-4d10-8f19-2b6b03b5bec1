import { CommonModule } from "@angular/common";
import { HttpErrorResponse } from "@angular/common/http";
import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	computed,
	ElementRef,
	HostListener,
	inject,
	OnInit,
	signal,
	ViewChild,
} from "@angular/core";
import { FormsModule } from "@angular/forms";
import { <PERSON>Sani<PERSON>zer, SafeHtml } from "@angular/platform-browser";
import { ModalController } from "@ionic/angular/standalone";
import { ButtonModule } from "primeng/button";
import { CheckboxModule } from "primeng/checkbox";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { StarmoneyService } from "src/app/core/application/starmoney.service";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { OtpPhoneVerifyComponent } from "../../shared/components/otp-phone-verify/otp-phone-verify.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-terms",
	styleUrls: ["./terms.component.scss"],
	standalone: true,
	imports: [
		CommonModule,
		FormsModule,
		HeaderMiniComponent,
		FooterComponent,
		ButtonModule,
		CheckboxModule,
	],
	changeDetection: ChangeDetectionStrategy.OnPush,
	template: `
		<div class="app-layout">
			<!-- Header -->
			<app-header-mini [mode]="'workflow'"></app-header-mini>

			<!-- Content -->
			<div class="app-content">
				<div class="content-wrapper">
					<h1 class="page-title" [innerHTML]="termAndConsent?.title"></h1>
					<div class="terms-content" #termsContent (scroll)="onScroll($event)">
						<div [innerHTML]="sanitizedContent"></div>
					</div>
				</div>
			</div>

			<!-- Footer -->
			<app-footer
				styleClass="footer-wrapper max-w-md sm:max-w-2xl mx-auto !justify-center !items-center !border-none">
				<div class="flex w-full flex-col gap-4 p-4">
					<div *ngIf="hasReachedBottom" class="checkbox-container">
						<p-checkbox
							[binary]="true"
							[ngModel]="isAccepted()"
							inputId="accept-terms"
							[disabled]="hasOverflow && !hasReachedBottom"
							(ngModelChange)="setAccepted($event)"
							(onChange)="onAcceptChange()"></p-checkbox>
						<label for="accept-terms" class="checkbox-label">
							ข้าพเจ้าได้อ่าน เข้าใจ และยินยอมตามรายละเอียดทั้งหมด ข้างต้นแล้ว
						</label>
					</div>

					<div *ngIf="hasReachedBottom" class="flex flex-row justify-center gap-4">
						<p-button
							label="ปฏิเสธ"
							styleClass="w-full"
							class="w-full"
							[outlined]="true"
							(onClick)="back()"></p-button>
						<p-button
							label="รับทราบและดำเนินการต่อ"
							styleClass="w-full"
							class="w-full"
							[disabled]="!isAccepted()"
							[loading]="isLoading()"
							(onClick)="onAcceptTerms()"></p-button>
					</div>

					<p-button
						*ngIf="hasOverflow && !hasReachedBottom"
						label="เลื่อนลงเพื่ออ่านต่อ"
						icon="pi pi-arrow-down"
						[outlined]="true"
						styleClass="w-full"
						(onClick)="scrollToBottom()"></p-button>
				</div>
			</app-footer>
		</div>
	`,
})
export class TermsComponent extends WorkflowStepBaseComponent implements OnInit {
	private starmoneyService = inject(StarmoneyService);
	private appUtilsService = inject(AppUtilsService);
	private sanitizer = inject(DomSanitizer);
	private cdr = inject(ChangeDetectorRef);
	private modalController = inject(ModalController);

	@ViewChild("termsContent") termsContent!: ElementRef;
	protected readonly isAcceptedState = signal<boolean>(false);
	protected readonly isAccepted = computed(() => this.isAcceptedState());

	hasReachedBottom = false;
	hasOverflow = false;
	isModal = false;
	code: string | null = null;
	termAndConsent: any | null = null;
	sanitizedContent: SafeHtml | null = null;

	constructor() {
		super();
	}

	ngOnInit(): void {
		// this.isModal = this.getFieldValue("isModal", false);
		this.code = this.stepData.meta?.["code"] || null;
		this.loadTermConsent();
	}

	ngAfterViewInit(): void {
		// ตรวจสอบว่ามี overflow หรือไม่
		setTimeout(() => {
			this.checkOverflow();
			this.cdr.detectChanges();
		}, 300);
	}

	private async loadTermConsent(): Promise<void> {
		await this.runWithLoading(async () => {
			try {
				if (!this.code) {
					throw new Error("Transaction ID or Term Code is undefined");
				}

				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.getTermAndConsent({
					txn_id: txnId,
					code: this.code,
					lang: "th",
				});

				if (apiResponse.isSuccess() && apiResponse.hasData()) {
					this.termAndConsent = apiResponse.data;
					this.sanitizedContent = this.sanitizer.bypassSecurityTrustHtml(
						this.termAndConsent.content,
					);
					// รอให้ content render เสร็จแล้วค่อย setup scroll
					setTimeout(() => this.checkOverflow(), 300);
				} else {
					throw new Error(apiResponse.message || "Failed to fetch term and consent");
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	onAcceptChange(): void {
		this.isAcceptedState.set(this.isAccepted());
	}

	setAccepted(value: boolean): void {
		this.isAcceptedState.set(value);
	}

	async onAcceptTerms(): Promise<void> {
		await this.runWithLoading(async () => {
			try {
				if (!this.isAccepted()) {
					await this.appUtilsService.showError("กรุณายอมรับข้อตกลง", "กรุณายอมรับข้อตกลง");
					return;
				}

				if (!this.code) {
					throw new Error("ไม่พบ Transaction ID หรือ Term Code");
				}

				const txnId = this.getTxnIdOrError();
				const apiResponse = await this.starmoneyService.acceptTerm({
					txn_id: txnId,
					code: this.code,
					is_accepted: this.isAccepted(),
				});

				if (apiResponse.isSuccess() && apiResponse.isTrue()) {
					// Mark current step (4.2) as completed first
					this.workflowModalService.markStepCompleted(this.stepId);

					// Check if this is the step that requires OTP verification
					if (this.code === "consent_disclosure_info_via_internet") {
						await this.openOtpVerificationModal();
					} else {
						this.next();
					}
				}
			} catch (error) {
				await this.handleError(error);
			}
		});
	}

	/**
	 * Opens the OTP verification modal for step 4.3
	 */
	private async openOtpVerificationModal(): Promise<void> {
		// Temporarily update current step to 4.3 for stepper display
		const originalStepId = this.workflowModalService.currentStepId();

		// Use a custom method to update just the current step ID without triggering navigation
		(this.workflowModalService as any).state.update((state: any) => ({
			...state,
			currentStepId: "4.3",
		}));

		const modal = await this.modalController.create({
			component: OtpPhoneVerifyComponent,
			cssClass: "global-dialog-modal modal-blur-backdrop",
			backdropDismiss: false,
			keyboardClose: false,
			componentProps: {
				afterInfoViaInternet: true, // Flag to indicate this is after info via internet consent
			},
		});

		await modal.present();
		const { data } = await modal.onDidDismiss();

		if (data && data.success) {
			// Mark step 4.3 as completed
			this.workflowModalService.markStepCompleted("4.3");

			// Continue to next step if there is one
			this.next();
		} else {
			// If OTP was cancelled, restore original step
			(this.workflowModalService as any).state.update((state: any) => ({
				...state,
				currentStepId: originalStepId,
			}));
		}
	}

	/**
	 * =================================================
	 * การตรวจสอบว่าถึงด้านล่างแล้ว
	 * =================================================
	 */

	/**
	 * ตรวจสอบว่าเนื้อหามี overflow หรือไม่
	 */
	private checkOverflow(): void {
		const element = this.termsContent?.nativeElement;
		if (!element) return;
		this.hasOverflow = element.scrollHeight > element.clientHeight;
		if (!this.hasOverflow) {
			// ถ้าไม่มี overflow ให้ถือว่าถึงด้านล่างแล้ว
			this.hasReachedBottom = true;
		} else {
			// ตรวจสอบว่าอยู่ที่ด้านล่างหรือไม่
			this.checkIfReachedBottom(element);
		}
	}

	/**
	 * ตรวจสอบว่าเลื่อนถึงด้านล่างหรือไม่
	 */
	private checkIfReachedBottom(element: HTMLElement): void {
		const scrollPosition = element.scrollTop + element.clientHeight;
		const scrollHeight = element.scrollHeight;

		// ถือว่าถึงด้านล่างถ้าเหลือน้อยกว่า 20px
		this.hasReachedBottom = scrollHeight - scrollPosition < 20;
	}

	/**
	 * เลื่อนไปที่ด้านล่างสุดของเนื้อหา
	 */
	scrollToBottom(): void {
		const element = this.termsContent?.nativeElement;
		if (!element) return;

		element.scrollTo({
			top: element.scrollHeight,
			behavior: "smooth",
		});

		setTimeout(() => {
			this.hasReachedBottom = true;
			this.cdr.detectChanges();
		}, 500); // รอให้ scroll เสร็จ
	}

	@HostListener("window:resize")
	onResize(): void {
		this.checkOverflow();
	}

	onScroll(event: Event): void {
		if (!this.hasOverflow) return;
		const element = event.target as HTMLElement;
		this.checkIfReachedBottom(element);
		this.cdr.detectChanges();
	}

	/**
	 * Handles errors in a consistent way
	 * @param error The error to handle
	 * @returns Always returns false to indicate error handling is complete
	 */
	private async handleError(
		error: unknown,
		contextMessage: string = "An unexpected error occurred",
	): Promise<boolean> {
		let errorMessage = "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง";
		let logMessage = contextMessage;

		if (error instanceof HttpErrorResponse) {
			logMessage = `HTTP Error (${error.status}): ${error.message}`;

			const apiError = error.error?.error;
			const apiMessage = error.error?.message;

			errorMessage = apiError || errorMessage;
			errorMessage = apiMessage ? `${errorMessage} : ${apiMessage}` : errorMessage;

			console.error("API Error:", error.error);
		} else if (error instanceof Error) {
			logMessage = `Error: ${error.message}`;
			errorMessage = error.message;
			console.error(logMessage, error.stack);
		} else {
			logMessage = `${contextMessage}: ${String(error)}`;
			console.error(logMessage, error);
		}

		this.appUtilsService.showAlertDialog({
			header: "เกิดข้อผิดพลาด",
			message: errorMessage,
			acceptLabel: "ตกลง",
			acceptCallback: () => console.log("ตกลง"),
		});

		return false;
	}
}
