import { CommonModule } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { IonicModule } from "@ionic/angular";
import { ButtonModule } from "primeng/button";
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { HeaderMiniComponent } from "../../shared/components/header-mini/header-mini.component";
import { WorkflowStepBaseComponent } from "../../workflow/workflow-step-base.component";

@Component({
	selector: "app-loan-submission-confirmation",
	imports: [
		CommonModule,
		IonicModule,
		HeaderMiniComponent,
		FooterComponent,
		ButtonModule,
	],
	styleUrls: ["./loan-submission-confirmation.component.scss"],
	template: `
		<ion-content>
			<div class="app-layout">
				<!-- Header -->
				<app-header-mini mode="workflow"></app-header-mini>

				<!-- Content -->
				<div class="app-content">
					<div class="content-wrapper !justify-center">
						<!-- Header Section -->
						<div class="text-center">
							<h1 class="page-title">บันทึกข้อมูลใบสมัครสินเชื่อแล้ว</h1>
						</div>

						<div class="rounded-lg bg-white p-4">
							<div class="flex flex-col items-center">
								<!-- Dotted Border Area -->
								<img
									src="assets/svgs/send-tablet-to-customer.svg"
									alt="Send Tablet to Customer"
									class="mb-12 h-auto w-full max-w-56 object-contain" />

								<p class="mb-4 text-base text-gray-900">
									ส่งแท็บเล็ตให้ลูกค้าเพื่อดำเนินการยืนยันตัวตน
								</p>
								<p class="text-base text-gray-500">
									กรุณาให้คำแนะนำและช่วยเหลือลูกค้า
								</p>
								<p class="text-base text-gray-500">
									ระหว่างที่ลูกค้ากำลังดำเนินการสมัครอยู่
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- Footer -->
			<app-footer>
				<div class="footer-wrapper">
					<p-button
						label="ต่อไป"
						styleClass="w-full"
						[loading]="isLoading()"
						(onClick)="next()" />
				</div>
			</app-footer>
		</ion-content>
	`,
})
export class LoanSubmissionConfirmationComponent
	extends WorkflowStepBaseComponent
	implements OnInit
{
	ngOnInit() {}
}
