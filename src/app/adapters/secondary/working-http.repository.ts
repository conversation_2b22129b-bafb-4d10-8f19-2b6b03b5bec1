import { Injectable } from "@angular/core";
import {
	GetOccupationGroupRequestDto,
	GetOccupationGroupResponseDto,
} from "@domain/dto/working/get-occupation-group.dto";
import {
	GetOccupationRequestDto,
	GetOccupationResponseDto,
} from "@domain/dto/working/get-occupation.dto";
import {
	GetWorkingInfoRequestDto,
	GetWorkingInfoResponseDto,
} from "@domain/dto/working/get-working-info.dto";
import {
	UpdateWorkingInfoRequestDto,
	UpdateWorkingInfoResponseDto,
} from "@domain/dto/working/update-working-info.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { WorkingRepositoryPort } from "@ports/out/working-repository.port";
import { firstValueFrom } from "rxjs";
import { RestApiAdapter } from "./rest-api.adapter";

@Injectable({
	providedIn: "root",
})
export class WorkingHttpRepository
	extends RestApiAdapter
	implements WorkingRepositoryPort
{
	async getWorkingInfo(
		request: GetWorkingInfoRequestDto,
	): Promise<ApiResponse<GetWorkingInfoResponseDto>> {
		return await firstValueFrom(
			this.post<GetWorkingInfoResponseDto>(
				`/v1/onboarding/working/get`,
				request,
			),
		);
	}

	async updateWorkingInfo(
		request: UpdateWorkingInfoRequestDto,
	): Promise<ApiResponse<UpdateWorkingInfoResponseDto>> {
		return await firstValueFrom(
			this.post<UpdateWorkingInfoResponseDto>(
				`/v1/onboarding/working/update`,
				request,
			),
		);
	}
	async getOccupationGroups(
		request: GetOccupationGroupRequestDto,
	): Promise<ApiResponse<GetOccupationGroupResponseDto[]>> {
		return await firstValueFrom(
			this.get<GetOccupationGroupResponseDto[]>(
				`/v1/data/dic/occupation-group`,
				{ params: { ...request } },
			),
		);
	}
	getOccupations(
		request: GetOccupationRequestDto,
	): Promise<ApiResponse<GetOccupationResponseDto[]>> {
		return firstValueFrom(
			this.get<GetOccupationResponseDto[]>(`/v1/data/dic/occupation`, {
				params: { ...request },
			}),
		);
	}
}
