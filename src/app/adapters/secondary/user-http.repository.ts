import { Injectable } from "@angular/core";
import { TransactionIdDto } from "@domain/dto/transaction.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { UserRepositoryPort } from "@ports/out/user-repository.port";
import { firstValueFrom } from "rxjs";
import { RestApiAdapter } from "./rest-api.adapter";

/**
 * User HTTP repository
 * This repository implements the UserRepository interface and uses the HttpClient
 */
@Injectable({
	providedIn: "root",
})
export class UserHttpRepository
	extends RestApiAdapter
	implements UserRepositoryPort
{
	async signInAnonymous(request: TransactionIdDto): Promise<ApiResponse<any>> {
		const rawResponse = await firstValueFrom(
			this.post<ApiResponse<any>>(`/v1/auth/signin/anonymous`, request),
		);

		const response = ApiResponse.fromResponse(rawResponse);
		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: response.data,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}

	async refreshToken(request: TransactionIdDto): Promise<ApiResponse<any>> {
		const rawResponse = await firstValueFrom(
			this.post<ApiResponse<any>>(`/v1/auth/refresh-token`, request),
		);

		const response = ApiResponse.fromResponse(rawResponse);
		return ApiResponse.fromResponse({
			statusCode: response.statusCode,
			data: response.data,
			message: response.message,
			error: response.error,
			timestamp: response.timestamp,
		});
	}
}
