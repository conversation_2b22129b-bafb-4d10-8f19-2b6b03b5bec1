import { Injectable } from "@angular/core";
import { GetLoanCategoriesResponseDto } from "@domain/dto/loan/get-loan-categories.dto";
import {
	GetLoanTypeRequestDto,
	GetLoanTypeResponseDto,
} from "@domain/dto/loan/get-loan-type.dto";
import { SetLoanTypeRequestDto } from "@domain/dto/loan/set-loan-type.dto";
import {
	StartKycTransactionRequestDto,
	StartKycTransactionResponseDto,
} from "@domain/dto/loan/start-transaction.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { LoanRepositoryPort } from "@ports/out/loan-repository.port";
import { firstValueFrom } from "rxjs";
import { RestApiAdapter } from "./rest-api.adapter";
import {
	GetLoanTermRequestDto,
	GetLoanTermResponseDto,
} from "@domain/dto/loan/get-loan-term.dto";
import { LoanTerm } from "@domain/models/loan.model";

@Injectable({
	providedIn: "root",
})
export class LoanHttpRepository
	extends RestApiAdapter
	implements LoanRepositoryPort
{
	/**
	 * Start a KYC transaction
	 * @param request The transaction ID
	 * @returns Promise of an API response containing the transaction data
	 */
	async startKycTransaction(
		request: StartKycTransactionRequestDto,
	): Promise<ApiResponse<StartKycTransactionResponseDto>> {
		const rawResponse = await firstValueFrom(
			this.post<StartKycTransactionResponseDto>(
				`/v1/onboarding/start-kyc`,
				request,
			),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async getLoanCategories(): Promise<
		ApiResponse<GetLoanCategoriesResponseDto[]>
	> {
		const rawResponse = await firstValueFrom(
			this.get<GetLoanCategoriesResponseDto[]>(`/v1/data/dic/product`),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async setLoanType(
		request: SetLoanTypeRequestDto,
	): Promise<ApiResponse<boolean>> {
		const rawResponse = await firstValueFrom(
			this.post<boolean>(`/v1/onboarding/loan/set-loantype`, request),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async getLoanType(
		request: GetLoanTypeRequestDto,
	): Promise<ApiResponse<GetLoanTypeResponseDto>> {
		const rawResponse = await firstValueFrom(
			this.post<GetLoanTypeResponseDto>(
				`/v1/onboarding/loan/get-loantype`,
				request,
			),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async getLoanTerms(
		request: GetLoanTermRequestDto,
	): Promise<ApiResponse<GetLoanTermResponseDto>> {
		const rawResponse = await firstValueFrom(
			this.post<GetLoanTermResponseDto>(
				`/v1/onboarding/term-consent/get-term-consent`,
				request,
			),
		);

		return ApiResponse.fromResponse(rawResponse);
	}
}
