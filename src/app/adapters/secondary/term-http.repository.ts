import { Injectable } from "@angular/core";
import { AcceptTermsRequestDto } from "@domain/dto/terms/accept-term.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { TermRepositoryPort } from "@ports/out/term-repository.port";
import { firstValueFrom } from "rxjs";
import { RestApiAdapter } from "./rest-api.adapter";
import {
	GetTermRequestDto,
	GetTermResponseDto,
} from "@domain/dto/terms/get-term.dto";

@Injectable({
	providedIn: "root",
})
export class TermHttpRepository
	extends RestApiAdapter
	implements TermRepositoryPort
{
	async getTerm(
		request: GetTermRequestDto,
	): Promise<ApiResponse<GetTermResponseDto>> {
		return await firstValueFrom(
			this.post<GetTermResponseDto>(
				`/v1/onboarding/term-consent/get-term-consent`,
				request,
			),
		);
	}

	async acceptTerm(
		request: AcceptTermsRequestDto,
	): Promise<ApiResponse<boolean>> {
		return await firstValueFrom(
			this.post<boolean>(
				`/v1/onboarding/term-consent/accept-term-consent`,
				request,
			),
		);
	}
}
