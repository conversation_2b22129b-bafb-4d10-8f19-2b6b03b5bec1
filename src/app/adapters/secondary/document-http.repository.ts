import { Injectable } from "@angular/core";
import {
	GetUploadDocsRequestDto,
	GetUploadDocsResponseDto,
} from "@domain/dto/document/get-upload-docs.dto";
import {
	UploadDocRequestDto,
	UploadDocResponseDto,
} from "@domain/dto/document/upload-doc.dto";
import { GetProductListResponseDto } from "@domain/dto/products/get-product-list.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { DocumentRepositoryPort } from "@ports/out/document-repository.port";
import { firstValueFrom } from "rxjs";
import { RestApiAdapter } from "./rest-api.adapter";

@Injectable({
	providedIn: "root",
})
export class DocumentHttpRepository
	extends RestApiAdapter
	implements DocumentRepositoryPort
{
	async getUploadDocuments(
		request: GetUploadDocsRequestDto,
	): Promise<ApiResponse<GetUploadDocsResponseDto>> {
		return await firstValueFrom(
			this.post<GetProductListResponseDto[]>(
				"/v1/onboarding/document/get",
				request,
			),
		);
	}

	async uploadDocument(
		request: UploadDocRequestDto,
	): Promise<ApiResponse<UploadDocResponseDto>> {
		const formData = new FormData();
		formData.append("txn_id", request.txn_id);
		formData.append("document_type", request.document_type);
		formData.append("file", request.file);

		return await firstValueFrom(
			this.post<GetProductListResponseDto[]>(
				"/v1/onboarding/document/upload",
				formData,
			),
		);
	}
}
