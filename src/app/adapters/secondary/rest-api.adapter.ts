import {
	HttpClient,
	HttpErrorResponse,
	HttpHeaders,
	HttpParams,
} from "@angular/common/http";
import { Injectable, inject } from "@angular/core";
import { ApiResponse } from "@domain/models/api-response.model";
import { Observable, catchError, finalize, tap, throwError } from "rxjs";
import { AppUtilsService } from "src/app/core/application/app-utils.service";
import { environment } from "src/environments/environment";

type HttpMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

interface RequestOptions {
	params?: Record<string, any>;
	headers?: HttpHeaders;
	showToast?: boolean;
	showLoading?: boolean;
	loadingMessage?: string;
}

@Injectable({
	providedIn: "root",
})
export class RestApiAdapter {
	private readonly http = inject(HttpClient);
	private readonly appUtils = inject(AppUtilsService);
	private readonly baseUrl = environment.apiUrl;

	// Default loading message
	private readonly defaultLoadingMessage = "กำลังโหลดข้อมูล...";

	// Success messages by method
	private readonly successMessages: Record<HttpMethod, string> = {
		GET: "ดำเนินการสำเร็จ",
		POST: "ดำเนินการสำเร็จ",
		PUT: "อัปเดตข้อมูลสำเร็จ",
		PATCH: "อัปเดตข้อมูลสำเร็จ",
		DELETE: "ลบข้อมูลสำเร็จ",
	};

	/**
	 * Create HTTP params from an object
	 * @param params Parameters object
	 * @returns HttpParams object
	 */
	private createParams(params: Record<string, any> = {}): HttpParams {
		let httpParams = new HttpParams();

		Object.entries(params).forEach(([key, value]) => {
			if (value !== null && value !== undefined) {
				httpParams = httpParams.set(key, String(value));
			}
		});

		return httpParams;
	}

	/**
	 * Execute HTTP request with common handling
	 * @param method HTTP method
	 * @param endpoint API endpoint
	 * @param options Request options
	 * @param body Request body (for POST, PUT, PATCH)
	 * @returns Observable of response
	 */
	private request<T>(
		method: HttpMethod,
		endpoint: string,
		options: RequestOptions = {},
		body?: any,
	): Observable<ApiResponse<T>> {
		const {
			params = {},
			headers,
			showToast = false,
			showLoading = true,
			loadingMessage = this.defaultLoadingMessage,
		} = options;

		const url = `${this.baseUrl}${endpoint}`;

		if (showLoading) {
			this.appUtils.showLoading(loadingMessage);
		}

		const httpOptions = {
			params: this.createParams(params),
			headers,
		};

		let request$: Observable<ApiResponse<T>>;

		switch (method) {
			case "GET":
				request$ = this.http.get<ApiResponse<T>>(url, httpOptions);
				break;
			case "POST":
				request$ = this.http.post<ApiResponse<T>>(url, body, httpOptions);
				break;
			case "PUT":
				request$ = this.http.put<ApiResponse<T>>(url, body, httpOptions);
				break;
			case "PATCH":
				request$ = this.http.patch<ApiResponse<T>>(url, body, httpOptions);
				break;
			case "DELETE":
				request$ = this.http.delete<ApiResponse<T>>(url, httpOptions);
				break;
		}

		return request$.pipe(
			tap((response) => {
				if (showToast && response.statusCode === 200) {
					const successMessage =
						response.message || this.successMessages[method];
					this.appUtils.showRestApiDetail(
						"success",
						successMessage,
						response.statusCode,
						successMessage,
						method,
						url,
					);
				}
			}),
			catchError((error) => this.handleError(error, url, method, showToast)),
			finalize(() => {
				if (showLoading) {
					this.appUtils.hideLoading();
				}
			}),
		);
	}

	/**
	 * Make a GET request
	 * @param endpoint API endpoint
	 * @param options Request options
	 * @returns Observable of response
	 */
	public get<T>(
		endpoint: string,
		options: RequestOptions = {},
	): Observable<ApiResponse<T>> {
		return this.request<T>("GET", endpoint, options);
	}

	/**
	 * Make a POST request
	 * @param endpoint API endpoint
	 * @param body Request body
	 * @param options Request options
	 * @returns Observable of response
	 */
	public post<T>(
		endpoint: string,
		body: any,
		options: RequestOptions = {},
	): Observable<ApiResponse<T>> {
		return this.request<T>("POST", endpoint, options, body);
	}

	/**
	 * Make a PUT request
	 * @param endpoint API endpoint
	 * @param body Request body
	 * @param options Request options
	 * @returns Observable of response
	 */
	public put<T>(
		endpoint: string,
		body: any,
		options: RequestOptions = {},
	): Observable<ApiResponse<T>> {
		return this.request<T>("PUT", endpoint, options, body);
	}

	/**
	 * Make a PATCH request
	 * @param endpoint API endpoint
	 * @param body Request body
	 * @param options Request options
	 * @returns Observable of response
	 */
	public patch<T>(
		endpoint: string,
		body: any,
		options: RequestOptions = {},
	): Observable<ApiResponse<T>> {
		return this.request<T>("PATCH", endpoint, options, body);
	}

	/**
	 * Make a DELETE request
	 * @param endpoint API endpoint
	 * @param options Request options
	 * @returns Observable of response
	 */
	public delete<T>(
		endpoint: string,
		options: RequestOptions = {},
	): Observable<ApiResponse<T>> {
		return this.request<T>("DELETE", endpoint, options);
	}

	/**
	 * Handle HTTP errors
	 * @param error HTTP error
	 * @param showToast Whether to show toast notifications
	 * @returns Observable with error
	 */
	protected handleError(
		error: HttpErrorResponse,
		url: string,
		method: HttpMethod = "GET",
		showToast: boolean = true,
	): Observable<never> {
		console.error("API error:", error);

		let errorMessage = "เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ";
		let errorTitle = "เกิดข้อผิดพลาด";

		if (error.error && error.error.method) {
			method = error.error.method;
		} else if (error.url) {
			method = "GET";
		}

		if (error.error instanceof ErrorEvent) {
			// Client-side error
			errorMessage = `Error: ${error.error.message}`;
		} else {
			// Server-side error
			errorMessage =
				error.error?.message ||
				`Error Code: ${error.status}, Message: ${error.message}`;
		}

		if (showToast) {
			console.log("Showing API error toast:", {
				method,
				url,
				errorTitle,
				errorMessage,
			});
			this.appUtils.showRestApiDetail(
				"error",
				errorTitle,
				error.status,
				errorMessage,
				method,
				url,
			);
		}

		return throwError(() => error);
	}

	/**
	 * Make a request that automatically retries on failure
	 * @param method HTTP method function to call
	 * @param endpoint API endpoint
	 * @param body Request body (if applicable)
	 * @param retryCount Number of retries
	 * @param retryDelay Delay between retries in milliseconds
	 * @returns Observable of response
	 */
	public retryRequest<T>(
		method: "get" | "post" | "put" | "patch" | "delete",
		endpoint: string,
		body: any = null,
		options: RequestOptions = {},
		retryCount: number = 3,
		retryDelay: number = 1000,
	): Observable<ApiResponse<T>> {
		let attempt = 0;

		const execute = (): Observable<ApiResponse<T>> => {
			return this[method]<T>(endpoint, body, {
				...options,
				showToast: false,
			}).pipe(
				catchError((error) => {
					attempt++;
					if (attempt <= retryCount) {
						console.log(
							`Retrying request (${attempt}/${retryCount}) after ${retryDelay}ms...`,
						);
						// Wait for the specified delay and retry
						return new Observable<ApiResponse<T>>((observer) => {
							setTimeout(() => {
								execute().subscribe({
									next: (value) => observer.next(value),
									error: (err) => observer.error(err),
									complete: () => observer.complete(),
								});
							}, retryDelay);
						});
					}
					// If we've exhausted all retries, show the toast and throw the error
					if (options.showToast !== false) {
						this.handleError(
							error,
							`${this.baseUrl}${endpoint}`,
							method.toUpperCase() as HttpMethod,
							true,
						);
					}
					return throwError(() => error);
				}),
			);
		};

		return execute();
	}
}
