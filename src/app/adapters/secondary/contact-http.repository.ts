import { Injectable } from "@angular/core";
import {
	GetContactRequestDto,
	GetContactResponseDto,
} from "@domain/dto/contact/get-contact.dto";
import {
	SendOtpRequestDto,
	SendOtpResponseDto,
} from "@domain/dto/contact/send-otp.dto";
import { UpdateContactRequestDto } from "@domain/dto/contact/update-contact.dto";
import { VerifyOtpRequestDto } from "@domain/dto/contact/verify-otp.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { ContactRepositoryPort } from "@ports/out/contact-repository.port";
import { firstValueFrom } from "rxjs";
import { RestApiAdapter } from "./rest-api.adapter";

@Injectable({
	providedIn: "root",
})
export class ContactHttpRepository
	extends RestApiAdapter
	implements ContactRepositoryPort
{
	async updateContact(
		request: UpdateContactRequestDto,
	): Promise<ApiResponse<boolean>> {
		return await firstValueFrom(
			this.post<boolean>(`/v1/onboarding/contact/update`, request),
		);
	}

	async getContact(
		request: GetContactRequestDto,
	): Promise<ApiResponse<GetContactResponseDto>> {
		return await firstValueFrom(
			this.post<GetContactResponseDto>(`/v1/onboarding/contact/get`, request),
		);
	}

	async sendOtp(
		request: SendOtpRequestDto,
	): Promise<ApiResponse<SendOtpResponseDto>> {
		return await firstValueFrom(
			this.post<SendOtpResponseDto>(`/v1/onboarding/contact/send-otp`, request),
		);
	}

	async verifyOtp(request: VerifyOtpRequestDto): Promise<ApiResponse<boolean>> {
		return await firstValueFrom(
			this.post<boolean>(`/v1/onboarding/contact/verify-otp`, request),
		);
	}
}
