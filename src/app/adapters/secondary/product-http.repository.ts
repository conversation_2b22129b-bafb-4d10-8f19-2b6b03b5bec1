import { Injectable } from "@angular/core";
import {
	GetOccupationGroupRequestDto,
	GetOccupationGroupResponseDto,
} from "@domain/dto/working/get-occupation-group.dto";
import {
	GetOccupationRequestDto,
	GetOccupationResponseDto,
} from "@domain/dto/working/get-occupation.dto";
import {
	GetWorkingInfoRequestDto,
	GetWorkingInfoResponseDto,
} from "@domain/dto/working/get-working-info.dto";
import {
	UpdateWorkingInfoRequestDto,
	UpdateWorkingInfoResponseDto,
} from "@domain/dto/working/update-working-info.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { ProductRepositoryPort } from "@ports/out/product-repository.port";
import { firstValueFrom } from "rxjs";
import { RestApiAdapter } from "./rest-api.adapter";
import { ConfirmProductRequestDto } from "@domain/dto/products/confirm-product.dto";
import {
	GetProductListRequestDto,
	GetProductListResponseDto,
} from "@domain/dto/products/get-product-list.dto";
import {
	GetProductRequestDto,
	GetProductResponseDto,
} from "@domain/dto/products/get-product.dto";
import { SetProductRequestDto } from "@domain/dto/products/set-product.dto";

@Injectable({
	providedIn: "root",
})
export class ProductHttpRepository
	extends RestApiAdapter
	implements ProductRepositoryPort
{
	async getProductList(
		request: GetProductListRequestDto,
	): Promise<ApiResponse<GetProductListResponseDto[]>> {
		return await firstValueFrom(
			this.post<GetProductListResponseDto[]>(
				`/v1/onboarding/loan/product/get-list`,
				request,
			),
		);
	}

	async setProduct(
		request: SetProductRequestDto,
	): Promise<ApiResponse<boolean>> {
		return await firstValueFrom(
			this.post<boolean>(`/v1/onboarding/loan/product/set-product`, request),
		);
	}

	async getProduct(
		request: GetProductRequestDto,
	): Promise<ApiResponse<GetProductResponseDto>> {
		return await firstValueFrom(
			this.post<GetProductResponseDto>(
				`/v1/onboarding/loan/product/get-product`,
				request,
			),
		);
	}

	async confirmProduct(
		request: ConfirmProductRequestDto,
	): Promise<ApiResponse<any>> {
		return await firstValueFrom(
			this.post<any>(`/v1/onboarding/loan/product/confirm-product`, request),
		);
	}
}
