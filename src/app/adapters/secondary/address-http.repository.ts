import { Injectable } from "@angular/core";
import {
	GetAddressInfoRequestDto,
	GetAddressInfoResponseDto,
} from "@domain/dto/address/get-address-info.dto";
import { ApiResponse } from "@domain/models/api-response.model";
import { AddressRepositoryPort } from "@ports/out/address-repository.port";
import { firstValueFrom } from "rxjs";
import { RestApiAdapter } from "./rest-api.adapter";
import {
	GetDistrictRequestDto,
	GetDistrictResponseDto,
} from "@domain/dto/address/get-district.dto";
import {
	GetHousingTypeRequestDto,
	GetHousingTypeResponseDto,
} from "@domain/dto/address/get-housing-type.dto";
import {
	GetPostcodeRequestDto,
	GetPostcodeResponseDto,
} from "@domain/dto/address/get-postcode.dto";
import {
	GetProvinceRequestDto,
	GetProvinceResponseDto,
} from "@domain/dto/address/get-province.dto";
import {
	GetSubDistrictRequestDto,
	GetSubDistrictResponseDto,
} from "@domain/dto/address/get-sub-district.dto";
import { UpdateAddressInfoRequestDto } from "@domain/dto/address/update-address-info.dto";
import { UpdateHousingTypeRequest } from "@domain/dto/address/update-housing-type.dto";

/**
 * Address HTTP repository
 * This repository implements the AddressRepositoryPort interface and uses the HttpClient
 * to interact with the address-related API endpoints
 */
@Injectable({
	providedIn: "root",
})
export class AddressHttpRepository
	extends RestApiAdapter
	implements AddressRepositoryPort
{
	async getAddressInfo(
		request: GetAddressInfoRequestDto,
	): Promise<ApiResponse<GetAddressInfoResponseDto[]>> {
		const rawResponse = await firstValueFrom(
			this.post<GetAddressInfoResponseDto[]>(
				`/v1/onboarding/address/get`,
				request,
			),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async getProvinces(
		request: GetProvinceRequestDto,
	): Promise<ApiResponse<GetProvinceResponseDto[]>> {
		const rawResponse = await firstValueFrom(
			this.get<GetProvinceResponseDto[]>(`/v1/data/dic/province`, {
				params: { ...request },
			}),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async getDistricts(
		request: GetDistrictRequestDto,
	): Promise<ApiResponse<GetDistrictResponseDto[]>> {
		const rawResponse = await firstValueFrom(
			this.get<GetDistrictResponseDto[]>(`/v1/data/dic/district`, {
				params: { ...request },
			}),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async getSubDistricts(
		request: GetSubDistrictRequestDto,
	): Promise<ApiResponse<GetSubDistrictResponseDto[]>> {
		const rawResponse = await firstValueFrom(
			this.get<GetSubDistrictResponseDto[]>(`/v1/data/dic/sub-district`, {
				params: { ...request },
			}),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async getPostalCodes(
		request: GetPostcodeRequestDto,
	): Promise<ApiResponse<GetPostcodeResponseDto[]>> {
		const rawResponse = await firstValueFrom(
			this.get<GetPostcodeResponseDto[]>(`/v1/data/dic/postcode`, {
				params: { ...request },
			}),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async getHousingTypes(
		request: GetHousingTypeRequestDto,
	): Promise<ApiResponse<GetHousingTypeResponseDto[]>> {
		const rawResponse = await firstValueFrom(
			this.get<GetHousingTypeResponseDto[]>(`/v1/data/dic/housing-type`, {
				params: { ...request },
			}),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async updateAddressInfo(
		request: UpdateAddressInfoRequestDto,
	): Promise<ApiResponse<GetAddressInfoResponseDto>> {
		const rawResponse = await firstValueFrom(
			this.post<GetAddressInfoResponseDto>(
				`/v1/onboarding/address/update`,
				request,
			),
		);

		return ApiResponse.fromResponse(rawResponse);
	}

	async updateHousingType(
		request: UpdateHousingTypeRequest,
	): Promise<ApiResponse<GetHousingTypeResponseDto>> {
		const rawResponse = await firstValueFrom(
			this.post<GetHousingTypeResponseDto>(
				`/v1/onboarding/address/update-contact`,
				request,
			),
		);

		return ApiResponse.fromResponse(rawResponse);
	}
}
