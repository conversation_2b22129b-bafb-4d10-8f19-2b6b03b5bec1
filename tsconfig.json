/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
	"compileOnSave": false,
	"compilerOptions": {
		"baseUrl": "./",
		"outDir": "./dist/out-tsc",
		"forceConsistentCasingInFileNames": true,
		"esModuleInterop": true,
		"strict": true,
		"noImplicitOverride": true,
		"noPropertyAccessFromIndexSignature": true,
		"noImplicitReturns": true,
		"noFallthroughCasesInSwitch": true,
		"sourceMap": true,
		"declaration": false,
		"experimentalDecorators": true,
		"moduleResolution": "node",
		"importHelpers": true,
		"target": "es2022",
		"module": "es2020",
		"lib": ["ES2021", "dom"],
		"useDefineForClassFields": false,
		"paths": {
			"@domain/*": ["src/app/core/domain/*"],
			"@ports/*": ["src/app/core/ports/*"]
		}
	},
	"angularCompilerOptions": {
		"enableI18nLegacyMessageIdFormat": false,
		"strictInjectionParameters": true,
		"strictInputAccessModifiers": true,
		"strictTemplates": true
	}
}
